package com.nexla.probe.sql.connection;

import com.nexla.common.ConnectionType;
import com.nexla.connect.common.connector.telemetry.BaseConnectorTelemetryReporter;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import java.util.Optional;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

public class SingleNexlaJDBCConnectionFactory implements NexlaJDBCConnectionFactory {

    private static final Logger logger = LoggerFactory.getLogger(SingleNexlaJDBCConnectionFactory.class);

    @Override
    @SneakyThrows
    public Connection createConnection(String url, Properties properties) {
        logger.info("Creating connection, url: {}",  url);
        Connection connection = DriverManager.getConnection(url, properties);
        Optional<? extends BaseConnectorTelemetryReporter> reporter = ConnectorTelemetryReporter.getInstanceByCurrentThread();
        reporter.ifPresent(rep -> {
            if (rep instanceof ConnectorTelemetryReporter) {
                ((ConnectorTelemetryReporter) rep).reportConnectionOpened(url);
            }
        });
        return connection;
    }

    @Override
    public void close() {

    }
}
