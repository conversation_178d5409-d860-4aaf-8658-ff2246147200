package com.nexla.probe.sql.connection;

import com.nexla.common.ConnectionType;
import com.nexla.common.RSAKeyUtils;
import com.nexla.common.exception.NexlaExceptionChecker;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import connect.jdbc.sink.dialect.DbDialect;
import lombok.SneakyThrows;

import javax.net.ssl.SSLHandshakeException;
import java.sql.Connection;
import java.sql.SQLFeatureNotSupportedException;
import java.sql.SQLSyntaxErrorException;
import java.util.Optional;
import java.util.Properties;

import static com.nexla.probe.sql.connection.ConnectionHelper.addSchemaToConnection;

public class PooledConnection extends ConnectionStrategy {

	private final NexlaJDBCConnectionFactory connectionFactory;

	public PooledConnection() {
		this.connectionFactory = new PooledNexlaJDBCConnectionFactory();
	}

	@Override
	public Connection getConnection(JdbcAuthConfig authConfig) {
		try {
			return createConnection(authConfig, ConnectionTlsStrategy.DefaultTls);
		} catch (Exception e) {
			if (ConnectionType.MYSQL.equals(authConfig.dbType) && NexlaExceptionChecker.checkParentException(e, SSLHandshakeException.class)) {
				return createConnection(authConfig, ConnectionTlsStrategy.Tls12);

			} else if (ConnectionType.ORACLE_AUTONOMOUS.equals(authConfig.dbType) && NexlaExceptionChecker.checkParentException(e, SQLSyntaxErrorException.class)) {
				if (e.getMessage().contains("ORA-20001")) {
					// get the current service
					String currentOracleService = authConfig.oracleServiceName;
					Optional<String> nextPossibleOracleService = ConnectionHelper.getNextAllowedOracleService(currentOracleService);

					if (nextPossibleOracleService.isEmpty()) {
						logger.error("Connection to Oracle ADW using service name {} wasn't successful.", currentOracleService, e);
						throw e;
					}
					try {
						// otherwise proceed
						logger.warn("Not allowed to connect on {} service, trying next suggested one - {}.", currentOracleService, nextPossibleOracleService.get());
						authConfig.oracleServiceName = nextPossibleOracleService.get();
						authConfig.url = alignWithChosenOracleAdwService(authConfig.url, currentOracleService, nextPossibleOracleService.get());
						return createConnection(authConfig, ConnectionTlsStrategy.DefaultTls);
					} catch (Exception e2) {
						if (e2.getMessage().contains("ORA-20001")) {
							// not allowed on previous tier, last resort
							String currentlyUsedOracleService = authConfig.oracleServiceName;
							Optional<String> finalPossibleOracleService = ConnectionHelper.getNextAllowedOracleService(currentlyUsedOracleService);
							if (finalPossibleOracleService.isPresent()) {
								logger.warn("Not allowed to connect on {} service, trying last resort - {} service.", currentlyUsedOracleService, finalPossibleOracleService.get());
								authConfig.oracleServiceName = nextPossibleOracleService.get();
								authConfig.url = alignWithChosenOracleAdwService(authConfig.url, currentlyUsedOracleService,  finalPossibleOracleService.get());
								return createConnection(authConfig, ConnectionTlsStrategy.DefaultTls);
							} else {
								throw e2;
							}
						} else {
							throw e2;
						}
					}
				} else {
					throw e;
				}
			} else {
				throw e;
			}
		}
	}

	@Override
	public void disconnect() {
		this.connectionFactory.close();
	}

	private String alignWithChosenOracleAdwService(String url, String previousOracleService, String currentOracleService) {
		return url.replaceFirst(previousOracleService, currentOracleService);
	}

	@SneakyThrows
	private Connection createConnection(JdbcAuthConfig config, ConnectionTlsStrategy tlsStrategy) {
		Properties properties = getProperties(config);
		String url = getUrl(config, tlsStrategy);

		Connection connection = this.connectionFactory.createConnection(url, properties);
		if (config.dbType.isSupportingSchema()) {
			addSchemaToConnection(config, connection);
		}

		DbDialect dbDialect = DbDialect.fromConnectionString(config.dbType);
		if (!dbDialect.isAutoCommit()) {
			try {
				connection.setAutoCommit(false);
			} catch (SQLFeatureNotSupportedException e) {
				// do nothing, spark simba driver used with Databricks does not support this feature
			}
		}
		return connection;
	}
}
