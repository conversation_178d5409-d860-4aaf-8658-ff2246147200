package connect.jdbc.sink.dialect;

import com.google.common.base.Supplier;
import com.google.common.collect.Sets;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.tracker.Tracker;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Date;
import connect.data.*;
import connect.jdbc.sink.dialect.copy.*;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.sink.dialect.type.SnowflakeType;
import connect.jdbc.sink.warehouse.DataWarehouseSink;
import connect.jdbc.sink.warehouse.SnowflakeDataWarehouseSink;
import connect.jdbc.util.WarehouseUtils;
import lombok.SneakyThrows;
import net.snowflake.client.core.SFSession;
import net.snowflake.client.jdbc.SnowflakeConnectionV1;
import one.util.streamex.StreamEx;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.*;
import static connect.jdbc.sink.dialect.DialectFeature.COPY_INSERT;
import static connect.jdbc.sink.dialect.DialectFeature.COPY_UPSERT;
import static connect.jdbc.sink.dialect.StringBuilderUtil.joinToBuilder;
import static java.util.Optional.of;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

public class SnowflakeSqlDialect extends RedshiftSqlDialect {

	private static final EnumSet<DialectFeature> DIALECT_FEATURES = EnumSet.of(COPY_INSERT, COPY_UPSERT);

	private static final String SCHEMA_PARAMETER = "schema";
	private static final String WAREHOUSE_PARAMETER = "warehouse";
	private static final String DEFAULT_SCHEMA_NAME = "PUBLIC";

	public SnowflakeSqlDialect() {
		super("\"", "\"", of(SCHEMA_PARAMETER), of(WAREHOUSE_PARAMETER), of(DEFAULT_SCHEMA_NAME));
	}

	@Override
	public Set<WarehouseCopyFileFormat> sourceFileFormats() {
		return Sets.newHashSet(CSV, JSON, CSV_GZIP, JSON_GZIP, CSV_COMMA, CSV_COMMA_GZIP);
	}

	@Override
	public Set<WarehouseCopyFileFormat> sinkFileFormats() {
		return Sets.newHashSet(JSON, JSON_GZIP, CSV, CSV_GZIP, CSV_COMMA, CSV_COMMA_GZIP, CSV_BAR, CSV_BAR_GZIP);
	}

	@Override
	public Optional<WarehouseCopyFileFormat> defaultSourceFileFormat() {
		return Optional.of(CSV_GZIP);
	}

	@Override
	public Optional<WarehouseCopyFileFormat> defaultSinkFileFormat() {
		return Optional.of(CSV_GZIP);
	}

	public StreamEx<String> getSqlTypes() {
		return StreamEx
			.of(SnowflakeType.values())
			.map(x -> x.type);
	}

	@Override
	public Schema.Type getSchemaTypeByDbType(String dbType) {
		var availableTypes = Sets.newHashSet(SnowflakeType.values())
				.stream()
				.map(Enum::name)
				.collect(Collectors.toSet());

		if (!availableTypes.contains(dbType)){
			return null;
		}

		var type = SnowflakeType.valueOf(dbType);
		switch (type) {
			case TIMESTAMP:
			case BIGINT:
				return Schema.Type.INT64;
			case DOUBLE:
				return Schema.Type.FLOAT64;
			case BOOLEAN:
				return Schema.Type.BOOLEAN;
			case TEXT:
				return Schema.Type.STRING;
			case BINARY:
				return Schema.Type.BYTES;
			case DATE:
				return Schema.Type.INT32;
			default:
				return null;
		}
	}

	public String getDbType(Schema.Type type) {
		switch (type) {
			case INT8:
			case INT16:
			case INT32:
			case INT64:
				return SnowflakeType.BIGINT.type;
			case FLOAT32:
			case FLOAT64:
				return SnowflakeType.DOUBLE.type;
			case BOOLEAN:
				return SnowflakeType.BOOLEAN.type;
			case STRING:
				return SnowflakeType.TEXT.type;
			case BYTES:
				return SnowflakeType.BINARY.type;
			default:
				return null;
		}
	}

	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				return SnowflakeType.DECIMAL.type;
			case Date.LOGICAL_NAME:
				return SnowflakeType.DATE.type;
			case Time.LOGICAL_NAME:
				return SnowflakeType.TIME.type;
			case Timestamp.LOGICAL_NAME:
				return SnowflakeType.TIMESTAMP.type;
			default:
				return null;
		}
	}

	@SneakyThrows
	@Override
	public NexlaDbInfo extractDbInfo(Connection connection, String url, Set<String> notUsed, boolean isSupportingSchema) {
		SnowflakeConnectionV1 snowflakeConnectionV1 = (SnowflakeConnectionV1) connection;
		SFSession sfSession = snowflakeConnectionV1.getSfSession();
		return new NexlaDbInfo(sfSession.getDatabase(), Sets.newHashSet(sfSession.getSchema()));
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return DIALECT_FEATURES;
	}

	@Override
	public SinkCopyOperation newSinkCopyOperation(JdbcSinkConnectorConfig config) {
		if (config.directUpload) {
			return new SinkSnowflakeDirectCopyOperation();
		} else {
			return new SinkSnowflakeCopyOperation(WarehouseUtils.getCopyOperationTempStorage(config));
		}
	}

	public boolean isAutoCommit() {
		return true;
	}

	@Override
	public SourceCopyOperation newSourceCopyOperation(JdbcSourceConnectorConfig config,
	                                                  WarehouseCopyFileFormat fileFormat,
	                                                  Logger logger,
	                                                  boolean isReplicationContext,
													  WarehouseCopyTempStorage storage,
													  FlowType flowType) {
		if (config.directUnload && isReplicationContext) {
			return new SourceSnowflakeDirectCopyOperation(config, fileFormat, logger, storage, flowType);
		} else {
			return new SourceSnowflakeCopyOperation(config, fileFormat, logger, storage, flowType);
		}
	}
	@Override
	public DataWarehouseSink newDataWarehouseSink(JdbcSinkConnectorConfig config,
	                                              WarehouseCopyFileFormat fileFormat,
	                                              Schema schema,
	                                              DbDialect dbDialect,
	                                              NexlaLogger logger) {
		return new SnowflakeDataWarehouseSink(new SinkCopyOperationCommon(config, fileFormat, schema, dbDialect, logger));
	}

	@Override
	public List<String> getAlterModifySqls(
			String tableName,
			String nonKeyCol,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig) {

		String sqlType = mappingConfig
				.getMapping()
				.values()
				.stream()
				.filter(map -> map.containsKey(nonKeyCol))
				.findFirst()
				.map(e -> e.get(nonKeyCol))
				.filter(t -> !t.equals(DEFAULT_MAPPING))
				.orElse(automaticBinding.get().getSqlType(nonKeyCol, this));

		//ALTER TABLE <t1> ADD COLUMN <new_column> <_correct_column_type_>;
		//UPDATE <t1> SET <new_column> = <column>;
		//ALTER TABLE <t1> DROP COLUMN <column>;
		//ALTER TABLE <t1> RENAME COLUMN <new_column> TO <column>;
		String tempCol = nonKeyCol + "_TEMP";
		String add = "ALTER TABLE " + tableName + " ADD COLUMN " + q(tempCol) + " " + sqlType + ";";
		String update = "UPDATE " + tableName + " SET " + q(tempCol) + " = " + q(nonKeyCol) + ";";
		String drop = "ALTER TABLE " + tableName + " DROP COLUMN " + q(nonKeyCol) + ";";
		String rename = "ALTER TABLE " + tableName + " RENAME COLUMN " + q(tempCol) + " TO " + q(nonKeyCol) + ";";

		List<String> sqls = new ArrayList<>();
		sqls.add(add);
		sqls.add(update);
		sqls.add(drop);
		sqls.add(rename);

		return sqls;
	}

	@Override
	public String getTargetName(String originalName) {
		return TableNameMapper.getName(originalName.toUpperCase(), TableNameMapper.groupNamePattern3, false, 256, "[A-Z0-9_]", TableNameMapper.prefix.toUpperCase());
	}

	@Override
	@SneakyThrows
	public java.sql.Timestamp getTimestampRoundUp(ResultSet rs, int column) {
		return this.roundUpTimestamp(super.getTimestamp(rs, column));
	}

	@Override
	public String getCreateSql(
			String tableName,
			Collection<String> keyColumns,
			Collection<String> nonKeyColumns,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig,
			JdbcSinkConnectorConfig sinkConnectorConfig) {

		StringBuilder builder = new StringBuilder("CREATE TABLE ");
		builder.append(tableName);
		builder.append(" (");
		if (mappingConfig.getTrackerMode() != Tracker.TrackerMode.NONE) {
			nonKeyColumns.add(mappingConfig.getTrackerFieldName());
		}
		joinToBuilder(builder, ",", keyColumns, nonKeyColumns, (b, val) -> {
			String sqlType = mappingConfig
					.getMapping()
					.values()
					.stream()
					.filter(map -> map.containsKey(val))
					.findFirst()
					.map(e -> e.get(val))
					.filter(t -> !t.equals(DEFAULT_MAPPING))
					.orElseGet(() -> automaticBinding.get().getSqlType(val, this));

			b.append(q(val)).append(" ").append(sqlType);
		});
		builder.append(",");

		if (isNotEmpty(keyColumns)) {
			builder.append("PRIMARY KEY(");
			joinToBuilder(builder, ",", keyColumns, escaper());
			builder.append(")");
		} else {
			//Deleting the final ,
			builder.deleteCharAt(builder.length() - 1);
		}

		builder.append(")");

		//To support cluserting keys
		if (isNotEmpty(sinkConnectorConfig.clusteringingColumns)
				&& sinkConnectorConfig.clusteringingColumns.size() > 0) {
			builder.append(" CLUSTER BY (");
			joinToBuilder(builder, ",", sinkConnectorConfig.clusteringingColumns, escaper());
			builder.append(")");
		}

		return builder.toString();
	}

	@Override
	public String getSelectRawDataTypesQuery(String table, String schema) {
		var schemaCondition = StringUtils.isNotBlank(schema) ? " AND table_schema = '" + schema.toUpperCase() + "'" : "";
		var query = "SELECT column_name, " +
				" CASE " +
				" WHEN data_type = 'TEXT' AND character_maximum_length IS NOT NULL THEN 'VARCHAR(' || character_maximum_length || ')' " +
				" WHEN data_type = 'TEXT' THEN 'VARCHAR' " +
				" WHEN numeric_precision IS NOT NULL AND numeric_scale IS NOT NULL THEN 'NUMBER(' || numeric_precision || ',' || numeric_scale || ')' " +
				" ELSE data_type " +
				" END AS data_type " +
				" FROM INFORMATION_SCHEMA.COLUMNS " +
				" WHERE table_name = '%s' %s";

		return String.format(query, table, schemaCondition);
	}
}
