package com.nexla.probe.sql.connection;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.anyString;

public class PostgresSearchPathTest {

    @Test
    public void testPostgresSearchPathSingleSchema() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);
        Statement statement = Mockito.mock(Statement.class);
        when(connection.createStatement()).thenReturn(statement);

        // Create a test JdbcAuthConfig with PostgreSQL and single schema
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.POSTGRES, "my_schema");

        // Call the method
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that the correct SQL was executed
        verify(statement).execute("SET search_path TO \"my_schema\", \"public\"");
    }

    @Test
    public void testPostgresSearchPathMultipleSchemas() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);
        Statement statement = Mockito.mock(Statement.class);
        when(connection.createStatement()).thenReturn(statement);

        // Create a test JdbcAuthConfig with PostgreSQL and multiple schemas
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.POSTGRES, "schema1, schema2, schema3");

        // Call the method
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that the correct SQL was executed
        verify(statement).execute("SET search_path TO \"schema1\", \"schema2\", \"schema3\", \"public\"");
    }

    @Test
    public void testPostgresSearchPathWithPublicSchema() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);
        Statement statement = Mockito.mock(Statement.class);
        when(connection.createStatement()).thenReturn(statement);

        // Create a test JdbcAuthConfig with PostgreSQL and schemas including public
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.POSTGRES, "schema1, public, schema3");

        // Call the method
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that the correct SQL was executed (public should appear only once)
        verify(statement).execute("SET search_path TO \"schema1\", \"public\", \"schema3\"");
    }

    @Test
    public void testPostgresSearchPathErrorHandling() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);
        Statement statement = Mockito.mock(Statement.class);
        when(connection.createStatement()).thenReturn(statement);

        // Make the statement execution throw an exception
        doThrow(new SQLException("Test exception")).when(statement).execute(anyString());

        // Create a test JdbcAuthConfig with PostgreSQL
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.POSTGRES, "my_schema");

        // Call the method - should not throw an exception
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that the method attempted to execute the SQL
        verify(statement).execute(anyString());
    }

    @Test
    public void testPostgresSearchPathNoSchemaSpecified() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);
        Statement queryStatement = Mockito.mock(Statement.class);
        Statement executeStatement = Mockito.mock(Statement.class);
        ResultSet resultSet = Mockito.mock(ResultSet.class);

        // Setup the mocks to return the search_path from the database
        when(connection.createStatement())
            .thenReturn(queryStatement)  // First call for getUserSearchPath
            .thenReturn(executeStatement); // Second call for setting the search_path
        when(queryStatement.executeQuery("SHOW search_path")).thenReturn(resultSet);
        when(resultSet.next()).thenReturn(true);
        when(resultSet.getString(1)).thenReturn("$user, public, extensions");

        // Create a test JdbcAuthConfig with PostgreSQL but no schema specified
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.POSTGRES, null);

        // Call the method
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that the method queried the search_path and set it correctly
        verify(queryStatement).executeQuery("SHOW search_path");
        verify(executeStatement).execute("SET search_path TO $user, public, extensions");
    }

    @Test
    public void testPostgresSearchPathDefaultPublicSchema() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);
        Statement queryStatement = Mockito.mock(Statement.class);
        Statement executeStatement = Mockito.mock(Statement.class);
        ResultSet resultSet = Mockito.mock(ResultSet.class);

        // Setup the mocks to return the search_path from the database
        when(connection.createStatement())
            .thenReturn(queryStatement)  // First call for getUserSearchPath
            .thenReturn(executeStatement); // Second call for setting the search_path
        when(queryStatement.executeQuery("SHOW search_path")).thenReturn(resultSet);
        when(resultSet.next()).thenReturn(true);
        when(resultSet.getString(1)).thenReturn("$user, public, extensions");

        // Create a test JdbcAuthConfig with PostgreSQL and the default "public" schema
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.POSTGRES, "public");

        // Call the method
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that the method queried the search_path and set it correctly
        verify(queryStatement).executeQuery("SHOW search_path");
        verify(executeStatement).execute("SET search_path TO $user, public, extensions");
    }

    @Test
    public void testPostgresSearchPathNoSchemaSpecifiedQueryFails() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);
        Statement queryStatement = Mockito.mock(Statement.class);
        Statement executeStatement = Mockito.mock(Statement.class);

        // Setup the mocks to simulate a failure when querying the search_path
        when(connection.createStatement())
            .thenReturn(queryStatement)  // First call for getUserSearchPath
            .thenReturn(executeStatement); // Second call for setting the search_path
        when(queryStatement.executeQuery("SHOW search_path")).thenThrow(new SQLException("Test exception"));

        // Create a test JdbcAuthConfig with PostgreSQL but no schema specified
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.POSTGRES, null);

        // Call the method - should not throw an exception
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that the method queried the search_path
        verify(queryStatement).executeQuery("SHOW search_path");

        // Verify that it falls back to using public schema
        verify(executeStatement).execute("SET search_path TO \"public\"");
    }

    @Test
    public void testNonPostgresConnection() throws Exception {
        // Mock objects
        Connection connection = Mockito.mock(Connection.class);

        // Create a test JdbcAuthConfig with MySQL
        TestJdbcAuthConfig config = new TestJdbcAuthConfig(ConnectionType.MYSQL, "my_schema");

        // Call the method
        ConnectionHelper.addSchemaToConnection(config, connection);

        // Verify that setSchema was called instead of executing SQL
        verify(connection).setSchema("my_schema");
        verify(connection, never()).createStatement();
    }

    /**
     * Test implementation of JdbcAuthConfig for testing purposes
     */
    private static class TestJdbcAuthConfig extends JdbcAuthConfig {
        public TestJdbcAuthConfig(ConnectionType dbType, String schemaName) {
            super(createConfigMap(dbType, schemaName), 1);
        }

        private static Map<String, Object> createConfigMap(ConnectionType dbType, String schemaName) {
            Map<String, Object> config = new HashMap<>();
            config.put("credentials_type", dbType.toString());
            if (schemaName != null) {
                config.put("schema_name", schemaName);
            }
            config.put("url", "jdbc:test:url");
            config.put("port", 5432);
            return config;
        }
    }
}
