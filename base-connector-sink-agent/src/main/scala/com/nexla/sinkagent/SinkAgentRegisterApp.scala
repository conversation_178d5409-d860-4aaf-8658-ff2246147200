package com.nexla.sinkagent

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.{AppType, NexlaConstants, NexlaSslContext, RestTemplateBuilder}
import com.nexla.kafka.KafkaMessageTransport
import com.nexla.sc.util.{AppUtils, Async, StrictNexlaLogging}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

object SinkAgentRegisterApp
  extends App
    with AppUtils
    with StrictNexlaLogging {

  implicit val system = ActorSystem()
  implicit val materializer = ActorMaterializer()
  implicit val executionContext: ExecutionContext = Async.ioExecutorContext

  implicit private val (props, nexlaAppConfig, _) = loadProps(AppType.SINK_AGENT, new AppProps(_))
  implicit val appSslContext: NexlaSslContext = nexlaSslContext(props)

  private val messageProducer = new NexlaMessageProducer(new KafkaMessageTransport(props.bootstrapServers, appSslContext, NexlaConstants.TOPIC_METRICS, NexlaConstants.TOPIC_NOTIFY))

  val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()

  val sinkConnectorClient = new SinkConnectorClient(props, restTemplate)
  while (Try(sinkConnectorClient.getConnectors()).isFailure) {
    logger.info(s"Kafka Connect application is not ready...")
    Thread.sleep(10000)
  }

  logger.info(s"Kafka Connect application is ready")

  Try {
    props.dedicatedTaskId match {
      case Some(sinkId) => startDedicatedSink(sinkId)
      case None => startSinkAgent()
    }
  } match {
    case Failure(e) =>
      logger.error("Failed to initialize connector: {}", e.getMessage, e)
      System.exit(1)
    case Success(_) => logger.info("SinkAgentRegisterApp run finished")
  }

  private def startDedicatedSink(sinkId: Int): Unit = {
    Try {
      logger.info("Removing all tasks from node")
      sinkConnectorClient.deleteAllConnectors()

      logger.info(s"Starting dedicated sink connector $sinkId")
      sinkConnectorClient.startDedicatedSinkTask(sinkId)
    } match {
      case Success(_) => logger.info(s"Dedicated sink connector $sinkId started")
      case Failure(e) => throw new Exception(s"Failed to start dedicated sink connector $sinkId", e)
    }
  }

  private def startSinkAgent(): Unit = {
    Try {
      logger.info(s"Starting SinkAgent")
      sinkConnectorClient.restartSinkAgent()
    } match {
      case Success(_) => logger.info("SinkAgent started")
      case Failure(e) => throw new Exception("Failed to start SinkAgent", e)
    }
  }
}
