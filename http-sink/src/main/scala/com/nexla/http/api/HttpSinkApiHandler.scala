package com.nexla.http.api

import akka.Done
import akka.actor.ActorSystem
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.marshalling.sse.EventStreamMarshalling._
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.model.sse.ServerSentEvent
import akka.http.scaladsl.server.{Directives, Route}
import akka.kafka.ConsumerMessage.CommittableMessage
import akka.kafka.ConsumerSettings
import akka.kafka.Subscriptions._
import akka.kafka.scaladsl.Consumer
import akka.stream.Materializer
import akka.stream.scaladsl.{Flow, Keep, Sink}
import cats.data.EitherT
import cats.implicits.catsStdInstancesForTry
import com.nexla.admin.client.{AdminApiClient, AuthResource}
import com.nexla.common.NexlaNamingUtils._
import com.nexla.common.ResourceType.SINK
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.connector.config.FlowType
import com.nexla.http.AppProps
import com.nexla.kafka.service.TopicMetaService
import com.nexla.sc.api.common.ApiResponse
import com.nexla.sc.util._
import com.nexla.sc.util.StrictNexlaLogging
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsDirectives.pathPrefixLabeled
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.common.serialization.StringDeserializer
import spray.json.DefaultJsonProtocol

import java.util.Optional
import java.util.Optional.empty
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Try}

class HttpSinkApiHandler(implicit adminApi: AdminApiClient,
                         topicMetaService: TopicMetaService,
                         messageSender: NexlaMessageProducer,
                         props: AppProps,
                         ec: ExecutionContext,
                         system: ActorSystem,
                         mat: Materializer)
  extends Directives
    with SprayJsonSupport
    with DefaultJsonProtocol
    with StrictNexlaLogging
    with WithLogging {

  private val authFilter = new AdminApiAuthFilter(adminApi)

  private val IdleTimeout = 10.minutes

  private def toEither[T](t: Try[T]): EitherT[Try, ApiException, T] =
    EitherT.fromEither(t.toEither).leftMap[ApiException]((e: Throwable) => ApiException("", Some(e)))

  private val events = withRequestTimeout(IdleTimeout) {
    (get & pathPrefixLabeled("stream" / "sink" / IntNumber / "group" / Segment, "stream/sink/:sinkId/group/:groupId") & end) { case (sinkId, groupId) =>
      extractRequest { request =>

        val value = for {
          sink <- toEither(Try(adminApi.getDataSink(sinkId).get()))
            .leftMap(_.copy(s"Sink not found: $sinkId", status = Some(NotFound)))
          dataSetId = sink.getDataSet.getId
          authorized <- toEither(Try(authFilter.authenticate(request, new AuthResource(SINK, sinkId, "read"))))
          _ <- EitherT.cond(authorized, (), ApiException("Access to sink is not authorized: " + sinkId))
          topic = nameDataSetTopic(dataSetId)
          topicExists <- toEither(Try(topicMetaService.isExistingTopic(topic)))
          _ <- EitherT.cond(topicExists, (), ApiException(s"Dataset topic not found: $topic", status = Some(NotFound)))
        } yield (dataSetId, topic)

        value.value
          .map {
            case Left(ApiException(message, _, Some(status))) => complete(status -> ApiResponse(message))
            case Left(ApiException(message, _, _)) => complete(InternalServerError -> ApiResponse(message))

            case Right((dataSetId, topic)) =>

              logger.info(s"[sink-$sinkId-dataset-$dataSetId] Started reading")
              complete {

                val consumerSettings =
                  ConsumerSettings(props.kafkaConsumerConfig, new StringDeserializer, new StringDeserializer)
                    .withBootstrapServers(props.bootstrapServers)
                    .withProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest")
                    .withGroupId(groupId)
                    .withProperties(props.consumerProps)

                Consumer
                  .committableSource(consumerSettings, topics(topic))
                  .idleTimeout(IdleTimeout)
                  .alsoTo(sendMetrics(dataSetId))
                  .mapAsync(1) { r =>
                    r.committableOffset
                      .commitScaladsl()
                      .map(_ => ServerSentEvent(r.record.value()))
                  }
                  .watchTermination()((_, eventualDone) =>
                    eventualDone.andThen {
                      case <EMAIL>(_) =>
                        logger.info(s"[sink-$sinkId-dataset-$dataSetId] Stopped reading, result=$r")
                      case r@Failure(e) =>
                        logger.info(s"[sink-$sinkId-dataset-$dataSetId] Stopped reading, result=$r", e)
                    })
              }
          }
          .recover { case e =>
            logger.error(s"[sink-$sinkId] Error", e)
            complete(InternalServerError)
          }
          .get
      }
    }
  }

  private def sendMetrics(dataSetId: Int): Sink[CommittableMessage[String, String], Future[Done]] =
    Flow[CommittableMessage[String, String]]
      .groupedWithin(1000, 1.second)
      .map(records => (records.size, records.view.map(_.record.value().length).sum))
      .toMat(Sink.foreach { case (records, bytes) =>
        val dataset = adminApi.getDataSet(dataSetId).get()
        // todo can runId be sent in the request?
        messageSender.publishMetrics(
          NexlaRawMetric.create(SINK, dataSetId, Long.box(records), Long.box(bytes), 0L,
            System.currentTimeMillis(), Optional.of(0L), empty(), empty(), empty(), empty(), empty(), empty(), FlowType.STREAMING,
            empty(), empty(), empty(),
            dataset.getOrgId,
            dataset.getOwnerId))
      })(Keep.right)

  val route: Route = concat(events)
}
