package com.nexla.connector.documentdb.source;

import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.connect.common.connector.BaseSourceConnector;
import com.nexla.connector.config.documentdb.DocumentDbSourceConnectorConfig;
import com.nexla.kafka.KafkaMessageTransport;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;

import java.util.List;
import java.util.Map;

import static com.nexla.common.NexlaConstants.VERSION;
import static java.util.Collections.nCopies;

public class DocumentDbSourceConnector extends BaseSourceConnector<DocumentDbSourceConnectorConfig> {
	@Override
	public String version() {
		return VERSION;
	}

	@Override
	protected String telemetryAppName() {
		return "documentdb-source";
	}

	@Override
	public void start(Map<String, String> settings) {
		super.start(settings);
	}

	@Override
	protected DocumentDbSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new DocumentDbSourceConnectorConfig(props);
	}

	@Override
	public Class<? extends Task> taskClass() {
		return DocumentDbSourceTask.class;
	}

	@Override
	public List<Map<String, String>> taskConfigs(int maxTasks) {
		return nCopies(maxTasks, config.originalsStrings());
	}

	@Override
	public void stop() {
		logger.info("Stopping document DB connector");
	}

	@Override
	public ConfigDef config() {
		return DocumentDbSourceConnectorConfig.configDef();
	}

	// FIXME seems to be not used. Remove method or ensure that NexlaMessage producer is closed
	protected NexlaMessageProducer createNexlaMessageProducer() {
		return new NexlaMessageProducer(new KafkaMessageTransport(config.bootstrapServers, sslContext, config.topicMetrics, config.topicNotify));
	}
}