package com.nexla.connector.iceberg.source;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.ConnectionType.S3_ICEBERG;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.ResourceType.SOURCE;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;

import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.Resource;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseSourceTask;
import com.nexla.connect.common.CollectRecordsResult;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connector.config.iceberg.IcebergSourceConnectorConfig;
import com.nexla.probe.iceberg.IcebergConnectorService;
import com.nexla.probe.iceberg.IcebergReadException;
import edu.emory.mathcs.backport.java.util.Collections;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.iceberg.PartitionSpec;
import org.apache.iceberg.Table;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.spark.SparkCatalog;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.spark.scheduler.SparkListener;
import org.apache.spark.scheduler.SparkListenerTaskEnd;
import org.apache.spark.sql.DataFrameReader;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.types.ArrayType;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import org.apache.spark.util.LongAccumulator;
import scala.Function2;

public class IcebergSourceTask extends BaseSourceTask<IcebergSourceConnectorConfig> {

  private final AtomicLong recordOffset = new AtomicLong(0L);
  private IcebergConnectorService probe = new IcebergConnectorService();
  private SparkSession spark;
  private DataFrameReader reader;

  @Override
  public ConfigDef configDef() {
    return IcebergSourceConnectorConfig.configDef();
  }

  @Override
  public void doStart(Map<String, String> props) {
    probe = new IcebergConnectorService();
    spark = probe.getSparkSession(config, Collections.emptyMap());
    reader = probe.prepareDataFrameReader(config);
    schemaDetection.setTryCombineSingleSchema();

    if (!spark.catalog().tableExists(config.tableName)) {
      throw new IcebergReadException(String.format("Table %s does not exist", config.tableName));
    }
  }

  @SneakyThrows
  @Override
  public void stop() {
    spark.stop();
    probe.close();
    super.stop();
  }

  @Override
  public CollectRecordsResult collectRecords() {
    final long ingestionTime = System.currentTimeMillis();
    final LongAccumulator countAccumulator = spark.sparkContext().longAccumulator("record-count");
    final LongAccumulator bytesAccumulator = spark.sparkContext().longAccumulator("byte-count");
    final SparkListener accumulatorListener = new SparkListener() {
      @Override
      public void onTaskEnd(SparkListenerTaskEnd taskEnd) {
        var metrics = taskEnd.taskMetrics();
        if (metrics != null && metrics.inputMetrics() != null) {
          countAccumulator.add(metrics.inputMetrics().recordsRead());
          bytesAccumulator.add(metrics.inputMetrics().bytesRead());
        }
      }
    };

    if (spark.sparkContext().isStopped()) {
      logger.warn("Spark context is stopped, not proceeding with collectRecords");
      return new CollectRecordsResult(Collections.emptyList());
    }

    spark.sparkContext().addSparkListener(accumulatorListener);

    // FIXME: extremely basic offset tracking, runs exactly once
    if (recordOffset.get() > 0L) {
      return new CollectRecordsResult(Collections.emptyList());
    }

    long startedQuery = System.currentTimeMillis();

    ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(() ->
            ctrlClient
                .filter(x -> System.currentTimeMillis() - startedQuery < config.queryHeartbeatPeriodMs)
                .ifPresent(jpc -> {
                  logger.info("Heartbeat. trace=false");
                  jpc.heartbeatConnector(new Resource(config.sourceId, SOURCE), runId, false);
                }),
        1, 1, TimeUnit.MINUTES);

    try {
      final Dataset<Row> df = reader.format("iceberg").load(config.tableName);

      final List<SourceRecordCreator> messages = df.collectAsList().stream()
          .map(IcebergSourceTask::convertRowToLinkedHashMap)
          .map(this::createRecord)
          .collect(Collectors.toList());

      // ! important to detect schema before sending metrics since detectSchemaIfNecessary
      // ! will set the appropriate dataset ID for metrics allocation
      List<SourceRecord> schemaDetectedRecords = detectSchemaIfNecessary(false, messages, Optional.empty());

      if (!messages.isEmpty()) {
        sendMetrics(String.format("%s%s", config.tableName, runId > 1L ? " ("+ runId +")" : ""),
            countAccumulator.sum(), bytesAccumulator.sum(), 0L, ingestionTime);
      }

      return new CollectRecordsResult(schemaDetectedRecords);
    } finally {
      heartbeat.cancel(true);
    }
  }

  @Override
  protected IcebergSourceConnectorConfig parseConfig(Map<String, String> props) {
    return new IcebergSourceConnectorConfig(props);
  }

  private static LinkedHashMap<String, Object> convertRowToLinkedHashMap(Row row) {
    LinkedHashMap<String, Object> rowMap = new LinkedHashMap<>();
    StructType schema = row.schema();

    for (StructField field : schema.fields()) {
      String fieldName = field.name();
      Object fieldValue = row.get(row.fieldIndex(fieldName));

      if (fieldValue != null) {
        DataType fieldType = field.dataType();
        Object primitiveValue = convertToPrimitive(fieldValue, fieldType);
        rowMap.put(fieldName, primitiveValue);
      }
    }

    return rowMap;
  }

  private static Object convertToPrimitive(Object value, DataType dataType) {
    if (dataType instanceof ArrayType) {
      ArrayType arrayType = (ArrayType) dataType;
      DataType elementType = arrayType.elementType();
      List<Object> primitiveArray = new ArrayList<>();

      if (value instanceof scala.collection.mutable.WrappedArray) {
        scala.collection.mutable.WrappedArray<?> wrappedArray = (scala.collection.mutable.WrappedArray<?>) value;
        List<?> convertedArray = scala.collection.JavaConverters.seqAsJavaList(wrappedArray.seq());
        for (Object element : convertedArray) {
          Object primitiveElement = convertToPrimitive(element, elementType);
          primitiveArray.add(primitiveElement);
        }
      }

      return primitiveArray;
    } else if (dataType instanceof StructType) {
      StructType structType = (StructType) dataType;
      LinkedHashMap<String, Object> primitiveStruct = new LinkedHashMap<>();

      if (value instanceof Row) {
        Row structRow = (Row) value;
        for (StructField field : structType.fields()) {
          String fieldName = field.name();
          Object fieldValue = structRow.get(structRow.fieldIndex(fieldName));
          Object primitiveFieldValue = convertToPrimitive(fieldValue, field.dataType());
          primitiveStruct.put(fieldName, primitiveFieldValue);
        }
      }

      return primitiveStruct;
    } else {
      return value;
    }
  }

  private SourceRecordCreator createRecord(LinkedHashMap<String, Object> dataMap) {
    final long now = System.currentTimeMillis();

    Function2<Integer, String, NexlaMessage> nexlaMessageCreator = (dataSetId, dataSetTopic) -> {
      NexlaMetaData metaData = new NexlaMetaData(
          S3_ICEBERG,
          now,
          recordOffset.getAndIncrement(),
          null,
          dataSetTopic,
          SOURCE,
          config.sourceId,
          false,
          new Tracker(Tracker.TrackerMode.FULL,
              SourceItem.fullTracker(
                  config.sourceId,
                  dataSetId,
                  config.tableName,
                  recordOffset.get(),
                  1,
                  now)),
          runId);

      return new NexlaMessage(dataMap, metaData);
    };

    Function2<Integer, String, SourceRecord> sourceRecordCreator = (datasetId, topic) -> {
      NexlaMessage message = nexlaMessageCreator.apply(datasetId, topic);
      return new SourceRecord(Collections.emptyMap(), Collections.emptyMap(), topic, null, null, null, STRING_SCHEMA, toJsonString(message));
    };

    return new SourceRecordCreator(dataMap, sourceRecordCreator, nexlaMessageCreator);
  }

  // TODO: extract the partition spec and pass it in SourceRecords
  @SneakyThrows
  private void getPartitionSpec(SparkSession session) {
    SparkCatalog catalog = new SparkCatalog();
    TableIdentifier id = TableIdentifier.of(config.tableName.split("\\."));
    Table table = catalog.icebergCatalog().loadTable(id);
    PartitionSpec spec = table.spec();
  }
}
