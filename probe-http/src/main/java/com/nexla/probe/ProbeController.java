package com.nexla.probe;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.config.ConfigUtils;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.*;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.SimplePool;
import com.nexla.common.probe.*;
import com.nexla.common.time.VarUtils;
import com.nexla.common.ui.PaginatedResult;
import com.nexla.connect.box.BoxConnectorService;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connector.*;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.big_query.BigQuerySourceConnectorConfig;
import com.nexla.connector.config.documentdb.DocumentDbSourceConnectorConfig;
import com.nexla.connector.config.documentdb.DocumentDbTreeService;
import com.nexla.connector.config.documentdb.dynamodb.DynamoDbAuthConfig;
import com.nexla.connector.config.documentdb.mongo.MongoAuthConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.jdbc.*;
import com.nexla.connector.config.kafka.JmsAuthConfig;
import com.nexla.connector.config.kafka.KafkaBackendReadConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.connector.config.ssh.HostPort;
import com.nexla.connector.config.ssh.tunnel.ConfigWithAuth;
import com.nexla.connector.config.ssh.tunnel.SshTunnel;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.config.vault.CredentialsStore;
import com.nexla.connector.config.vault.NexlaAppConfig;
import com.nexla.connector.config.vectordb.VectorTreeService;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.listing.client.ListingClient;
import com.nexla.parser.ParserUtilsExt.Result;
import com.nexla.probe.azure.blob.AzureBlobStoreConnectorService;
import com.nexla.probe.azure.datalake.AzureDataLakeConnectorService;
import com.nexla.probe.bigquery.BigQueryClientService;
import com.nexla.probe.bigquery.BigQueryConnectorService;
import com.nexla.probe.connectors.JMSConnectorService;
import com.nexla.probe.connectors.PubSubConnectorService;
import com.nexla.probe.deltalake.DeltaLakeConnectorService;
import com.nexla.probe.dropbox.DropboxConnectorService;
import com.nexla.probe.dynamodb.DynamoDbService;
import com.nexla.probe.firebase.FirebaseService;
import com.nexla.probe.ftp.FileConnectorServiceBuilder;
import com.nexla.probe.ftp.FtpConnectorService;
import com.nexla.probe.gcs.GCSConnectorService;
import com.nexla.probe.gdrive.GDriveConnectorService;
import com.nexla.probe.http.RequestSender;
import com.nexla.probe.http.RestConnectorService;
import com.nexla.probe.http.SoapConnectorService;
import com.nexla.probe.iceberg.IcebergConnectorService;
import com.nexla.probe.kafka.KafkaBackendReadService;
import com.nexla.probe.kafka.KafkaConnectorService;
import com.nexla.probe.listtree.DbListTreeIteration;
import com.nexla.probe.listtree.DbListTreeIterationPaged;
import com.nexla.probe.listtree.DocumentDbListTreeIteration;
import com.nexla.probe.listtree.VectorDbListTreeIteration;
import com.nexla.probe.onedrive.OneDriveConnectorService;
import com.nexla.probe.mongodb.MongoDbService;
import com.nexla.probe.pinecone.PineconeService;
import com.nexla.probe.redis.RedisConnectorService;
import com.nexla.probe.s3.S3ConnectorService;
import com.nexla.probe.sharepoint.SharepointConnectorService;
import com.nexla.probe.sink.EmptySinkTaskContext;
import com.nexla.probe.sink.rest.ProbeRestSinkTask;
import com.nexla.probe.sink.soap.ProbeSoapSinkTask;
import com.nexla.probe.sql.SqlConnectorService;
import com.nexla.probe.webdav.WebDAVConnectorService;
import com.nexla.sinkcaller.HttpResponseState;
import com.nexla.sinkcaller.RestSinkCaller;
import com.nexla.sinkcaller.SoapSinkCaller;
import com.nexla.soap.SoapWsdlParser;
import com.nexla.soap.wsdl.WsdlDefinition;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FilenameUtils;
import org.apache.http.client.utils.URIBuilder;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.AbstractConfig;
import org.apache.kafka.connect.sink.SinkTask;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joor.ReflectException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.HttpStatusCodeException;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Supplier;

import static com.nexla.admin.client.config.SourceConfigUtils.sourceConfigDef;
import static com.nexla.admin.client.oauth2.NexlaTokenProvider.sameToken;
import static com.nexla.admin.config.ConfigUtils.createSinkConfig;
import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.ListingResourceType.FOLDER;
import static com.nexla.common.NexlaConstants.ConnectionTypeCategory.DATABASE;
import static com.nexla.common.NexlaConstants.PATH;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.StreamUtils.*;
import static com.nexla.common.parse.SampleContentType.BINARY;
import static com.nexla.common.probe.ProbeControllerConstants.*;
import static com.nexla.connector.ConnectorService.DEFAULT_SAMPLE_ROWS;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.file.AWSAuthConfig.getFilePath;
import static com.nexla.connector.config.soap.SoapWsdlConfig.SOAP_WSDL_URL;
import static com.nexla.parser.ParserUtilsExt.detectParser;
import static com.nexla.probe.http.BaseRequestSender.createSender;
import static com.nexla.probe.sharepoint.SharepointConnectorService.METADATA_WEBURL;
import static com.nexla.probe.sharepoint.SharepointConnectorService.METADATA_ACCESS;
import static com.nexla.spec.Configs.findSpec;
import static java.util.Arrays.deepToString;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Optional.*;
import static java.util.stream.Collectors.toList;
import static org.codehaus.groovy.runtime.InvokerHelper.asList;
import static org.joor.Reflect.on;
import static org.springframework.http.HttpStatus.*;

public class ProbeController {

	private static final Logger logger = LoggerFactory.getLogger(ProbeController.class);

	private static final String JSON = "json";
	private static final String CSV = "csv";
	private static final String SINK_CONFIG = "sink_config";
	private static final String SOURCE_CONFIG = "source_config";
	private static final String INPUT = "input";

	private static final Set<String> DDL_MARKER_WORDS = new HashSet<>() {{
		add("DROP ");
		add("DELETE ");
		add("INSERT ");
		add("UPDATE ");
		add("GRANT ");
		add("REVOKE ");
		add("TRUNCATE ");
		add("CREATE ");
		add("ALTER ");
		add("RENAME ");
		add("COMMENT ");
		add("MERGE ");
	}};

	private final AdminApiClient adminApiClient;
	private final String decryptKey;
	private final int maxListBucketFiles;
	private final String nexlaCredsEnc;
	private final String nexlaCredsEncIv;
	private final ScriptEvalClient scriptEvalClient;
	private NexlaSslContext sslContext;
	private final String bootstrapServers;
	private final NexlaAppConfig nexlaAppConfig;
	private final ListingClient listingClient;

	private final Integer consumerMaxPartitionFetchBytes;
	private final Integer consumerFetchMaxBytes;

	private final FileConnectorServiceBuilder fileConnectorServiceBuilder;

	public ProbeController(
		AdminApiClient adminApiClient,
		ListingClient listingClient,
		AppProps props,
		NexlaAppConfig nexlaAppConfig,
		NexlaSslContext sslContext,
		ScriptEvalClient scriptEvalClient,
		FileConnectorServiceBuilder fileConnectorServiceBuilder
	) {
		this.adminApiClient = adminApiClient;
		this.listingClient = listingClient;
		this.decryptKey = props.decryptKey();
		this.maxListBucketFiles = props.maxListBucketFiles();

		this.bootstrapServers = props.bootstrapServers();
		this.nexlaAppConfig = nexlaAppConfig;
		this.nexlaCredsEnc = props.nexlaCredsEncoded().enc();
		this.nexlaCredsEncIv = props.nexlaCredsEncoded().encIv();

		this.consumerMaxPartitionFetchBytes = props.consumerMaxPartitionFetchBytes()
				.getOrElse(() -> null);
		this.consumerFetchMaxBytes = props.consumerFetchMaxBytes()
				.getOrElse(() -> null);

		this.sslContext = sslContext;
		this.scriptEvalClient = scriptEvalClient;
		this.fileConnectorServiceBuilder = fileConnectorServiceBuilder;
	}

	@SneakyThrows
	public List<NexlaBucket> list(ConnectionType connectionType, ProbeInput probeInput) {
		AbstractConfig sourceConfig = fullSourceConfig(connectionType, probeInput);
		try (ConnectorService probeService = getProbeService(connectionType)) {
			return (List<NexlaBucket>) probeService.listBuckets(sourceConfig).toList();
		}
	}

	public ConnectorService getProbeService(ConnectionType connectionType) {
		if (connectionType.isFile()) {
			return fileConnectorServiceBuilder.createFileConnectorService(connectionType);
		}

		if (connectionType.equals(MONGO)) {
			return new MongoDbService();
		} else if (connectionType.equals(FIREBASE)) {
			return new FirebaseService();
		} else if (connectionType.equals(DYNAMODB)) {
			return new DynamoDbService();
		} else if (connectionType.equals(BIGQUERY)) {
			return new BigQueryConnectorService(adminApiClient, listingClient, new BigQueryClientService(adminApiClient, decryptKey));
		} else if (connectionType.equals(REST)) {
			return new RestConnectorService(scriptEvalClient, of(adminApiClient));
		} else if (connectionType.equals(KAFKA) || connectionType.equals(CONFLUENT_KAFKA)) {
			return new KafkaConnectorService(bootstrapServers);
		} else if (connectionType.equals(SOAP)) {
			return new SoapConnectorService(scriptEvalClient);
		} else if (connectionType.equals(DATA_MAP)) {
			return new RedisConnectorService();
		} else if (connectionType.equals(JMS)) {
			return new JMSConnectorService();
		} else if (connectionType.equals(GOOGLE_PUBSUB)) {
			return new PubSubConnectorService();
		} else if (connectionType.equals(PINECONE)) {
			return new PineconeService();
		} else if (connectionType.equals(S3_ICEBERG)) {
			return new IcebergConnectorService();
		}
		if (connectionType.category == DATABASE) {
			return new SqlConnectorService(nexlaAppConfig.getStore(), adminApiClient);
		} else {
			throw new IllegalArgumentException(connectionType.name());
		}
	}

	public ConnectorService.AuthResponse authenticate(ConnectionType connectionType, ProbeInput input) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			BaseAuthConfig config = parseAuthConfig(decryptKey, input, connectionType);
			return probeTunnel(config, () -> probeService.authenticate(config));
		} catch (Exception e) {
			logger.error("[creds-{}] Exception while authenticating", input.getCredsId(), e);
			return ConnectorService.AuthResponse.authError(getUserFriendlyException(e));
		}
	}

	private Throwable getUserFriendlyException(Throwable startThrowable) {
		if (startThrowable instanceof ReflectException) {
			startThrowable = startThrowable.getCause();
		}

		if (startThrowable instanceof InvocationTargetException) {
			startThrowable = startThrowable.getCause();
		}

		Throwable current = startThrowable;
		while (!(current instanceof ProbeException) && current.getCause() != null) {
			current = current.getCause();
		}
		if (current instanceof ProbeException) {
			return current;
		}
		return startThrowable;
	}

	protected BaseAuthConfig parseAuthConfig(String decryptKey, ProbeInput probeInput, ConnectionType connectionType) {
		Map<String, String> originals = "true".equals(probeInput.getParams().get(UNIT_TEST))
			? probeInput.getParams()
			: new NexlaDataCredentials(decryptKey, probeInput.getCredsEnc(), probeInput.getCredsEncIv()).decrypt();

		Class authConfigClass = findSpec(connectionType).authConfigClass().get();
		BaseAuthConfig authConfig = on(authConfigClass).create(originals, probeInput.getCredsId()).get();
		return authConfig;
	}

	public Map<String, Object> readSample(
		ConnectionType connectionType,
		ProbeInput probeInput,
		Optional<Boolean> rawMode,
		Optional<Integer> offset,
		Optional<Integer> pageSize
	) {
		try {
			if (probeInput.getParams().containsKey(SINK_CONFIG)) {
				return readSinkSample(connectionType, probeInput);
			}
			else
				return readSourceSample(connectionType, probeInput, rawMode, offset, pageSize);
		} catch (ProbeRetriableException e) {
			logger.error("Failed to read sample for source = {}", connectionType, e);
			Map<String, Object> body = Maps.newHashMap();
			Throwable innerException = e.getCause();
			if (innerException instanceof HttpStatusCodeException) {
				HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) innerException;

				body.put(ERROR_MESSAGE, httpStatusCodeException.getMessage());
				body.put(RESPONSE, httpStatusCodeException.getResponseBodyAsString());
				body.put(STATUS_CODE, httpStatusCodeException.getRawStatusCode());
			} else {
				body.put(ERROR_MESSAGE, innerException.getMessage());
				body.put(STATUS_CODE, INTERNAL_SERVER_ERROR.value());
			}
			return body;
		} catch (Exception e) {
			logger.error("Failed to read sample for source = {}", connectionType, e);
			Map<String, Object> body = Maps.newHashMap();
			String errorMessage = e.getMessage();
			// Special case to handle errors thrown when there is error in config parameters
			if (e instanceof ReflectException && e.getCause() instanceof InvocationTargetException) {
				errorMessage = ((InvocationTargetException)e.getCause()).getTargetException().getMessage();
				if (errorMessage == null) {
					// fallback to original message
					errorMessage = e.getMessage();
				}
			}
			body.put(ERROR_MESSAGE, errorMessage);
			body.put(STATUS_CODE, INTERNAL_SERVER_ERROR.value());
			return body;
		}
	}

	@SneakyThrows
	public Map<String, Object> readSourceSample(
		ConnectionType connectionType,
		ProbeInput probeInput,
		Optional<Boolean> rawMode,
		Optional<Integer> offset,
		Optional<Integer> pageSize
	) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			maybeAddRedisCredsToRest(connectionType, probeInput);
			var sourceConfig = fullSourceConfig(connectionType, probeInput);

			if (connectionType.isDatabase() && isDDLQuery(sourceConfig)) {
				String msg = String.format("DDL/DML queries containing %s are prohibited in the source sampling", Arrays.deepToString(DDL_MARKER_WORDS.toArray()));
				logger.warn(msg);
				return map(STATUS_CODE, FORBIDDEN.value(), ERROR_MESSAGE, msg);
			}

			return probeTunnel(sourceConfig, () -> {
				ProbeSampleResult sampleResult = probeService.readSample(sourceConfig, rawMode.orElse(false));
				ProbeSampleResult limitedResult = sampleResult.limitOutput(offset, countLines(pageSize, connectionType));

				return buildFromSample(limitedResult);
			});
		}
	}
	
	@SneakyThrows
	public Map<String, Object> readSinkSample(
			ConnectionType connectionType,
			ProbeInput probeInput
	) {
		String dataInput = probeInput.getParams().get(INPUT);
		List<NexlaMessage> nexlaMessages = OBJECT_MAPPER.readValue(dataInput, new TypeReference<List<NexlaMessage>>() {});
		SinkConnectorConfig sinkConnectorConfig = fullSinkConfig(connectionType, probeInput);

		BaseSinkTask task = createEmptySinkTask(connectionType, adminApiClient);
		task.config = sinkConnectorConfig;
		task.doStart();

		List<NexlaMessageContext> nexlaMessageContextList = nexlaMessages.stream().map(msg -> {
			MessageMapper messageMapper = new MessageMapper(task.config.mappingConfig, task.config, false, Optional.empty());
			ExtractedMessage extractedMessage = messageMapper.extractMessage(msg);
			return new NexlaMessageContext(extractedMessage.getOriginal(), extractedMessage.getMapped(), new com.nexla.common.sink.TopicPartition("probe-sink", 0), 0L);
		}).collect(toList());

		SinkSampleResult results = fetchSinkResults(connectionType, task, nexlaMessages.size(), StreamEx.of(nexlaMessageContextList));
		List<String> resultLines = results.getData();

		List<ProbeSampleResultEntry<String>> resultDataStream  = StreamEx.of(resultLines)
				.map(ProbeSampleResultEntry::new)
				.toList();

		return buildFromSample(
				new StringSampleResult(resultDataStream, Optional.empty(), true),
				Optional.of(results.getStatusCode())
		);
	}

	private SinkSampleResult fetchSinkResults(ConnectionType connectionType, SinkTask sinkTask, int streamSize, StreamEx<NexlaMessageContext> messages) {
		List<String> resultLines;
        if (connectionType.equals(REST)) {
            ProbeRestSinkTask restSinkTask = (ProbeRestSinkTask) sinkTask;
            RestSinkCaller restSinkCaller = restSinkTask.createSinkCaller();
            List<HttpResponseState> result = restSinkTask.doPutBatch(streamSize, restSinkCaller, messages, true);
            resultLines = result.stream().map(HttpResponseState::getResponse).map(r -> r.orElse("")).collect(toList());
            int statusCode = result.stream()
                    .filter(r -> r.getStatusCode() <= 199 || 299 < r.getStatusCode())
                    .findFirst().map(HttpResponseState::getStatusCode)
                    .orElse(OK.value());
            return new SinkSampleResult(resultLines, statusCode);
        } else if (connectionType.equals(SOAP)) {
            ProbeSoapSinkTask soapSinkTask = (ProbeSoapSinkTask) sinkTask;
            SoapSinkCaller soapSinkCaller = soapSinkTask.createSinkCaller();
            resultLines = soapSinkTask.doPutBatch(streamSize, soapSinkCaller, messages);
            return new SinkSampleResult(resultLines, OK.value());
        }
        throw new IllegalArgumentException(connectionType.toString());
    }

	private void maybeAddRedisCredsToRest(ConnectionType connectionType, ProbeInput probeInput) {
		if (Objects.equals(connectionType, REST)) {
//			probeInput.getParams().put(REDIS_CREDS_ENC, redisCredsEnc);
//			probeInput.getParams().put(REDIS_CREDS_ENC_IV, redisCredsEncIv);
		}
	}

	@SneakyThrows
	public List<NexlaFile> listFiles(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService<?> probeService = getProbeService(connectionType)) {
			AbstractConfig readConfig = fullSourceConfig(connectionType, probeInput);
			return probeService.listBucketContents(readConfig).limit(maxListBucketFiles).collect(toList());
		}
  }

	public AbstractConfig fullSourceConfig(ConnectionType connectionType, ProbeInput probeInput) {
		Map<String, String> allParams = probeInput.getAllParams(decryptKey, connectionType, nexlaCredsEnc, nexlaCredsEncIv);
		Optional.ofNullable(probeInput.getParams().get(SOURCE_CONFIG)).map(JsonUtils::jsonToMap).map(StreamUtils::stringifyJsonMap).ifPresent(allParams::putAll);
		enrichWithCredentialsStore(allParams, sourceConfigDef(connectionType));
		return ConfigUtils.createSourceConfig(connectionType, allParams);
	}

	public SinkConnectorConfig fullSinkConfig(ConnectionType connectionType, ProbeInput probeInput) {
		Map<String, String> allParams = probeInput.getAllParams(decryptKey, connectionType, nexlaCredsEnc, nexlaCredsEncIv);
		Map<String, String> sinkConfig = stringifyJsonMap(JsonUtils.jsonToMap(probeInput.getParams().get(SINK_CONFIG)));
		allParams.putAll(sinkConfig);
		enrichWithCredentialsStore(allParams, sourceConfigDef(connectionType));
		return ConfigUtils.createSinkConfig(connectionType, allParams);
	}

	@SneakyThrows
	public TreeMap<String, Object> listTreeDb(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			AbstractConfig config = getConfig(probeInput, connectionType, probeService);

			Optional<String> database;
			Optional<String> schema;
			Optional<String> table;

			if (config instanceof JdbcSourceConnectorConfig) {
				JdbcSourceConnectorConfig jdbcSourceConnectorConfig = (JdbcSourceConnectorConfig) config;
				database = ofNullable(probeInput.getParams().get("database"))
					.or(() -> ofNullable(jdbcSourceConnectorConfig.authConfig.databaseName));
				schema = ofNullable(probeInput.getParams().get("schema"))
					.or(() -> ofNullable(jdbcSourceConnectorConfig.authConfig.schemaName));
				table = ofNullable(probeInput.getParams().get("table"));
			} else {
				database = ofNullable(probeInput.getParams().get("database"));
				schema = ofNullable(probeInput.getParams().get("schema"));
				table = ofNullable(probeInput.getParams().get("table"));
			}

			DbListTreeIteration treeIteration = new DbListTreeIteration(config, (DbListTreeService) probeService);
			return probeTunnel(config, () -> treeIteration.listTree(database, schema, table));
		}
	}

	@SneakyThrows
	public DbPage<TreeMap<String, Object>> listTreeDbPaged(ConnectionType connectionType,
																													ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			final AbstractConfig config = getConfig(probeInput, connectionType, probeService);

			String database;
			String schema;
			String table = ofNullable(probeInput.getParams().get("table")).orElse("");
			if (config instanceof JdbcSourceConnectorConfig) {
				JdbcSourceConnectorConfig jdbcSourceConnectorConfig = (JdbcSourceConnectorConfig) config;
				database = ofNullable(probeInput.getParams().get("database"))
					.or(() -> ofNullable(jdbcSourceConnectorConfig.authConfig.databaseName)).orElse("");
				schema = ofNullable(probeInput.getParams().get("schema"))
					.or(() -> ofNullable(jdbcSourceConnectorConfig.authConfig.schemaName)).orElse("");
			} else {
				database = ofNullable(probeInput.getParams().get("database")).orElse("");
				schema = ofNullable(probeInput.getParams().get("schema")).orElse("");
			}

			if (probeService instanceof DbListTreeServicePaged) {
				final String pageSize = ofNullable(probeInput.getParams().get("pageSize")).orElse("100");
				final String offset = ofNullable(probeInput.getParams().get("offset")).orElse("");

				DbListTreeIterationPaged treeIteration = new DbListTreeIterationPaged(config, (DbListTreeServicePaged) probeService);
				return probeTunnel(config, () -> treeIteration
					.listTree(database,
						schema,
						table,
						Integer.parseInt(pageSize),
						offset));
			} else {
				DbListTreeIteration treeIteration = new DbListTreeIteration(config, (DbListTreeService) probeService);
				return probeTunnel(config, () -> DbPage.of(treeIteration
					.listTree(of(database),
						of(schema),
						of(table))));
			}
		}
	}

	@SneakyThrows
	public TreeMap<String, Object> listTreeDocumentDb(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			AbstractConfig config = getConfig(probeInput, connectionType, probeService);
			Optional<String> database;

			if (config instanceof DocumentDbSourceConnectorConfig) {
				database = extractDocumentDbDatabase((DocumentDbSourceConnectorConfig) config, probeInput);
			} else {
				database = ofNullable(probeInput.getParams().get("database"));
			}

			DocumentDbListTreeIteration treeIteration = new DocumentDbListTreeIteration(config, (DocumentDbTreeService) probeService);
			return probeTunnel(config, () -> treeIteration.listTree(database));
		}
	}

	@SneakyThrows
	public TreeMap<String, Object> listTreeVectorDb(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			AbstractConfig config = getConfig(probeInput, connectionType, probeService);
			Optional<String> database = ofNullable(probeInput.getParams().get("database"));
			VectorDbListTreeIteration treeIteration = new VectorDbListTreeIteration(config, (VectorTreeService) probeService);
			return probeTunnel(config, () -> treeIteration.listTree(database));
		}
	}

	private Optional<String> extractDocumentDbDatabase(DocumentDbSourceConnectorConfig config, ProbeInput probeInput) {
		if (config.authConfig instanceof MongoAuthConfig) {
			return ofNullable(((MongoAuthConfig) config.authConfig).database);
		} else if (config.authConfig instanceof DynamoDbAuthConfig) {
			// In DynamoDB there can be only one database per AWS credentials. There's no name for this database.
			return of(DYNAMODB.name().toLowerCase());
		} else {
			return ofNullable(probeInput.getParams().get("database"));
		}
	}

	@SneakyThrows
	public TreeMap<String, Object> listTreeDirect(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			AbstractConfig config = getConfig(probeInput, connectionType, probeService);
			return probeTunnel(config, () -> probeService.listTreeDirect(config));
		}
	}

	@SneakyThrows
	public TreeMap<String, Object> listTreeFiles(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			AbstractConfig config = getConfig(probeInput, connectionType, probeService);
			List<NexlaFile> files = probeService.listTopLevelBuckets(config).toList();
			return treeify(files);
		}
	}

	@SneakyThrows
	public WsdlDefinition listSoap(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			SimplePool<RequestSender> pool = new SimplePool<>(1);
			AbstractConfig config = getConfig(probeInput, connectionType, probeService);
			RestAuthConfig authConfig = (RestAuthConfig) config;
			pool.add(createSender(authConfig, sameToken(), scriptEvalClient, false, authConfig.skipUrlEncoding, empty()));

			SoapWsdlParser soapWsdlParser = new SoapWsdlParser(pool);
			String wsdlUrl = probeInput.getParams().get(SOAP_WSDL_URL);

			return soapWsdlParser.readSoapWsdlDefinitions(wsdlUrl);
		}
	}

	private AbstractConfig getConfig(
		ProbeInput probeInput,
		ConnectionType connectionType,
		ConnectorService probeService
	) {
		if (probeService instanceof RestConnectorService ||
			probeService instanceof SoapConnectorService) {
			return parseAuthConfig(decryptKey, probeInput, connectionType);
		}
		return fullSourceConfig(connectionType, probeInput);
	}

	@SneakyThrows
	public ProbeReadOutput read(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {

			String extension = getExtension(connectionType, probeInput.getParams().get(PATH));
			AbstractConfig readConfig = fullSourceConfig(connectionType, probeInput);

			Map<String, String> overriddenExtensions = (readConfig instanceof FileSourceConnectorConfig)
				? ((FileSourceConnectorConfig) readConfig).overridenExtensions
				: emptyMap();

			try {
				List<NexlaMessage> messages = readProbeMessages(probeInput, probeService, readConfig, overriddenExtensions);
				return new ProbeReadOutput(extension, messages);
			} catch (Exception e) {
				logger.error("Failed to retrieve files, input={}", probeInput, e);
				return new ProbeReadOutput(extension, asList(new NexlaMessage(lhm("result", "Failed to retrieve files"))));
			} finally {
				closeSilently(probeService);
			}
		} catch (Exception e) {
			logger.error("Error processing read request, connectionType={} probeInput={}", connectionType, probeInput, e);
			throw e;
		}
	}


	@SneakyThrows
	public InputStream readEntireFile(String listingPath, DataSource dataSource) {
		logger.info("Downloading file {} from source {}", listingPath, dataSource.getId());
		DataCredentials creds = dataSource.getDataCredentials();

		ProbeInput probeInput = new ProbeInput(creds.getId(), creds.getCredentialsEnc(), creds.getCredentialsEncIv(), Map.of(SOURCE_CONFIG, JsonUtils.toJsonString(dataSource.getSourceConfig())));
		try (ConnectorService probeService = getProbeService(dataSource.getConnectionType())) {

			if (probeService instanceof FileConnectorService) {
				FileSourceConnectorConfig config = (FileSourceConnectorConfig) fullSourceConfig(dataSource.getConnectionType(), probeInput);
				FileConnectorService fileConnectorService = (FileConnectorService) probeService;

				return fileConnectorService.readInputStream(config, listingPath);
			} else {
				throw new IllegalArgumentException("Connection type " + dataSource.getConnectionType() + " does not support reading entire files");
			}
		}
	}

	@SneakyThrows
	public FileDetails uploadFile(String relativePath, DataSink dataSink, File file) {
		logger.info("Uploading file {} to sink {} using temp file {}", relativePath, dataSink.getId(), file.getAbsolutePath());
		DataCredentials creds = dataSink.getDataCredentials();

		ProbeInput probeInput = new ProbeInput(creds.getId(), creds.getCredentialsEnc(), creds.getCredentialsEncIv(), Map.of(SINK_CONFIG, JsonUtils.toJsonString(dataSink.getSinkConfig())));

		try (ConnectorService probeService = getProbeService(dataSink.getConnectionType())) {

			if (probeService instanceof FileConnectorService) {
				FileSinkConnectorConfig config = (FileSinkConnectorConfig) fullSinkConfig(dataSink.getConnectionType(), probeInput);
				FileConnectorService fileConnectorService = (FileConnectorService) probeService;

				return uploadFileInternal(fileConnectorService, config, relativePath, file);
			} else {
				throw new IllegalArgumentException("Connection type " + dataSink.getConnectionType() + " does not support uploading files");
			}
		}
	}

	private FileDetails uploadFileInternal(FileConnectorService fileConnectorService, FileSinkConnectorConfig config, String relativeDstPath, File localFile) {
		String normDirPath = AWSAuthConfig.getDirectoryPath(config.sinkType, config.getPath());
		DateTime nowInUserTimeZone = DateTimeUtils.nowUTC().withZone(DateTimeZone.forID(config.timezone));
		String folderStructure = VarUtils.getFolderStructure(nowInUserTimeZone, Collections.emptyMap(), config.outputDirNamePattern.map(VarUtils.VarInfo::getTemplate), config.datetimePadding);
		String resultPath = Paths.get(normDirPath, folderStructure, relativeDstPath).toString();
		String destinationFilePath = AWSAuthConfig.normalizeFilePath(config.sinkType, resultPath);

		if (config.overwriteExistingFile) {
			return fileConnectorService.write(config, destinationFilePath, localFile);
		} else {
			return fileConnectorService.writeWithSuffixIfExists(config, destinationFilePath, localFile);
		}
	}

	private List<NexlaMessage> readProbeMessages(ProbeInput probeInput, ConnectorService probeService, AbstractConfig readConfig, Map<String, String> overriddenExtensions) {
		if (probeService instanceof FileConnectorService) {

			FileConnectorAuth fileAuth = (FileConnectorAuth) readConfig;
			String filePath = getFilePath(fileAuth.getConnectionType(), fileAuth.getPath());
			FileConnectorService fileConnectorService = (FileConnectorService) probeService;
			InputStream is = fileConnectorService.readInputStream(fileAuth, filePath);

			Result detection = detectParser(
				Optional.empty(), is, overriddenExtensions, probeInput.getParams(), filePath, null, logger);

			StreamEx<NexlaMessage> samples = detection.parser()
					.map(parser -> parser
							.parseMessages(detection.restoredStream())
							.filter(Optional::isPresent).map(Optional::get)
							.onClose(() -> closeSilently(is)))
					.getOrElse(() -> {
						closeSilently(is);
						return StreamEx.of(new NexlaMessage(lhm("result", "Not supported")));
					});

			try (samples) {
				return samples.toList();
			}
		} else if (probeService instanceof MessageReader) {
			return probeTunnel(readConfig, () -> ((MessageReader) probeService).readStream(readConfig).toList());
		} else {
			return Lists.newArrayList(new NexlaMessage(lhm("result", "Not supported")));
		}
	}

	public PaginatedResult<List<Object>> readQuarantine(Map<String, String> input) {
		return KafkaBackendReadService.readQuarantine(new KafkaBackendReadConfig(enrichWithConsumerRecordSizeProps(input)), bootstrapServers, sslContext);
	}

	private Map<String, String> enrichWithConsumerRecordSizeProps(Map<String, String> props) {
		Map<String, String> enriched = new HashMap<>(props);
		if (consumerMaxPartitionFetchBytes != null && !enriched.containsKey(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG)) {
			enriched.put(KafkaBackendReadConfig.MAX_PARTITION_FETCH_BYTES, String.valueOf(consumerMaxPartitionFetchBytes));
		}

		if (consumerFetchMaxBytes != null && !enriched.containsKey(ConsumerConfig.FETCH_MAX_BYTES_CONFIG)) {
			enriched.put(KafkaBackendReadConfig.FETCH_MAX_BYTES, String.valueOf(consumerMaxPartitionFetchBytes));
		}

		return enriched;
	}

	public ProbeReadOutput readKafkaSamples(ConnectionType connectionType, ProbeInput probeInput) {
		try {
			Map<String, String> configParams = enrichWithConsumerRecordSizeProps(probeInput.getAllParams(decryptKey, connectionType, nexlaCredsEnc, nexlaCredsEncIv));
			KafkaBackendReadConfig readConfig = new KafkaBackendReadConfig(configParams);
			StreamEx<NexlaMessage> result = KafkaBackendReadService.readSamples(readConfig, bootstrapServers, sslContext);

			try (StreamEx<NexlaMessage> messageStream = result) {
				return new ProbeReadOutput(JSON, messageStream.toList());
			} catch (Exception e) {
				logger.error("Failed to retrieve files, input={}", probeInput, e);
				return new ProbeReadOutput(JSON, asList(new NexlaMessage(lhm("result", "Failed to retrieve files"))));
			}
		} catch (Exception e) {
			logger.error("Error processing read request, connectionType={} probeInput={}", connectionType, probeInput, e);
			throw e;
		}
	}

	private String getExtension(ConnectionType connectionType, String file) {
		final String extension;
		if (connectionType.equals(BIGQUERY)) {
			extension = CSV;
		} else if (connectionType.equals(KAFKA) || connectionType.equals(CONFLUENT_KAFKA) || connectionType.equals(MYSQL) || connectionType.equals(POSTGRES) || connectionType.equals(ORACLE) || connectionType.equals(ORACLE_AUTONOMOUS) || connectionType.equals(REDSHIFT) || connectionType.equals(SQLSERVER) || connectionType.equals(AZURE_SYNAPSE) || connectionType.equals(HIVE) || connectionType.equals(PRESTO) || connectionType.equals(TERADATA) || connectionType.equals(NETSUITE_JDBC) || connectionType.equals(GCP_SPANNER) || connectionType.equals(DB2) || connectionType.equals(HANA_JDBC) || connectionType.equals(SYBASE) || connectionType.equals(AWS_ATHENA)) {
			extension = JSON;
		} else if (connectionType.equals(REST)) {
			extension = JSON;
		} else {
			extension = FilenameUtils.getExtension(file);
		}
		return extension;
	}

	@SneakyThrows
	public boolean checkWrite(ConnectionType connectionType, ProbeInput probeInput) {
		AbstractConfig sinkConfig = createSinkConfig(connectionType, probeInput.getAllParams(decryptKey, connectionType, nexlaCredsEnc, nexlaCredsEncIv));
		try (ConnectorService probeService = getProbeService(connectionType)) {
			AbstractConfig config = getConfig(probeInput, connectionType, probeService);
			return probeTunnel(sinkConfig, () -> probeService.checkWriteAccess(sinkConfig));
		}
	}

	/**
	 * Create hierarchy from the list of file names
	 */
	TreeMap<String, Object> treeify(List<NexlaFile> files) {
		TreeMap<String, Object> treeMap = new TreeMap<>();
		for (NexlaFile file : files) {
			String treeifyPath = file.getMetadata()
				.map(x -> x.remove(TREEIFY_PATH))
				.map(Object::toString)
				.orElse(file.getFullPath());

			String[] fileParts = treeifyPath.split("/");
			if (fileParts.length > 0 && "".equals(fileParts[0])) {
				fileParts[0] = "/";
			}
			TreeMap<String, Object> currentLocation = treeMap;

			for (int i = 0; i < fileParts.length; i++) {
				String filePart = fileParts[i];

				if (!currentLocation.containsKey(filePart)) {
					String type = FOLDER.name().toLowerCase();
					if (i == fileParts.length - 1) {
						type = file.getType().name().toLowerCase();
					}

					Map<String, Object> innerMap = new TreeMap<>();
					innerMap.put(TYPE, type);
					innerMap.put(VALUE, new TreeMap<>());

					int filePartIndex = i;
					file.getMetadata().ifPresent(meta -> {
						ofNullable(meta.get(DISPLAY_PATH))
							.ifPresent(dn -> {
								String namePart = dn.toString().split("/")[filePartIndex];
								innerMap.put(DISPLAY_NAME, namePart);
							});
					});

					if (ListingResourceType.FILE.name().equals(type.toUpperCase())) {
						innerMap.put(SIZE, file.getSize());
						innerMap.put(CREATED_ON, file.getCreatedAt());
						innerMap.put(UPDATED_ON, file.getLastModified());
						file.getMetadata()
							.ifPresent(meta -> {
								ofNullable(meta.get(ID)).ifPresent(dn -> innerMap.put(ID, dn));
								ofNullable(meta.get(MIME_TYPE)).ifPresent(dn -> innerMap.put(MIME_TYPE, dn));
								ofNullable(meta.get(METADATA_WEBURL)).ifPresent(dn -> innerMap.put(METADATA_WEBURL, dn));
								ofNullable(meta.get(METADATA_ACCESS)).ifPresent(dn -> innerMap.put(METADATA_ACCESS, dn));
							});
					}
					currentLocation.put(filePart, innerMap);
				}
				currentLocation = (TreeMap<String, Object>) ((TreeMap<String, Object>) currentLocation.get(filePart)).get(VALUE);
			}
		}
		addMeta(treeMap);
		logger.info(JsonUtils.toJsonString(treeMap));
		return treeMap;
	}

	private void addMeta(TreeMap<String, Object> treeMap) {
		TreeMap<Object, Object> metaValue = new TreeMap<>();
		metaValue.put(TYPE, META);
		metaValue.put(VALUE, new TreeMap<>());
		treeMap.put(META, metaValue);
	}

	private boolean containsAnyMarkerWord(String query) {
		for (String markerWord: DDL_MARKER_WORDS) {
			if (query.contains(markerWord)) {
				return true;
			}
		}
		return false;
	}

	private boolean isDDLQuery(AbstractConfig config) {
		try {
			if (config instanceof JdbcSourceConnectorConfig) {
				JdbcSourceConnectorConfig cfg = (JdbcSourceConnectorConfig) config;
				return cfg.query.map(q -> {
					String upperCaseQuery = q.toUpperCase();
					return containsAnyMarkerWord(upperCaseQuery);
				}).orElse(false);
			} else if (config instanceof BigQuerySourceConnectorConfig) {
				BigQuerySourceConnectorConfig cfg = (BigQuerySourceConnectorConfig) config;
				return cfg.query.map(q -> {
					String upperCaseQuery = q.toUpperCase();
					return containsAnyMarkerWord(upperCaseQuery);
				}).orElse(false);
			} else {
				logger.warn("Neither BigQuery nor JDBC config, assuming it's not a DDL query");
				return false;
			}
		} catch (Exception e) {
			logger.warn("Exception during config checking, assuming false");
			return false;
		}
	}

	@SneakyThrows
	public void createDestination(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			Map<String, String> allParams = probeInput.getAllParams(decryptKey, connectionType, nexlaCredsEnc, nexlaCredsEncIv);
			AbstractConfig sinkConfig = createSinkConfig(connectionType, allParams);
			Optional<Integer> sourceId = ofNullable(allParams.get("data_source_id"))
				.map(Integer::valueOf);
			probeTunnel(sinkConfig, () -> probeService.createDestination(sinkConfig, sourceId));
		}
	}

	@SneakyThrows
	public void alterTable(ConnectionType connectionType, ProbeInput probeInput) {
		try (ConnectorService probeService = getProbeService(connectionType)) {
			AbstractConfig sinkConfig = createSinkConfig(connectionType, probeInput.getAllParams(decryptKey, connectionType, nexlaCredsEnc, nexlaCredsEncIv));
			probeTunnel(sinkConfig, () -> probeService.alterTable(sinkConfig));
		}
	}

	private Map<String, Object> buildFromSample(ProbeSampleResult result) {
		return buildFromSample(result, Optional.empty());
	}

	private Map<String, Object> buildFromSample(
		ProbeSampleResult result,
		Optional<Integer> statusCode
	) {
		if (result.isEmpty()) {
			logger.error("File not found");
			return map(STATUS_CODE, NOT_FOUND.value(), ERROR_MESSAGE, NOT_FOUND.getReasonPhrase());
		} else if (result.isBinary()) {
			return map(STATUS_CODE, OK.value(), CONTENT_TYPE, BINARY.getContentType());
		} else {
			Map<String, Object> httpResultMap = result.httpResultMap();

            statusCode.ifPresent(sc -> httpResultMap.put(STATUS_CODE, sc));
			return httpResultMap;
		}
	}

	private Optional<Integer> countLines(Optional<Integer> count, ConnectionType connectionType) {
		if (!Objects.equals(connectionType, REST)) {
			return of(count.orElse(DEFAULT_SAMPLE_ROWS));
		}
		return count;
	}

	private void probeTunnel(AbstractConfig config, Runnable func) {
		if (config instanceof ConfigWithAuth) {
			BaseAuthConfig authConfig = ((ConfigWithAuth) config).authConfig();
			Optional<SshTunnel> tunnel = new ProbeTunnelSupport(authConfig).createTunnel(logger);
			try {
				func.run();
			} finally {
				tunnel.ifPresent(SshTunnel::close);
			}
		} else {
			func.run();
		}
	}

	private <T> T probeTunnel(AbstractConfig config, Supplier<T> func) {
		if (config instanceof ConfigWithAuth) {
			BaseAuthConfig authConfig = ((ConfigWithAuth) config).authConfig();
			Optional<SshTunnel> tunnel = new ProbeTunnelSupport(authConfig).createTunnel(logger);
			try {
				return func.get();
			} finally {
				tunnel.ifPresent(SshTunnel::close);
			}
		} else {
			return func.get();
		}
	}

	private BaseSinkTask createEmptySinkTask(ConnectionType connectionType, AdminApiClient adminApiClient) {
		BaseSinkTask sinkTask;
		if (connectionType.equals(REST)) {
			sinkTask = new ProbeRestSinkTask(adminApiClient);
		} else if (connectionType.equals(SOAP)) {
			sinkTask = new ProbeSoapSinkTask(adminApiClient);
		} else {
			throw new IllegalArgumentException(connectionType.toString());
		}
		
		sinkTask.initialize(new EmptySinkTaskContext());
		sinkTask.setRunId(System.currentTimeMillis());
		sinkTask.offsetsSender = Optional.empty();
		NexlaLogKey nexlaLogKey = new NexlaLogKey(SINK, -1, empty());
		sinkTask.setLogger(new NexlaLogger(LoggerFactory.getLogger(this.getClass()), nexlaLogKey));
		
		return sinkTask;
	}

	@AllArgsConstructor
	private class ProbeTunnelSupport implements SshTunnelSupport {

		BaseAuthConfig authConfig;

		@Override
		public Optional<BaseAuthConfig> authConfig() {
			return of(authConfig);
		}

		@Override
		public CredentialsStore credentialsStore() {
			return nexlaAppConfig.getStore();
		}

		public List<HostPort> getHostPorts() {
			if (authConfig instanceof JdbcAuthConfig) {
				JdbcAuthConfig cfg = ((JdbcAuthConfig) authConfig);
				if (cfg.url != null && !cfg.url.isEmpty() && (cfg.host == null || cfg.port == null)) {
					Optional<HostPort> hostPort = JdbcAuthConfig.extractHostPortFromUrl(cfg.getCredsId(), cfg.url);
					if (hostPort.isPresent()) {
						return Lists.newArrayList(hostPort.get());
					}
				}
				return Lists.newArrayList(new HostPort(cfg.host, cfg.port));
			} else if (authConfig instanceof JmsAuthConfig) {
				JmsAuthConfig jmsAuthConfig = ((JmsAuthConfig) authConfig);
				try {
					var uri = new URIBuilder(jmsAuthConfig.url);
					return Lists.newArrayList(new HostPort(uri.getHost(), uri.getPort()));
				} catch (Exception e) {
					var split = jmsAuthConfig.url.split(":");
					return Lists.newArrayList(new HostPort(split[0], Integer.valueOf(split[1])));
				}
			}
			return emptyList();
		}

	}

}
