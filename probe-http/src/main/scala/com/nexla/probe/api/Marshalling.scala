package com.nexla.probe.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import spray.json.DefaultJsonProtocol

object Marshalling
  extends SprayJsonSupport
    with DefaultJsonProtocol {

  implicit val BucketFormat = jsonFormat1(Bucket)

  implicit val ColumnInfoFormat = jsonFormat4(ColumnInfo)
  implicit val TableInfoFormat = jsonFormat2(TableInfo)
  implicit val DbInfoFormat = jsonFormat2(DbInfo)
  implicit val TopicsFormat = jsonFormat1(TopicsInfo)
  implicit val TopicsSubscriptionsFormat = jsonFormat2(TopicSubscriptionInfo)
  implicit val NexFileFormat = jsonFormat11(NexFile)

  implicit val WsdlParameterFormat = jsonFormat14(WsdlParameter)
  implicit val WsdlOperationFormat = jsonFormat2(WsdlOperation)
  implicit val WsdlBindingFormat = jsonFormat3(WsdlBinding)
  implicit val WsdlPortFormat = jsonFormat2(WsdlPort)
  implicit val WsdlServiceFormat = jsonFormat3(WsdlService)
  implicit val WsdlDefinitionFormat = jsonFormat1(WsdlDefinition)
}
