package com.nexla.probe

import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.amazonaws.services.s3.AmazonS3
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder, DataCredentials}
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.{AppType, NexlaConstants, NexlaDataCredentials, NexlaSslContext, RestTemplateBuilder}
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.kafka.{AdminControlUpdatesListener, CtrlTopics, KafkaMessageTransport}
import com.nexla.listing.client.ListingClient
import com.nexla.probe.api.ray.RayDryRunner
import com.nexla.probe.api.{<PERSON><PERSON><PERSON><PERSON><PERSON>, FileStreaming<PERSON>pi, ProbeApiHandler, ProbeService, ScalaEvalHandler}
import com.nexla.probe.custom.ray.RayApiClientImpl
import com.nexla.probe.ftp.FileConnectorServiceBuilder
import com.nexla.probe.s3.S3ConnectorService
import com.nexla.sc.client.JitScriptEvalClient
import com.nexla.sc.util.{AppUtils, Async, StrictNexlaLogging}
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsRoute
import org.slf4j.bridge.SLF4JBridgeHandler

import java.util.{Optional, UUID}
import scala.compat.java8.OptionConverters._
import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

object ProbeApp
  extends App
    with AppUtils
    with StrictNexlaLogging {

  // Needed to redirect java.util.logging to slf4j
  SLF4JBridgeHandler.removeHandlersForRootLogger
  SLF4JBridgeHandler.install

  private val (props, nexlaAppConfig, envMap) = loadProps(AppType.PROBE, new AppProps(_))
  val (telemetry, httpMetricsRegistry) = initTelemetry(props.telemetryConf, AppType.PROBE.appName, Some(props.dataDog))

  implicit val system = defaultActorSystem(logger)
  implicit val materializer = Materializer(system)
  implicit val executionContext: ExecutionContext = Async.ioExecutorContext

  implicit val appSslContext: NexlaSslContext = nexlaSslContext(props)

  private val messageProducer = new NexlaMessageProducer(new KafkaMessageTransport(props.bootstrapServers, appSslContext, NexlaConstants.TOPIC_METRICS, NexlaConstants.TOPIC_NOTIFY))
  private val metricAggregator = HealthMetricAggregator.getInstance(messageProducer)

  val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()

  private val adminApi = new AdminApiClientBuilder()
    .setAppName(AppType.PROBE.appName)
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.credEnrichmentUrl)
    .setTelemetry(Optional.of(telemetry))
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  val listingClient = new ListingClient(props.listingAppServer, props.nexlaCreds.username, props.nexlaCreds.password, restTemplate)

  val rayApi = new RayApiClientImpl(props.rayApiServer, props.nexlaCreds)
  val imcDryRunHelper = new RayDryRunner(adminApi, listingClient, rayApi, props)

  val ctrlTopics = new CtrlTopics(Some(UUID.randomUUID()), props, appSslContext, system)
  val scriptEvalClient = new JitScriptEvalClient()
  val fileConnectorServiceBuilder = new FileConnectorServiceBuilder(adminApi, listingClient, props.decryptKey)

  private def initApp(props: AppProps,
                      adminApi: AdminApiClient, fileConnectorServiceBuilder: FileConnectorServiceBuilder) = {

    val oldController = new ProbeController(
      adminApi,
      listingClient,
      props,
      nexlaAppConfig,
      appSslContext,
      scriptEvalClient,
      fileConnectorServiceBuilder
    )

    val probeService = new ProbeService(props.nexlaCredsEncoded, props.decryptKey)

    val mezzanineCreds: Option[DataCredentials] = props.imcMezzanineCredsId.map(credsId => adminApi.getDataCredentials(credsId).get())
    val mezzanineS3Client: Option[AmazonS3] = mezzanineCreds.map { presentCreds =>
      S3ConnectorService.createS3ClientFromCreds(
        new AWSAuthConfig(NexlaDataCredentials.getCreds(props.decryptKey, presentCreds.getCredentialsEnc, presentCreds.getCredentialsEncIv), presentCreds.getId), null)
    }

    val probeApiHandler = new ProbeApiHandler(oldController, probeService, props.featureFlagJsonIncludeNull, imcDryRunHelper, adminApi, mezzanineS3Client ,props)
    val fileStreamingApi = new FileStreamingApi(oldController, adminApi)
    val scalaEvalHandler = new ScalaEvalHandler()

    val api = new ApiHandler(props.nexlaCreds, probeApiHandler, fileStreamingApi, scalaEvalHandler, httpMetricsRegistry, envMap)
    configureHttpClientSslContext(appSslContext)

    val httpSystem = Http(system)

    val context = appSslContext
      .getServerKeystoreStore()
      .asScala
      .map(sslContext => httpsContext(sslContext, appSslContext.getServerTruststoreStore.orElse(null)))
      .getOrElse(httpSystem.defaultServerHttpContext)

    val binding = httpSystem.bindAndHandle(api.route, "0.0.0.0", props.nexlaAppPort, context)
    appSslContext.getServerKeystoreStore().asScala.foreach(_.clean())
    binding.andThen {
      case Success(Http.ServerBinding(_)) =>

        new AdminControlUpdatesListener(AppType.PROBE, ctrlTopics, metricAggregator).startMonitoring()

        logger.info(s"API server started on ${props.podIp}:${props.nexlaAppPort}")

      case Failure(e) =>
        logger.error(s"Could not start API server on ${props.nexlaAppPort}. Exiting...", e)
        System.exit(1)
    }

    httpMetricsRegistry.map { actualReg =>
        val httpMetricsRoute = HttpMetricsRoute(api.metricsRoute).recordMetrics(actualReg)

        val metricsBinding = Http().bindAndHandle(httpMetricsRoute, "0.0.0.0", props.nexlaMetricsPort)

        metricsBinding.andThen {
          case Success(Http.ServerBinding(_)) =>
            logger.info(s"Metrics server started on ${props.podIp}:${props.nexlaMetricsPort}")
          case Failure(e) =>
            logger.error(s"Could not start Metrics server on ${props.nexlaMetricsPort}.", e)
        }
    }
  }

  initApp(props, adminApi, fileConnectorServiceBuilder)

}
