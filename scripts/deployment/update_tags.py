import os
import re
import shutil
import sys

projects_info = """
http-sink	http-sink		3.2.0-SNAPSHOT_release-v3.1.0_674_5b085de80f8d918c3e20a75fcf6b08dfe6e1ef06
kafka-connect-bigquery-sink	sink-bigquery-connector-blue-1	y	3.2.0-SNAPSHOT_release-v3.1.0_707_c542bfbce3477169cd4a4cb8296d115bf5106752
ingestion-service	ingestion		
"""

def main(env, helm_charts_repo_location):
    # capture everything before the tag for replacement with \g<1>{tag}
    sink_tag_pattern = re.compile(r'^(\s*tag:\s*).+$', re.MULTILINE)
    working_dir = os.path.expanduser(f"{helm_charts_repo_location}/Values/saas/{env}")
    os.chdir(working_dir)
    print(f"updating files at {working_dir}")

    def project_to_control_tag(project):
        return "TAG_" + project.upper().replace("-", "_")

    def copy_to_swapped_color_file(file_name):
        color_transitions = {'blue': 'green', 'green': 'blue'}
        old_color = next(color for color in color_transitions if color in file_name)
        new_color = color_transitions[old_color]
        new_file_name = file_name.replace(old_color, new_color)
        shutil.copyfile(file_name, new_file_name)
        # capture everything before and after the color ()blue() so it's easier to replace.
        color_patterns = [
            re.compile(rf'(^fullnameOverride: (?:\w+-)+){old_color}(-\d+$)', re.MULTILINE),
            re.compile(rf'(^\s+NODE_ID: (?:\w+-)+){old_color}(-\d+$)', re.MULTILINE)
        ]

        with open(file_name, 'r') as file:
            content = file.read()

        for pattern in color_patterns:
            content = pattern.sub(rf'\1{new_color}\2', content, count=1)

        with open(new_file_name, 'w') as file:
            file.write(content)
        print(f"Copied {file_name} to {new_file_name} and replaced {old_color} with {new_color}.")
        return new_file_name

    control_tag_replacements = {}

    for project_info in projects_info.split("\n"):
        parts = project_info.split("	")
        if len(parts) != 4 or parts[0] == "" or parts[3] == "":
            print(f"no project name or tags for row {parts}")
            continue
        control_tag_replacements[project_to_control_tag(parts[0])] = parts[3]
        if parts[0].split("-")[-1] == "source":
            continue
        elif parts[1] == "":
            print(f"neither a source, nor do we know where to deploy {parts}")
            continue

        file_names = [name + ".yaml" for name in parts[1].split(",")]
        tag = parts[3]
        for file_name in file_names:
            if not os.path.isfile(file_name):
                print(f"File '{file_name}' does not exist in '{working_dir}'. Please only specify currently existing files.")
                continue
            try:
                if parts[2].strip() == "y":
                    # stateful sink, copy -green-\d+ to -blue-\d+ and vice versa
                    file_name = copy_to_swapped_color_file(file_name)

                with open(file_name, 'r') as file:
                    content = file.read()
                content = sink_tag_pattern.sub(rf"\g<1>{tag}", content, count=1)
                with open(file_name, 'w') as file:
                    file.write(content)
            except Exception as e:
                print(f"Error processing '{file_name}': {e}.")
                continue

    control_files = ["ctrl-listeners.yaml", "ctrl-listeners-jobscheduler.yaml"]
    for control_file in control_files:
        with open(control_file, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        for idx, line in enumerate(lines):
            try:
                property = line.split(":")[0].strip()
                if property in control_tag_replacements:
                    lines[idx] = line.split(":")[0] + ": " + control_tag_replacements[property] + "\n"
            except AttributeError as ignore:
                continue
        with open(control_file, 'w', encoding='utf-8') as file:
            file.writelines(lines)

if __name__ == "__main__":
    if len(sys.argv) == 3:
        env = sys.argv[1]
        helm_charts_repo_location = sys.argv[2]
    elif len(sys.argv) == 2:
        env = sys.argv[1]
        helm_charts_repo_location = "~/workspace/nexla-helm-charts"
        print("no <helm_charts_repo_location> argument passed, assuming ~/workspace/nexla-helm-charts")
    else:
        env = "development"
        print("no <env> argument passed, assuming development")
        helm_charts_repo_location = "~/workspace/nexla-helm-charts"
        print("no <helm_charts_repo_location> argument passed, assuming ~/workspace/nexla-helm-charts")
    main(env, helm_charts_repo_location)
