package com.nexla.connector.soap.sink;

import com.github.tomakehurst.wiremock.core.Options;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.github.tomakehurst.wiremock.matching.EqualToXmlPattern;
import com.google.common.collect.Lists;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.PDataset;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.PDataset;
import com.nexla.admin.client.pipeline.PTransform;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.junit.*;
import org.testcontainers.containers.KafkaContainer;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.ClassRule;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.Optional;
import java.util.*;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.nexla.common.NexlaConstants.*;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToXml;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.nexla.common.ConnectionType.REST;
import static com.nexla.common.NexlaConstants.CREDENTIALS_DECRYPT_KEY;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.FAST_MODE;
import static com.nexla.connector.config.soap.SoapWsdlConfig.*;
import static com.nexla.connector.properties.RestConfigAccessor.*;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Collections.singletonList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Category(IntegrationTests.class)
public class SoapSinkTaskTest extends BaseKafkaTest {

	public static KafkaContainer kafka = new KafkaContainer("7.2.11");

	@BeforeClass
	public static void setUp() {
		kafka.withReuse(true);
		kafka.start();
		init(kafka);
	}

	@After
	@SneakyThrows
	public void after() {
		if (task != null && task.getNexlaMessageProducer() != null) {
			task.getNexlaMessageProducer().close();
		}
	}

	@AfterClass
	public static void tearDown() {
		kafka.stop();
	}

	private static final HashMap<String, String> BASE_PARAMS = new HashMap<String, String>() {{
		put(UNIT_TEST, "true");
		put(AUTH_TYPE, AuthType.NONE.name());

		put(CREDS_ENC, "1");
		put(CREDS_ENC_IV, "1");
		put(CREDENTIALS_DECRYPT_KEY, "1");

		put(SINK_ID, "1");
		put(FAST_MODE, "true"); // disable listing calls
	}};

	@ClassRule
	public static WireMockRule wireMockServer = new WireMockRule(Options.DYNAMIC_PORT);

	private SoapSinkTask task;

	@Before
	public void onBefore() {
		AdminApiClient adminApiClient = mock(AdminApiClient.class);
		AdminApiClientBuilder.INSTANCE = adminApiClient;
		List<PDataset> dataSets = List.of(new PDataset(24, 32, null));
		PDataSource ds = new PDataSource();
		DataSink sink = new DataSink();
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(new Pipeline(dataSets, ds, sink)));

		DataSink dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.MYSQL);
		dataSink.setSinkConfig(java.util.Map.of("parallelism", "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);

		PDataSource pd = new PDataSource();
		pd.setId(2);
		pd.setConnectionType(ConnectionType.SOAP);
		pd.setCredentialsType("none");

		PTransform ptr = new PTransform(Collections.emptyList());
		PDataset ps = new PDataset(1, 2, ptr);

		when(adminApiClient.getPipeline(any(Integer.class))).thenReturn(Optional.of(
				new Pipeline(List.of(ps), pd, dataSink)));

		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(dataSink));

		wireMockServer.resetAll();
		this.task = new SoapSinkTask();
		task.initialize(mock(SinkTaskContext.class));
	}

	@Test
	@SneakyThrows
	@Ignore //TODO Fix it
	public void put_soap() {

		String request = IOUtils.toString(getClass().getClassLoader().getResource("./soap-request.xml").toURI(), UTF_8);

		String response = IOUtils.toString(getClass().getClassLoader().getResource("./soap-result.xml").toURI(), UTF_8);

		stubFor(
			post(urlEqualTo("/soap-endpoint"))
				.withRequestBody(new EqualToXmlPattern(request))
				.willReturn(aResponse()
					.withStatus(200)
					.withHeader("Content-Type", "application/xml")
					.withBody(response)));

		String wsdl = IOUtils.toString(
			getClass().getClassLoader().getResource("./soap-wsdl.wsdl").toURI(),
			Charset.defaultCharset()
		).replace("###WS_ADDRESS###", "http://localhost:" + wireMockServer.port() + "/soap-endpoint");

		stubFor(
			get(urlEqualTo("/soap-wsdl"))
				.willReturn(aResponse()
					.withStatus(200)
					.withHeader("Content-Type", "application/xml")
					.withBody(wsdl)));

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("TrackRequest/WebAuthenticationDetail/UserCredential/Key", "4xxxxxxxxxxxxxxxy");
		data.put("TrackRequest/WebAuthenticationDetail/UserCredential/Password", "3xxxxxxxxxxxxxxxW");
		data.put("TrackRequest/ClientDetail/AccountNumber", "*********");
		data.put("TrackRequest/ClientDetail/MeterNumber", "*********");
		data.put("TrackRequest/TransactionDetail/CustomerTransactionId", "Track By Number_v16");
		data.put("TrackRequest/TransactionDetail/Localization/LanguageCode", "EN");
		data.put("TrackRequest/TransactionDetail/Localization/LocaleCode", "US");
		data.put("TrackRequest/SelectionDetails/PackageIdentifier/Type", "TRACKING_NUMBER_OR_DOORTAG");
		data.put("TrackRequest/SelectionDetails/PackageIdentifier/Value", "************");
		data.put("TrackRequest/SelectionDetails/Destination/GeographicCoordinates", "rates evertitque");

		HashMap<String, String> baseProps = new HashMap<String, String>(BASE_PARAMS) {{
			put(BOOTSTRAP_SERVERS, BaseKafkaTest.BOOTSTRAP_SERVERS);
			put(METHOD, "POST");
			put(SOAP_WSDL_URL, "http://localhost:" + wireMockServer.port() + "/soap-wsdl");
			put(SOAP_PORT_TYPE, "TrackPortType");
			put(SOAP_OPERATION, "track");
			put(SOAP_BINDING, "TrackServiceSoapBinding");
			put(SOAP_SERVICE_PORT, "TrackServicePort");
			put(SOAP_SERVICE, "TrackService");
			put(POLL_MS, "1");
		}};
		// how did it appear here?
		baseProps.remove(null);
		// how?..
		baseProps.remove(null, null);

		task.start(baseProps);

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlEqualTo("/soap-endpoint"))
			.withRequestBody(equalToXml(request)));

	}

	private static NexlaMetaData metadata() {
		NexlaMetaData meta = new NexlaMetaData();
		meta.setRunId(1L);
		return meta;
	}

}