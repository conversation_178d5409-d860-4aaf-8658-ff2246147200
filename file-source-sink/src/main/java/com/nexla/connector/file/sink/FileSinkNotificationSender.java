package com.nexla.connector.file.sink;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.OwnerAndOrg;
import com.nexla.common.NexlaMessage;
import com.nexla.common.Resource;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.exception.NexlaErrorMessage;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.connect.common.NexlaConnectorUtils;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import lombok.AllArgsConstructor;

import java.util.Optional;

import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.connect.common.NexlaConnectorUtils.publishException;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

@AllArgsConstructor
public class FileSinkNotificationSender {

	private final FileSinkConnectorConfig config;
	private final NexlaMessageProducer nexlaMessageProducer;

	public void publishMetrics(String fileName, Optional<String> displayName, long numRecords, long rawBytes,
							   long errorCount, Optional<Long> runId, Optional<DataSet> dataset, Optional<NexlaErrorMessage> errorMessage) {
		NexlaConnectorUtils.publishMetrics(nexlaMessageProducer, SINK, config.sinkId, fileName, numRecords, rawBytes,
			errorCount, DateTimeUtils.nowUTC().getMillis(), runId, empty(), empty(), displayName,
			dataset.map(DataSet::getId), empty(), empty(), errorMessage, empty(), FlowType.STREAMING,
				dataset.map(OwnerAndOrg::getOrgId).orElse(null), dataset.map(OwnerAndOrg::getOwnerId).orElse(null));
	}

	public void publishExceptionMessage(String fileName, long numRecords, Throwable exp, long errorCount, Long runId) {
		publishException(nexlaMessageProducer, runId, SINK, config.sinkId, numRecords, fileName, getErrorDetails(exp, ofNullable(errorCount)));
	}

	public void sendQuarantineMessage(Resource resource, NexlaMessage message, Throwable exp) {
		nexlaMessageProducer.sendQuarantineMessage(resource, message, exp);
	}

	protected void publishMonitoringMessage(String log,
											Integer orgId,
											Long runId,
											NexlaMonitoringLogType logType,
											NexlaMonitoringLogSeverity severity) {
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				orgId,
				runId,
				config.sinkId,
				SINK,
				log,
				logType,
				severity,
				System.currentTimeMillis());
		nexlaMessageProducer.publishMonitoringLog(monitoringLogEvent);
	}

}
