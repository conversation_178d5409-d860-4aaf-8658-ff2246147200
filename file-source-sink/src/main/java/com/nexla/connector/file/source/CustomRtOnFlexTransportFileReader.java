package com.nexla.connector.file.source;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.model.UploadResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.NexlaFile;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.ResourceType;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.parse.NexlaParser;
import com.nexla.connect.common.NexlaConnectorUtils;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.CustomRtMode;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.control.ListingFileStatus;
import com.nexla.file.service.FileConnectorService;
import com.nexla.listing.client.ListingClient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.zip.GZIPInputStream;

import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.probe.s3.S3ConnectorService.createS3ClientFromCreds;

public class CustomRtOnFlexTransportFileReader extends TransportFileReader {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomRtOnFlexTransportFileReader.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static final String PROP_ID = "id";
    public static final String PROP_FILE_NAME = "file_name";
    public static final String PROP_FILE_PATH = "file_path";
    public static final String PROP_ORIGINAL_FILE_PATH = "original_file_path";
    public static final String PROP_SIZE = "size";

    private static final long MULTIPART_UPLOAD_THRESHOLD_BYTES = 1L << 28;
    private static final long MINIMUM_UPLOAD_PART_SIZE_BYTES = 1L << 27;

    /*
    Only one of these will be set, depending on the configuration.
     */
    private final AWSAuthConfig.BucketPrefix uploadBase;
    private final CustomRtMode mode;

    private final List<TransportFile> processedFiles = new ArrayList<>();

    public CustomRtOnFlexTransportFileReader(FileSourceNotificationSender notificationSender, FileConnectorService probeService, SchemaDetectionUtils schemaDetection, FileSourceOffsetWriter offsetWriter, MessageGrouper messageGrouper, NexlaLogger logger, Optional<OffsetStorageReader> offsetReader, Optional<ListingClient> listingClient, Optional<Consumer<TransportFile>> onFileConsumed, FileSourceContext fileSourceContext, Optional<Runnable> heartbeatCallback, boolean listingEnabled, CustomRtMode mode) {
        super(notificationSender, probeService, schemaDetection, offsetWriter, messageGrouper, logger, offsetReader, listingClient, onFileConsumed, fileSourceContext, heartbeatCallback, listingEnabled);

        FileSourceConnectorConfig fscc = this.fileSourceContext.config;

        uploadBase = createUploadBase(fileSourceContext, fscc);

        this.mode = mode;

        this.firstTime.set(false);
    }

    protected AWSAuthConfig.BucketPrefix createUploadBase(FileSourceContext fileSourceContext,
                                                          FileSourceConnectorConfig fscc) {
        return toBucketPrefix(fscc.customRtDestination + "/" + fscc.sourceId + "/" + DateTime.now().toString("yyyy-MM-dd") + "/pre-processed/" + fileSourceContext.runId, true);
    }

    @SneakyThrows
    @Override
    protected void initTransportFile(TransportFile transportFile, AdminApiClient adminApiClient, boolean isNoopMessageConsumer) {
        transportFile.ctx.nexlaFile = transportFile.nexlaFile;
        transportFile.ctx.skippingParserErrors = true;
        transportFile.ctx.messageNumber = transportFile.lastMessageNumber;

        transportFile.ctx.state = ReadingContext.ReadingContextState.INITIALIZED;

        detectParserIfNeeded(transportFile.ctx, adminApiClient);
    }

    private void detectParserIfNeeded(ReadingContext ctx, AdminApiClient adminApiClient) throws IOException {
        if (this.mode == CustomRtMode.PARSING) {
            getNexlaParser(ctx, adminApiClient)
                    .ifPresent(parser -> {
                        logger.info("Detected parser: {}", parser.getClass().getSimpleName());
                        ctx.parser = parser;
                    });
        }
    }

    @Data
    @AllArgsConstructor
    private static class FileWithName {
        final File file;
        final String fullPath;
    }

    @SneakyThrows
    @Override
    protected <T> List<T> doReadNextBatch(TransportFile tf, FileReadResult<T> messageConsumer) {
        this.processedFiles.add(tf);

        ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(() -> heartbeatFile(tf.nexlaFile), 0, 1, TimeUnit.MINUTES);
        try {
            if (this.mode == CustomRtMode.PARSING) {
                return doReadNextBatchParsing(tf, messageConsumer);
            } else {
                return doReadNextBatchReplication(tf, messageConsumer);
            }
        } catch (Throwable e) {
            logger.error("Error processing file: {}", tf.nexlaFile.getFullPath(), e);

            this.markFileAsErrored(tf, e.getMessage());

            this.finishReading(tf.ctx, false /* ignored */);

            return messageConsumer.removeResult();
        } finally {
            heartbeat.cancel(false);
        }
    }

    private <T> List<T> doReadNextBatchReplication(TransportFile tf, FileReadResult<T> messageConsumer) {
        Optional<FileWithName> optFileWithExtension = extractIfNeeded(tf);

        FileWithName file = optFileWithExtension
                .orElse(new FileWithName(tf.ctx.localFile, tf.nexlaFile.getFullPath()));

        Uploaded upload = upload(
                file.getFile(),
                file.getFullPath()
        );

        optFileWithExtension
                .ifPresent(f -> {
                    try {
                        Files.delete(f.getFile().toPath());
                    } catch (IOException e) {
                        logger.error("Error deleting file: {}", f.getFile(), e);
                    }
                });

        messageConsumer.acceptMessage(
                tf.ctx.schema,
                tf.ctx,
                toNexlaMessage(upload, tf.nexlaFile),
                true,
                1,
                new HashMap<>()
        );

        finishReading(tf.ctx, false /* ignored */);

        return messageConsumer.removeResult();
    }

    @SneakyThrows
    private Optional<FileWithName> extractIfNeeded(TransportFile tf) {
        if (!isGz(tf.ctx.localFile)) {
            return Optional.empty();
        }

        File extracted = File.createTempFile("extracted", ".tmp");

        try (GZIPInputStream gzip = new GZIPInputStream(new FileInputStream(tf.ctx.localFile));
             FileOutputStream out = new FileOutputStream(extracted)) {
            IOUtils.copy(gzip, out);
        }

        return Optional.of(new FileWithName(extracted, Path.of(
                new File(tf.nexlaFile.getFullPath()).getParent(),
                FilenameUtils.getBaseName(tf.nexlaFile.getFullPath())
        ).toString()));
    }

    private static boolean isGz(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] signature = new byte[2];
            int read = fis.read(signature);
            return read == 2 && signature[0] == (byte) 0x1F && signature[1] == (byte) 0x8B;
        } catch (IOException e) {
            return false;
        }
    }

    @SneakyThrows
    private <T> List<T> doReadNextBatchParsing(TransportFile tf, FileReadResult<T> messageConsumer) {
        Optional<FileWithName> optFile = parseToFile(tf);

        if (optFile.isEmpty()) {
            return Collections.emptyList();
        }

        FileWithName fileWithName = optFile.get();

        Uploaded upload = upload(
                fileWithName.getFile(),
                fileWithName.getFullPath()
        );

        Files.delete(fileWithName.getFile().toPath());

        messageConsumer.acceptMessage(
                tf.ctx.schema,
                tf.ctx,
                toNexlaMessage(upload, tf.nexlaFile),
                true,
                1,
                new HashMap<>()
        );

        finishReading(tf.ctx, false /* ignored */);

        return messageConsumer.removeResult();
    }

    private Optional<FileWithName> parseToFile(TransportFile tf) throws IOException {
        if (tf.ctx.parser == null) {
            logger.info("Can't parse file: {}, parser is not initialized", tf.nexlaFile.getFullPath());
            return Optional.empty();
        }

        NexlaParser parser = tf.ctx.parser;
        StreamEx<Optional<NexlaMessage>> optMessages = parser.parseMessages(() -> {
            try {
                return Files.newInputStream(tf.ctx.localFile.toPath());
            } catch (IOException e) {
                logger.error("Error reading file: {}", tf.ctx.localFile, e);
                throw new RuntimeException(e);
            }
        }, true);

        try (optMessages) {
            List<Optional<NexlaMessage>> optMessagesList = optMessages.toList();

            List<NexlaMessage> messages = new ArrayList<>();
            for (int offset = 0; offset < optMessagesList.size(); offset++) {
                Optional<NexlaMessage> optMessage = optMessagesList.get(offset);
                if (optMessage.isPresent()) {
                    NexlaMessage message = optMessage.get();
                    message.setNexlaMetaData(createNexlaMetaData(tf, offset, this.fileSourceContext.config));
                    messages.add(message);
                }
            }

            logger.info("Parsed messages: {}", messages.size());

            File parsed = File.createTempFile("parsed", ".json");

            OBJECT_MAPPER.writeValue(parsed, messages);

            String baseName = FilenameUtils.getBaseName(tf.nexlaFile.getFullPath()) + "_" + FilenameUtils.getExtension(tf.nexlaFile.getFullPath());
            String extension = "json";
            String path = new File(tf.nexlaFile.getFullPath()).getParent();

            return Optional.of(new FileWithName(parsed, Path.of(path, baseName + "." + extension).toString()));
        }
    }

    private static NexlaMetaData createNexlaMetaData(TransportFile tf, long offset, FileSourceConnectorConfig fscc) {
        NexlaMetaData metadata = new NexlaMetaData(
                fscc.sourceType,
                System.currentTimeMillis(),
                offset,
                tf.nexlaFile.getFullPath(),
                null,
                ResourceType.SOURCE, fscc.sourceId,
                true,
                null, null
        );

        tf.nexlaFile.getMetadata().ifPresent((fileTags) -> {
            metadata.setTags(new LinkedHashMap<>(fileTags));
        });

        metadata.setLastModified(tf.nexlaFile.getLastModified());

        return metadata;
    }

    private Uploaded upload(File localFile, String fullPath) {
        AmazonS3 s3Client = createS3Client();

        TransferManager transferManager = TransferManagerBuilder.standard()
                .withS3Client(s3Client)
                .withMultipartUploadThreshold(MULTIPART_UPLOAD_THRESHOLD_BYTES)
                .withMinimumUploadPartSize(MINIMUM_UPLOAD_PART_SIZE_BYTES)
                .build();

        try {
            String uploadFullPath = toUploadPath(fullPath);
            LOGGER.info("Uploading file: {}", uploadFullPath);

            UploadResult putObjectResult = transferManager
                    .upload(destinationBucket(), uploadFullPath, localFile)
                    .waitForUploadResult();

            LOGGER.info("Uploaded file: {}", putObjectResult);

            return new Uploaded(uploadFullPath);
        } catch (Throwable e) {
            LOGGER.error("Error uploading file to S3", e);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            transferManager.shutdownNow();

            s3Client.shutdown();
        }
    }

    private String destinationBucket() {
        return uploadBase.bucket;
    }

    protected String toUploadPath(String fullPath) {
        return Path.of(uploadBase.prefix, fullPath).toString();
    }

    protected AmazonS3 createS3Client() {
        return createS3ClientFromCreds(this.fileSourceContext.config.customRtAuthConfig, this.fileSourceContext.config.customRtAuthConfig.region);
    }

    private void markFileAsErrored(TransportFile tf, String errorMessage) {
        try {
            listingClient.filter(x -> listingEnabled).ifPresent(client -> {
                logger.info("Marking file as errored: {}", tf.nexlaFile.getFullPath());
                client.setFileStatus(
                        fileSourceContext.config.sourceId,
                        tf.nexlaFile.getId(),
                        ListingFileStatus.DONE,
                        Optional.empty(),
                        Optional.of(false),
                        Optional.of(errorMessage)
                );
            });

            NexlaConnectorUtils.publishMetrics(
                    notificationSender.nexlaMessageProducer,
                    ResourceType.SOURCE,
                    fileSourceContext.config.sourceId,
                    tf.nexlaFile.getFullPath(),
                    0,
                    0,
                    0,
                    DateTimeUtils.nowUTC().getMillis(),
                    Optional.ofNullable(fileSourceContext.runId),
                    Optional.of(true),
                    Optional.ofNullable(tf.nexlaFile.getMd5()),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.ofNullable(tf.nexlaFile.getLastModified()),
                    Optional.of(0L),
                    Optional.empty(),
                    Optional.of(tf.nexlaFile.getFullPath()),
                    FlowType.CUSTOM,
                    fileSourceContext.orgId,
                    fileSourceContext.ownerId
            );
        } catch (Exception e) {
            logger.error("Error marking file as errored: {}", e.getMessage(), e);
        }
    }

    @Override
    protected void finishReading(ReadingContext ctx, boolean done) {
        fileLock.lock();
        try {
            TransportFile tf = fileObjects.poll();
            onFileConsumed.ifPresent(c -> c.accept(tf));

            logger.info("[{}] FULL FILE TIME={}, path={}", ctx.nexlaFile.getId(), ctx.timer, ctx.nexlaFile.getFullPath());
            ctx.close();
        } finally {
            fileLock.unlock();
        }
    }

    private NexlaMessage toNexlaMessage(Uploaded uploaded, NexlaFile file) {
        logger.info("Uploaded file: {}, original: {}, path: {}, size: {}", uploaded, file.getId(), file.getFullPath(), file.getSize());

        return new NexlaMessage(new LinkedHashMap<>() {{
            put(PROP_ID, file.getId());
            put(PROP_FILE_NAME, new File(uploaded.relativePath).getName());
            put(PROP_FILE_PATH, destinationBucket() + "/" + uploaded.relativePath);
            put(PROP_ORIGINAL_FILE_PATH, file.getFullPath());
            put(PROP_SIZE, file.getSize());
        }});
    }

    @Data
    private static class Uploaded {
        final String relativePath;
    }
}
