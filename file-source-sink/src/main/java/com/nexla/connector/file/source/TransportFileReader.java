package com.nexla.connector.file.source;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.collect.Iterators;
import com.google.common.collect.Maps;
import com.google.common.io.Files;
import com.nexla.FileDescription;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.*;
import com.nexla.common.exception.ParseError;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.io.SourceAwareInputStream;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.Metric;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.parse.MultiPartDocument;
import com.nexla.common.parse.NexlaParser;
import com.nexla.connect.common.NexlaConnectorUtils;
import com.nexla.connect.common.SchemaDetectionResult;
import com.nexla.connect.common.connector.schema.DataSetService;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.file.GroupingProps;
import com.nexla.control.ListingFileStatus;
import com.nexla.file.service.FileConnectorService;
import com.nexla.listing.client.ListingClient;
import com.nexla.listing.client.ListingResult;
import com.nexla.parser.DelimitedTextParser;
import com.nexla.parser.ParserUtils;
import com.nexla.parser.ParserUtilsExt;
import com.nexla.parser.SchemaAware;
import java.util.concurrent.atomic.AtomicBoolean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.storage.OffsetStorageReader;

import java.io.*;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.nexla.common.ConfigUtils.optBoolean;
import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.NexlaConstants.DATASET_CUSTOM_NAME;
import static com.nexla.common.NexlaMetaData.createSourceMetadata;
import static com.nexla.common.NexlaNamingUtils.nameDataSetTopic;
import static com.nexla.common.NotificationEventType.EMPTY_DATA;
import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.StreamUtils.zipWithIndices;
import static com.nexla.common.datetime.DateTimeUtils.timed;
import static com.nexla.common.parse.ParserConfigs.Zip.ZIP_PASSWORD;
import static com.nexla.common.probe.ProbeControllerConstants.DISPLAY_PATH;
import static com.nexla.common.probe.ProbeControllerConstants.MIME_TYPE;
import static com.nexla.connect.common.NexlaConnectorUtils.sendNexlaNotificationEvent;
import static com.nexla.connector.file.source.OffsetUtils.*;
import static com.nexla.connector.file.source.ReadingContext.ReadingContextState.*;
import static com.nexla.connector.properties.FileConfigAccessor.OFFSET_POSITION_KEY;
import static com.nexla.connector.properties.FileConfigAccessor.PARTITION_KEY;
import static com.nexla.control.ListingFileStatus.DONE;
import static com.nexla.control.ListingFileStatus.STOPPED;
import static com.nexla.parser.ParserUtilsExt.detectParser;
import static java.nio.file.Files.createTempDirectory;
import static java.util.Collections.*;
import static java.util.Optional.*;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.io.FileUtils.deleteDirectory;
import static org.apache.commons.io.FileUtils.listFiles;

public class TransportFileReader implements ITransportFileReader {

	public static final String ARCHIVE_CONTENT_DELIMITER = ";";

	protected static ScheduledExecutorService SCHEDULED_POOL = Executors.newScheduledThreadPool(1);
	private Map<String, File> PATH_TO_ARCHIVE_DIR = Collections.synchronizedMap(Maps.newHashMap());

	public static final String SKIP_KEY = "skip";
	public static final String ERROR_KEY = "error";

	private static final int ONE_MB = 1024 * 1024;
	public static final int MAX_ERROR_MESSAGES_TO_LOG = 5;

	protected final ReentrantLock fileLock = new ReentrantLock();

	protected final NexlaLogger logger;
	protected final FileSourceNotificationSender notificationSender;
	private final FileConnectorService probeService;
	protected final SchemaDetectionUtils schemaDetection;
	private final FileSourceOffsetWriter offsetWriter;
	private final Optional<OffsetStorageReader> offsetReader;
	private final MessageGrouper messageGrouper;
	protected final Optional<ListingClient> listingClient;
	protected final Optional<Consumer<TransportFile>> onFileConsumed;
	protected final FileSourceContext fileSourceContext;

	protected final Queue<TransportFile> fileObjects = new LinkedList<>();
	protected final boolean listingEnabled;
	protected boolean stopping;
	private boolean customParserEnabled = false;
	protected final AtomicBoolean firstTime = new AtomicBoolean(true);

	@Deprecated
	private final Optional<Runnable> heartbeatCallback;

	private Optional<NexlaSchema> nexlaSchema = Optional.empty();

	private Optional<String> zipPassword;

	public TransportFileReader(
			FileSourceNotificationSender notificationSender,
			FileConnectorService probeService,
			SchemaDetectionUtils schemaDetection,
			FileSourceOffsetWriter offsetWriter,
			MessageGrouper messageGrouper,
			NexlaLogger logger,
			Optional<OffsetStorageReader> offsetReader,
			Optional<ListingClient> listingClient,
			Optional<Consumer<TransportFile>> onFileConsumed,
			FileSourceContext fileSourceContext,
			Optional<Runnable> heartbeatCallback,
			boolean listingEnabled
	) {
		this.notificationSender = notificationSender;
		this.probeService = probeService;
		this.schemaDetection = schemaDetection;
		this.offsetWriter = offsetWriter;
		this.messageGrouper = messageGrouper;
		this.logger = logger;
		this.offsetReader = offsetReader;
		this.listingClient = listingClient;
		this.onFileConsumed = onFileConsumed;
		this.fileSourceContext = fileSourceContext;
		this.heartbeatCallback = heartbeatCallback;
		this.listingEnabled = listingEnabled;
		this.zipPassword = ofNullable(fileSourceContext.config.originals()
				.get(ZIP_PASSWORD)).map(e -> e.toString());
	}

	public TransportFileReader(
			FileSourceNotificationSender notificationSender,
			FileConnectorService probeService,
			SchemaDetectionUtils schemaDetection,
			FileSourceOffsetWriter offsetWriter,
			MessageGrouper messageGrouper,
			NexlaLogger logger,
			Optional<OffsetStorageReader> offsetReader,
			Optional<ListingClient> listingClient,
			Optional<Consumer<TransportFile>> onFileConsumed,
			FileSourceContext fileSourceContext,
			Optional<Runnable> heartbeatCallback,
			Optional<NexlaSchema> nexlaSchema,
			boolean listingEnabled
	) {
		this.notificationSender = notificationSender;
		this.probeService = probeService;
		this.schemaDetection = schemaDetection;
		this.offsetWriter = offsetWriter;
		this.messageGrouper = messageGrouper;
		this.logger = logger;
		this.offsetReader = offsetReader;
		this.listingClient = listingClient;
		this.onFileConsumed = onFileConsumed;
		this.fileSourceContext = fileSourceContext;
		this.heartbeatCallback = heartbeatCallback;
		this.listingEnabled = listingEnabled;
		this.zipPassword = ofNullable(fileSourceContext.config.originals()
				.get(ZIP_PASSWORD)).map(e -> e.toString());
		this.nexlaSchema = nexlaSchema;
	}

	@Data
	@AllArgsConstructor
	private static class CreateDataSetResult {
		@NonNull
		private List<NexlaMessage> messages;
		@NonNull
		private SchemaDetectionResult result;
	}

	public void cleanupArchives() {
		PATH_TO_ARCHIVE_DIR.forEach((path, dir) -> {
			try {
				deleteDirectory(dir);
			} catch (IOException e) {
				logger.error("Failed to delete archive directory: " + dir.getAbsolutePath(), e);
			}
		});

		PATH_TO_ARCHIVE_DIR.clear();
	}

	@SneakyThrows
	@Override
	public void stop() {
		this.stopping = true;

		logger.info("Waiting for task to close resources");

		fileLock.lock();
		try {
			if (!fileObjects.isEmpty()) {
				ReadingContext currFileContext = fileObjects.peek().ctx;
				if (currFileContext.state == ReadingContext.ReadingContextState.INITIALIZED) {
					logger.info("Closing resources in stop() method");
					finishReading(currFileContext, false);
				}
			}
		} finally {
			fileLock.unlock();
		}

		closeSilently(probeService);
		logger.info("Resources closed");
	}

	@Override
	public <T> ReadBatchResult<T> readNextBatch(FileReadResult<T> messageConsumer, AdminApiClient adminApiClient) {
		fileLock.lock();
		try {
			if (fileObjects.isEmpty()) {
				if (listingClient.filter(x -> listingEnabled).isPresent()) {
					boolean listingInProgress = tryGetNextFile();
					if (fileObjects.isEmpty()) {
						return new ReadBatchResult<>(null, new SchemaContext(), false, !listingInProgress);
					}
				} else {
					return new ReadBatchResult<>(null, new SchemaContext(), false, true);
				}
			}
			ReadBatchResult<T> result = readNextBatchArchiveAware(messageConsumer, adminApiClient, fileObjects.peek());
			if (result.archiveListingUpdated) {
				return readNextBatch(messageConsumer, adminApiClient);
			} else {
				return result;
			}
		} finally {
			fileLock.unlock();
		}
	}

	/**
	 * @return true if it is archive and to get the actual files we need to poll again
	 */
	public boolean doDownload(TransportFile transportFile, String displayPath) {
		String fullPath = transportFile.nexlaFile.getFullPath();

		if (!fileSourceContext.config.fastMode) {
			logger.info("Reading file={} ({})", fullPath, displayPath);
		}

		transportFile.ctx.errorCount = 0;
		transportFile.ctx.errorMessages.clear();

		EncryptionAwareArchiveListedFile listedFile = new EncryptionAwareArchiveListedFile(
				fullPath,
				displayPath,
				fileSourceContext.config.fileEncryptConfig.orElse(null),
				this.zipPassword
		);
		transportFile.ctx.isArchiveOrExtracted = listedFile.isArchive();

		if (transportFile.ctx.state == ReadingContext.ReadingContextState.NEW) {
			notificationSender.publishDownloadMessage(
					"Downloading file %s",
					Optional.ofNullable(displayPath).orElse(fullPath),
					fileSourceContext.orgId,
					NexlaMonitoringLogSeverity.INFO
			);
			if (listedFile.getInternalName().isPresent()) {
				downloadInternalTransportFile(listedFile, transportFile);
			} else {
				downloadTransportFile(transportFile, displayPath);
				if (listedFile.isArchive()) { // has no ARCHIVE_CONTENT_DELIMITER -> full file listed
					List<FileDescription> files = unpackArchiveFile(listedFile, transportFile);
					archiveNotifyListing(transportFile, files);

					transportFile.ctx.localFile.delete();
					fileObjects.poll();
					return true;
				}

				return false;
			}
		}
		return false;
	}

	@SneakyThrows
	public <T> ReadBatchResult<T> readNextBatchArchiveAware(FileReadResult<T> messageConsumer, AdminApiClient adminApiClient, TransportFile transportFile) {
		String displayPath = transportFile.nexlaFile.getMetadata()
				.map(meta -> meta.get(DISPLAY_PATH))
				.map(Object::toString)
				.orElse(null);

		if (doDownload(transportFile, displayPath)) {
			return new ReadBatchResult<>(null, new SchemaContext(), true, false);
		}

		if (transportFile.ctx.state == DOWNLOADED) {
			boolean isNoopMessageConsumer = messageConsumer instanceof NoopFileReadResult;
			initTransportFile(transportFile, adminApiClient, isNoopMessageConsumer);
		}

		if (this.firstTime.get()) {
			this.firstTime.set(false);
			Optional<DataSource> ds = adminApiClient.getDataSource(fileSourceContext.config.sourceId);
			if (ds.isPresent()) {
				Integer dockerInstances = Optional
						.ofNullable(ds.get().getSourceConfig().get(NexlaConstants.DOCKER_INSTANCES))
						.map(a -> {
							String strVal = a.toString();
							try {
								return Integer.parseInt(strVal);
							} catch (NumberFormatException e) {
								logger.warn("docker.instances is present for source-{}, but its value is not a parseable integer. Falling back to 1.", fileSourceContext.config.sourceId);
								return 1;
							}
						}).orElse(1);
				if (dockerInstances > 1) {
					logger.info("This source has more than 1 source pod configured. Returning first batch as empty to prevent possible rebalance issues.");
					return new ReadBatchResult<>(null, transportFile.ctx.schema, false, false);
				} else {
					logger.info("Running source in standard mode (1 pod per source).");
				}
			}
		}

		return read(messageConsumer, transportFile);
	}

	private <T> ReadBatchResult<T> read(FileReadResult<T> messageConsumer, TransportFile transportFile) {
		if (transportFile.ctx.state == ReadingContext.ReadingContextState.INITIALIZED) {
			List<T> messages = doReadNextBatch(transportFile, messageConsumer);
			return new ReadBatchResult<>(messages, transportFile.ctx.schema, false, false);
		} else {
			return new ReadBatchResult<>(null, transportFile.ctx.schema, false, false);
		}
	}

	//
	private void archiveNotifyListing(TransportFile transportFile, List<FileDescription> files) {
		listingClient.ifPresent(lc -> {
			List<String> fileNames = StreamEx.of(files).map(FileDescription::getName).toList();
			logger.info("Updating listed archive file={} with files: {}", transportFile.nexlaFile.getId(), JsonUtils.toJsonString(fileNames));
			lc.externalArchiveListing(fileSourceContext.config.sourceId, transportFile.nexlaFile.getId(), files);
		});
	}

	private List<FileDescription> unpackArchiveFile(ArchiveAwareListedFile listedFile, TransportFile transportFile) {
		String key = fileSourceContext.config.sourceId + "_" + transportFile.nexlaFile.getFullPath();
		File archiveDir = PATH_TO_ARCHIVE_DIR.computeIfAbsent(key, (k) -> {
			File tempDir = Files.createTempDir();
			tempDir.deleteOnExit();
			listedFile.unarchive(transportFile.ctx.localFile, tempDir);
			return tempDir;
		});
		return StreamEx.of(listFiles(archiveDir, null, true))
				.map(x -> getFileDescription(archiveDir, x))
				.toList();
	}

	private void downloadInternalTransportFile(ArchiveAwareListedFile listedFile, TransportFile transportFile) {

		String internalFile = listedFile.getInternalName().get();

		String cacheKey = fileSourceContext.config.sourceId + "_" + listedFile.getArchiveName();
		File cachedArchiveDir = PATH_TO_ARCHIVE_DIR.get(cacheKey);

		if (cachedArchiveDir != null && !new File(cachedArchiveDir, internalFile).exists()) {
			PATH_TO_ARCHIVE_DIR.remove(cacheKey);
		}

		File tempDir = PATH_TO_ARCHIVE_DIR.computeIfAbsent(cacheKey, tf -> unpackArchive(listedFile, listedFile.getArchiveName()));

		boolean internalFileDownloaded = tempDir != null && new File(tempDir, internalFile).exists();
		File localFile = internalFileDownloaded ? new File(tempDir, internalFile) : null;

		if (localFile == null) {
			onNotExistingTransportFile(transportFile);
		} else {
			transportFile.ctx.localFile = localFile;
			transportFile.ctx.state = DOWNLOADED;
		}
	}

	@SneakyThrows
	private File unpackArchive(ArchiveAwareListedFile fileType, String archiveFilePath) {
		File tempDirInt = createTempDirectory("").toFile();
		tempDirInt.deleteOnExit();

		boolean fileExists = probeService.doesFileExists(fileSourceContext.config, archiveFilePath);
		if (fileExists) {
			File arch = downloadFile(archiveFilePath);
			fileType.unarchive(arch, tempDirInt);
			arch.delete();
			return tempDirInt;
		} else {
			return null;
		}
	}

	private void onNotExistingTransportFile(TransportFile transportFile) {
		logger.error("Skipping processing file={} as it does not exist", transportFile);

		notificationSender.publishExceptionMessage(
				transportFile.ctx,
				0L,
				"File does not exist: " + transportFile.nexlaFile.getFullPath(),
				new Exception("File does not exist or is not available: " + transportFile.nexlaFile.getFullPath()),
				Optional.ofNullable(fileSourceContext.runId));
		notificationSender.publishDownloadMessage(
				"File %s does not exist",
				transportFile.ctx.nexlaFile.getFullPath(),
				fileSourceContext.orgId,
				NexlaMonitoringLogSeverity.ERROR);

		removeFileFromQueue(transportFile.ctx, true);
		transportFile.ctx.localFile = null;
		transportFile.ctx.state = NOT_EXISTS;
	}

	@SneakyThrows
	private FileDescription getFileDescription(File archiveDir, File x) {
		String md5;

		if (x.length() > 0) {
			md5 = org.apache.commons.codec.digest.DigestUtils.md5Hex(new FileInputStream(x));
		} else {
			md5 = "0";
		}

		var relativeName = StringUtils.removeStart(x.getAbsolutePath(), archiveDir.getAbsolutePath());
		var name = StringUtils.removeStart(relativeName, "/");
		return new FileDescription(name, md5, x.length());
	}

	protected boolean tryGetNextFile() {
		ListingResult listingResult = listingClient.get().takeFile(fileSourceContext.config.sourceId);
		listingResult.getFile().flatMap(this::tryAddFile).ifPresent(fileObjects::add);
		return listingResult.headerListingInProgress;
	}

	private Optional<TransportFile> tryAddFile(NexlaFile file) {
		Optional<Map<String, Object>> offsetById = offset(file.getId());
		if (offsetById.isPresent()) {
			logger.info("Continue reading file[{}]={}, offset={}", file.getId(), file.getFullPath(), offsetById);
			return of(toTransportFile(file, offsetById.get()));
		} else {
			switch (file.getSource()) {
				case REINGEST:
					return reingestedFile(file);
				case LISTING:
					return ingestFile(file);
				default:
					throw new IllegalArgumentException();
			}
		}
	}

	private Optional<TransportFile> ingestFile(NexlaFile file) {
		if (isFileChanged(file)) {
			return handleChangedFile(file);
		}
		TransportFile transportFile = toTransportFile(file, offset(file.getFullPath()).orElse(emptyMap()));

		if (transportFile.eofReached) {
			return migrateEof(file, transportFile);
		} else if (transportFile.skip) {
			return migrateSkipped(file, transportFile);
		} else {
			return of(transportFile);
		}
	}

	private boolean isFileChanged(NexlaFile file) {
		return file.getLinkedFileId() != null;
	}

	private Optional<TransportFile> reingestedFile(NexlaFile file) {
		logger.info("Reingesting file[{}]={}", file.getId(), file.getFullPath());
		return of(toTransportFile(file, emptyMap()));
	}

	private Optional<TransportFile> migrateSkipped(NexlaFile file, TransportFile transportFile) {
		listingClient.filter(x -> listingEnabled).ifPresent(lc -> lc.setFileStatus(fileSourceContext.config.sourceId, file.getId(), DONE, empty(), empty()));
		logger.info("Migrated file to DONE due to SKIP=true, file={}", transportFile.nexlaFile.getFullPath());
		return empty();
	}

	private Optional<TransportFile> migrateEof(NexlaFile file, TransportFile transportFile) {
		listingClient.filter(x -> listingEnabled).ifPresent(lc -> lc.setFileStatus(fileSourceContext.config.sourceId, file.getId(), DONE, empty(), empty()));
		logger.info("Migrated file to DONE due to EOF=true, file={}", transportFile.nexlaFile.getFullPath());
		return empty();
	}

	private Optional<TransportFile> handleChangedFile(NexlaFile file) {
		if (fileSourceContext.config.appendMode) {
			// in APPEND mode file will be ingested from previously stored offset
			Optional<Map<String, Object>> offsetById = offset(file.getLinkedFileId());
			if (offsetById.isPresent()) {
				logger.error("File changed, continue ingesting in APPEND mode, file[{}]={}", file.getId(), file.getFullPath());
				return of(toTransportFile(file, offsetById.get()));
			} else {
				logger.error("File changed, but original offset was not found to continue in APPEND mode, ingesting from 0, file[{}]={}", file.getId(), file.getFullPath());
				return of(toTransportFile(file, emptyMap()));
			}
		} else {
			logger.error("File changed, ingesting from 0, file[{}]={}", file.getId(), file.getFullPath());
			return of(toTransportFile(file, emptyMap()));
		}
	}

	private Optional<Map<String, Object>> offset(Object partition) {
		return offsetReader.flatMap(or -> ofNullable(or.offset(singletonMap(PARTITION_KEY, partition))));
	}

	protected <T> List<T> doReadNextBatch(TransportFile tf, FileReadResult<T> messageConsumer) {
		ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(this::heartbeatCurrentFile, 0, 1, TimeUnit.MINUTES);
		try {
			boolean grouping = fileSourceContext.config.groupingProps.isPresent();
			boolean shouldReadFullFileInMemory = grouping && fileSourceContext.config.groupingProps.get().inMemory;
			Map<Integer, Metric> datasetMetrics = Maps.newHashMap();

			while (!stopping && tf.ctx.iterator.hasNext() &&
					(shouldReadFullFileInMemory || messageConsumer.acceptsMoreData())) {

				Optional<NexlaMessage> message = tf.ctx.iterator.next();

				// keeping current schema as eofReached = iterator.hasNext() method will update it
				// if next record belongs to next part in multi-part document
				SchemaContext schema = tf.ctx.schema;
				boolean eofReached = !tf.ctx.iterator.hasNext();

				message.ifPresent(m ->
						messageConsumer.acceptMessage(
								schema, tf.ctx, m, eofReached, tf.ctx.messageNumber + 1, datasetMetrics));

				tf.ctx.messageNumber++;
			}

			boolean eof = !tf.ctx.iterator.hasNext();

			if (stopping || eof) {

				logger.info("Closing file in readNextBatch() method={}", tf.nexlaFile.getFullPath());
				boolean done = eof;
				finishReading(tf.ctx, done);
				if (stopping) {
					return emptyList();
				}
				logErrors(tf.ctx);
			}

			if (eof) {
				messageConsumer.onEof(tf.ctx, tf.skip, tf.error);
			}

			if (!fileSourceContext.config.fastMode) {
				logger.info("File offset={} file={}", tf.ctx.messageNumber, tf.nexlaFile.getFullPath());
			}
			messageConsumer.onSuccess(tf.ctx, eof, datasetMetrics);
		} catch (DataSetService.DataSetLimitReached dlr) {
			logger.error("Dataset limit reached on file={}", tf, dlr);
			finishReading(tf.ctx, false, true);
		} catch (Exception e) {
			String message = "Processing error, file might be processed partially";
			logger.error("{}, file={}", message, tf, e);
			// skipping processing file
			finishReading(tf.ctx, true);
			messageConsumer.onException(e, tf.ctx);
		} finally {
			heartbeat.cancel(false);
		}
		return messageConsumer.removeResult();
	}

	protected void heartbeatCurrentFile() {
		TransportFile peek = fileObjects.peek();
		if (peek != null) {
			heartbeatFile(peek.nexlaFile);
		} else {
			logger.warn("Cannot heartbeat file, no file in queue");
		}
	}

	protected void heartbeatFile(NexlaFile nexlaFile) {
		listingClient
				.filter(x -> listingEnabled)
				.ifPresent(lc -> lc.heartBeat(fileSourceContext.config.sourceId, nexlaFile.getId()));
	}

	private void downloadTransportFile(TransportFile transportFile, String displayPath) {
		logger.info("Checking if file exists {}", transportFile.nexlaFile.getFullPath());
		boolean fileExists = probeService.doesFileExists(fileSourceContext.config, transportFile.nexlaFile.getFullPath());
		if (fileExists) {
			transportFile.ctx.localFile = downloadFile(transportFile.nexlaFile.getFullPath());
			transportFile.ctx.state = DOWNLOADED;
			notificationSender.publishDownloadMessage(
					"File %s downloaded successfully",
					Optional.ofNullable(displayPath).orElse(transportFile.nexlaFile.getFullPath()),
					fileSourceContext.orgId,
					NexlaMonitoringLogSeverity.INFO
			);
		} else {
			onNotExistingTransportFile(transportFile);
		}
	}

	@SneakyThrows
	protected void initTransportFile(TransportFile transportFile, AdminApiClient adminApiClient,
																 boolean isNoopMessageConsumer) {

		transportFile.ctx.nexlaFile = transportFile.nexlaFile;
		transportFile.ctx.skippingParserErrors = true;
		transportFile.ctx.messageNumber = transportFile.lastMessageNumber;

		try {
			Optional<NexlaParser> parserOpt = getNexlaParser(transportFile.ctx, adminApiClient);

			if (parserOpt.isPresent()) {
				NexlaParser parser = parserOpt.get();
				setReadingContextParser(transportFile.ctx, transportFile.ctx.nexlaFile.getFullPath(), parser);
				transportFile.ctx.state = PARSER_INITED;

				// errors while detecting schema should be skipped if file will be advanced
				// as it leads to the fact that the schema had been detected before
				// and we do not want the same errors to be handled again
				transportFile.ctx.skippingParserErrors = needAdvanceToOffset(transportFile.ctx) || isNoopMessageConsumer;
				transportFile.ctx.messages = detectSchemaAndConfigureStream(transportFile.ctx);
				// skip parsing errors during advancing
				transportFile.ctx.skippingParserErrors = true;
				advanceToLastOffset(transportFile.ctx);
				// turn on error handling
				transportFile.ctx.skippingParserErrors = false;
				transportFile.ctx.state = ReadingContext.ReadingContextState.INITIALIZED;
				listingClient
						.filter(x -> listingEnabled)
						.ifPresent(client -> {
									notificationSender.sendSuccessMessage(
											emptyContextWithFile(transportFile.ctx),
											Maps.newHashMap(),
											transportFile.eofReached,
											Optional.ofNullable(fileSourceContext.runId));
								}
						);
			} else {
				logger.warn("Could not find parser for file, skipping: {}", transportFile.ctx.nexlaFile.getFullPath());
				notificationSender.publishExceptionMessage(transportFile.ctx, 0L, "Unable to find parser",
						new Exception("Unable to find parser"), Optional.ofNullable(fileSourceContext.runId));
				notificationSender.publishDownloadMessage(
						"Unable to find a parser for file %s",
						transportFile.ctx.nexlaFile.getFullPath(),
						fileSourceContext.orgId,
						NexlaMonitoringLogSeverity.ERROR);
				finishReading(transportFile.ctx, true);
			}

		} catch (DataSetService.DataSetLimitReached dlr) {
			logger.error("Dataset limit reached on file={}", transportFile.nexlaFile.getFullPath());
			finishReading(transportFile.ctx, false, true);
		} catch (Exception e) {

			logger.error("Error in file init " + transportFile.nexlaFile.getFullPath(), e);

			boolean done;
			boolean rethrow = false;

			switch (probeService.analyzeException(e)) {
				case RETRY:
					offsetWriter.markFileAsErroneous(emptyList(), transportFile.ctx, e);
					done = true;
					break;
				case QUARANTINE:
				case SKIP_FILE:
					// skipping processing file
					offsetWriter.markFileAsSkipped(emptyList(), transportFile.ctx, e);
					done = true;
					break;
				case RETHROW:
					// rethrowing exception and stop task
					rethrow = true;
					done = false;
					break;
				default:
					throw new IllegalArgumentException(e);
			}

			finishReading(transportFile.ctx, done);

			if (rethrow) {
				throw e;
			}
		}
	}

	protected void finishReading(ReadingContext ctx, boolean done) {
		finishReading(ctx, done, false);
	}

	protected void finishReading(ReadingContext ctx, boolean done, boolean skipErrorReporting) {
		removeFileFromQueue(ctx, done);

		if (!skipErrorReporting && ctx.state == INITIALIZED) {
			ctx.parser.fileParsingLog.forEach((fileName, message) -> {
				sendNexlaNotificationEvent(
						notificationSender.nexlaMessageProducer,
						ERROR, fileSourceContext.runId,
						SOURCE, fileSourceContext.config.sourceId,
						ctx.nexlaFile.getFullPath() + ":" + fileName, 0L, message);
				notificationSender.publishMonitoringMessage(
						String.format("Error in file %s: %s", fileName, message),
						fileSourceContext.orgId,
						NexlaMonitoringLogType.LOG,
						NexlaMonitoringLogSeverity.ERROR);
			});
		}

		logger.info("[{}] FULL FILE TIME={}, path={}", ctx.nexlaFile.getId(), ctx.timer, ctx.nexlaFile.getFullPath());
		ctx.close();
	}

	protected void removeFileFromQueue(ReadingContext ctx, boolean done) {
		Optional<TransportFile> optTransportFile = fileObjects.stream()
				.filter(tf -> Objects.equals(tf.ctx.nexlaFile.getId(), ctx.nexlaFile.getId()))
				.findFirst();

		if (optTransportFile.isPresent()) {
			TransportFile tf = optTransportFile.get();
			fileObjects.remove(tf);

			onFileConsumed.ifPresent(c -> c.accept(tf));
			listingClient.filter(x -> listingEnabled).ifPresent(client -> {
				ListingFileStatus status = done ? DONE : STOPPED;
				Optional<Boolean> schemaDetectionAttempted = of(tf.ctx).map(x -> x.schemaDetectionAttempted).or(() -> of(false));
				client.setFileStatus(fileSourceContext.config.sourceId, tf.nexlaFile.getId(), status, of(tf.ctx.messageNumber), schemaDetectionAttempted, empty());
			});
		} else {
			logger.warn("File not found in queue, file={}", ctx.nexlaFile);
		}
	}

	@SneakyThrows
	private File downloadFile(String fullPath) {
		// downloading file first, then process it from local FS
		logger.info("Downloading file {}", fullPath);
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		File tempFile = File.createTempFile(fileSourceContext.config.sourceId + "_" + fullPath, "");
		tempFile.deleteOnExit();

		ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(this::heartbeatCurrentFile, 0, 1, TimeUnit.MINUTES);

		try (
				InputStream is = probeService.readInputStream(fileSourceContext.config, fullPath);
				FileOutputStream os = new FileOutputStream(tempFile);
		) {
			IOUtils.copyLarge(is, os);
		} catch (IOException e) {
			notificationSender.publishDownloadMessage(
					"Error while downloading file %s",
					fullPath,
					fileSourceContext.orgId,
					NexlaMonitoringLogSeverity.ERROR);
			throw new ProbeRetriableException(e);
		} finally {
			heartbeat.cancel(false);
		}

		logger.info("Downloaded file={}, size={}, time={}", fullPath, tempFile.length(), stopWatch);
		return tempFile;
	}

	protected Stream<Optional<NexlaMessage>> detectSchemaAndConfigureStream(ReadingContext ctx) {
		if (ctx.parser instanceof MultiPartDocument) {


			Supplier<Supplier<StreamEx<StreamEx<Optional<NexlaMessage>>>>> partStreamsSupplier = () ->
					() -> ((MultiPartDocument) ctx.parser)
							.parseMultiPartMessages(() -> createInputStream(ctx.localFile), true);
			StreamEx<Optional<NexlaMessage>> allMessagesStream = partStreamsSupplier.get().get()
					.reduce(StreamEx.empty(), (collectorStream, nextPartStream) -> {
						List<Optional<NexlaMessage>> nexlaMessagesList = nextPartStream.collect(toList());

						String innerFileName = nexlaMessagesList.stream()
								.map(nexlaMsgOpt -> {
									NexlaMetaData metaData = ofNullable(nexlaMsgOpt.get().getNexlaMetaData()).orElse(new NexlaMetaData());
									String result = null;
									if (metaData.getTags() != null) {
										result = String.valueOf(metaData.getTags().get(DATASET_CUSTOM_NAME));
									}
									return result;
								}).collect(toList()).stream().findFirst().orElse(null);
						DetectionResult detectionResult =
								detectSchema(ctx, innerFileName, nexlaMessagesList.iterator());

						// assigning to local var for memory leak prevention
						SchemaContext schema = detectionResult.schema;
						// peeking first element to update appropriate schema in context
						// at the beginning of reading next part in a multi-part file
						StreamEx<Optional<NexlaMessage>> streamWithSchemaDetection =
								StreamEx.of(detectionResult.iterator)
										.peekFirst(i -> ctx.schema = schema);

						return collectorStream.append(streamWithSchemaDetection);
					})
					.onClose(() -> {
						// closing part streams
//					closeSilently(parts.toArray(new StreamEx[0]));
						// closing main stream
						closeSilently(partStreamsSupplier.get().get());
					});

			ctx.iterator = allMessagesStream.iterator();

			return allMessagesStream;

		} else {

			StreamEx<Optional<NexlaMessage>> messages =
					ctx.parser.parseMessages(() -> createInputStream(ctx.localFile), true);

			initLineNavigator(ctx);

			DetectionResult detectionResult = detectSchema(ctx, null,  messages.iterator());

			ctx.schema = detectionResult.schema;
			ctx.iterator = detectionResult.iterator;

			return messages;
		}
	}

	protected DetectionResult detectSchema(ReadingContext ctx, String name, Iterator<Optional<NexlaMessage>> iterator) {
		SchemaContext schema = new SchemaContext();

		// schema detection
		String dataSetTopic = null;
		FileSourceConnectorConfig sourceCfg = fileSourceContext.config;
		if (sourceCfg.schemaDetectionTopic != null) {
			dataSetTopic = sourceCfg.schemaDetectionTopic;
			ctx.schemaDetectionAttempted = false;
		} else if (skipSmallFileDetection(ctx)) {
			Integer dataSetId = fileSourceContext.dataSetId.get();
			dataSetTopic = nameDataSetTopic(dataSetId);
			schema.dataSetId = dataSetId;
			ctx.schemaDetectionAttempted = false;
		} else if (nexlaSchema.isPresent()) {
			// skipping schema detection as schema was passed from a higher level
			ctx.schemaDetectionAttempted = false;
		} else {
			ctx.schemaDetectionAttempted = true;
			if (sourceCfg.schemaDetectionOnce && schemaDetection.getNumSchemas() != 0) {
				if (schemaDetection.getNumSchemas() > 1) {
					throw new ConnectException("There must be single topic configured for dataset when schema.detection.once=true");
				}
				schemaDetection.setTryCombineSingleSchema();
			}
			List<Optional<NexlaMessage>> restoreMessages = new ArrayList<>();
			List<Optional<NexlaMessage>> messagesForSchemaDetection = new ArrayList<>();

			try {
				while (iterator.hasNext()) {
					Optional<NexlaMessage> dataItemOptional = iterator.next();
					restoreMessages.add(dataItemOptional);
					if (dataItemOptional.isPresent()) {
						messagesForSchemaDetection.add(dataItemOptional);
					}

					if (!sourceCfg.isFullFileSchemaDetection()) {
						if (messagesForSchemaDetection.size() == sourceCfg.schemaDetectionMaxMessages) {
							break;
						}
					}
				}
			} catch (Exception e) {
				logger.warn("Error in file {}", e.getMessage());
				ctx.errorCount++;
			}

			String notifMsg = StringUtils.EMPTY;
			if (!messagesForSchemaDetection.isEmpty()) {

				Optional<CreateDataSetResult> optCreateResult = updateOrCreateDataSet(ctx, name, sourceCfg, messagesForSchemaDetection);

				if (optCreateResult.isPresent()) {
					final List<NexlaMessage> sampleMessages = optCreateResult.get().getMessages();
					final SchemaDetectionResult detectionResult = optCreateResult.get().getResult();
					List<NexlaMessage> messagesWithMeta = zipWithIndices(StreamEx.of(sampleMessages)).mapKeyValue((num, m) -> {
								NexlaMetaData nexlaMetaData = createSourceMetadata(
										sourceCfg.sourceType, sourceCfg.sourceId, sourceCfg.version,
										ctx.nexlaFile, false, (long) num, schema.dataSetTopic,
										fileSourceContext.runId, m.getNexlaMetaData(), schema.dataSetId);

								m.setNexlaMetaData(nexlaMetaData);
								return m;
							})
							.toList();

					detectionResult.updateSamplesCallback.accept(messagesWithMeta);

					schema.dataSetId = detectionResult.dataSetId;
					dataSetTopic = detectionResult.topic;
				} else {
					// we should not process further files with invalid schema, will mark it as processed
					notifMsg = String.format("File has invalid schema detected: %s", ctx.nexlaFile.getFullPath());
					// if we skip file we need to reset iterator as well
					restoreMessages = new ArrayList<>();
				}
			} else {
				// file is read fully with no readable messages, will mark it as processed
				notifMsg = String.format("File has no readable lines: %s", ctx.nexlaFile.getFullPath());
			}

			if (StringUtils.isNotBlank(notifMsg)) {
				logger.warn("Skipping file... {}", notifMsg);
				NexlaConnectorUtils.sendNexlaNotificationEvent(notificationSender.nexlaMessageProducer,
						EMPTY_DATA,
						fileSourceContext.runId,
						SOURCE,
						sourceCfg.sourceId,
						ctx.nexlaFile.getFullPath(),
						0L,
						notifMsg);
				notificationSender.publishMonitoringMessage(
						notifMsg,
						fileSourceContext.orgId,
						NexlaMonitoringLogType.LOG,
						NexlaMonitoringLogSeverity.WARNING);
			}

			//restoring iterator's state
			iterator = Iterators.concat(restoreMessages.iterator(), iterator);
		}

		schema.dataSetTopic = dataSetTopic;

		return new DetectionResult(schema, iterator);
	}

	/**
	 * While DataSet processing schema should be valid.
	 * Invalid schema should not be set as a dataset source schema if 'GroupingProps' is enabled.
	 *
	 * @param ctx                        file context
	 * @param name					     custom name for DataSet
	 * @param sourceCfg                  file source connector config
	 * @param messagesForSchemaDetection nexla messages to process
	 * @return optional static nested class with list of nexla messages and schema detection result object
	 */
	protected Optional<CreateDataSetResult> updateOrCreateDataSet(ReadingContext ctx,
	                                                            String name,
	                                                            FileSourceConnectorConfig sourceCfg,
	                                                            List<Optional<NexlaMessage>> messagesForSchemaDetection) {
		CreateDataSetResult result = null;
		List<LinkedHashMap<String, Object>> recs = messagesForSchemaDetection.stream()
				.filter(Optional::isPresent)
				.map(Optional::get)
				.map(NexlaMessage::getRawMessage)
				.collect(toList());

		if (ctx.parser instanceof SchemaAware && sourceCfg.groupingProps.isEmpty()) {
			NexlaSchema resolvedSchema = ((SchemaAware) ctx.parser).getSchema();
			SchemaDetectionResult detectionResult = schemaDetection.updateOrCreateDataSet(recs, name, of(resolvedSchema));
			List<NexlaMessage> sampleMessages = StreamEx.of(messagesForSchemaDetection).filter(Optional::isPresent).map(Optional::get).toList();
			result = new CreateDataSetResult(sampleMessages, detectionResult);
		} else {
			// for grouped case schema is detected by an artificial grouped message
			List<LinkedHashMap<String, Object>> schemaDetectionMessages = sourceCfg.groupingProps
					.map(groupingProps -> groupInMemory(groupingProps, recs))
					.orElse(recs);

			if (!schemaDetectionMessages.isEmpty()) {
				SchemaDetectionResult detectionResult = schemaDetection.updateOrCreateDataSet(schemaDetectionMessages, name, empty());
				List<NexlaMessage> sampleMessages = StreamEx.of(schemaDetectionMessages).map(NexlaMessage::new).toList();
				if (detectionResult == null) {
					logger.warn("Schema detection result for source {} is NULL", sourceCfg.sourceId);
				}
				result = new CreateDataSetResult(sampleMessages, detectionResult);
			}
		}

		return Optional.ofNullable(result);
	}

	private boolean skipSmallFileDetection(ReadingContext ctx) {
		return !fileSourceContext.config.listingMode &&
				fileSourceContext.config.schemaDetectionOnce &&
				fileSourceContext.dataSetId.isPresent() &&
				ctx.localFile.length() < fileSourceContext.config.maxFileSizeSchemaDetect;
	}

	private List<Object> groupKeyValue(LinkedHashMap<String, Object> message, GroupingProps groupingProps) {
		return groupingProps.key.stream().map(message::get).collect(toList());
	}

	private List<LinkedHashMap<String, Object>> groupInMemory(
			GroupingProps groupingProps,
			List<LinkedHashMap<String, Object>> records
	) {
		List<LinkedHashMap<String, Object>> groupedRecords = new ArrayList<>();
		if (records.isEmpty()) {
			return groupedRecords;
		}

		LinkedHashMap<List<Object>, List<LinkedHashMap<String, Object>>> groupedMessages = new LinkedHashMap<>();
		for (LinkedHashMap<String, Object> rawMessage : records) {
			List<Object> groupKey = groupKeyValue(rawMessage, groupingProps);

			boolean groupContainsNullKey = groupKey.contains(null);
			if (groupingProps.publishNullKey || !groupContainsNullKey) {
				groupedMessages.computeIfAbsent(groupKey, a -> new ArrayList<>()).add(rawMessage);
			}
		}

		Iterator<Map.Entry<List<Object>, List<LinkedHashMap<String, Object>>>> iter = groupedMessages.entrySet().iterator();
		while (iter.hasNext()) {
			Map.Entry<List<Object>, List<LinkedHashMap<String, Object>>> e = iter.next();
			List<LinkedHashMap<String, Object>> groupRecords = e.getValue();
			LinkedHashMap<String, Object> recs = messageGrouper.getGroupMessage(groupingProps, groupRecords);
			groupedRecords.add(recs);
		}

		return groupedRecords;
	}

	private void advanceToLastOffset(ReadingContext ctx) {
		if (needAdvanceToOffset(ctx)) {
			timed(() -> {
				logger.info("Partial file read {}, advancing iterator to {}", ctx.nexlaFile.getFullPath(), ctx.messageNumber);
				StreamUtils.advance(ctx.iterator, ctx.messageNumber);
			}, "File advanced, begin publishing to kafka");
		}
	}

	protected boolean needAdvanceToOffset(ReadingContext ctx) {
		return ctx.messageNumber > 0;
	}

	private void initLineNavigator(ReadingContext readingContext) {
		if (readingContext.parser instanceof DelimitedTextParser) {
			DelimitedTextParser delimitedTextParser = (DelimitedTextParser) readingContext.parser;
			readingContext.lineNavigator = of(new FileLineNavigator(readingContext.localFile, delimitedTextParser.usedCharset));
		}
	}

	@SneakyThrows
	protected InputStream createInputStream(File tempFile) {
		BufferedInputStream bufferedInputStream = new BufferedInputStream(new FileInputStream(tempFile), ONE_MB);
		return new SourceAwareInputStream<>(tempFile, bufferedInputStream);
	}

	protected Optional<NexlaParser> getNexlaParser(ReadingContext ctx, AdminApiClient adminApiClient) throws IOException {
		try (FileInputStream inputStream = new FileInputStream(ctx.localFile)) {
			String fileName = ctx.nexlaFile.getMetadata()
					.filter(m -> !ctx.isArchiveOrExtracted)
					.map(meta -> meta.get(DISPLAY_PATH))
					.map(Object::toString)
					.orElse(ctx.nexlaFile.getFullPath());
			Optional<String> mimeType = getMiMeType(ctx, fileName);
			ParserUtilsExt.Result detection = detectParser(mimeType, inputStream, fileSourceContext.config.overridenExtensions, fileSourceContext.config.originalsStrings(), fileName, null, logger);
			if (detection.parser().isDefined()) {
				return Optional.of(detection.parser().get());
			} else if (isCustomParserEnabled()) {
				logger.info("Custom parser enabled. Looking for custom parser");
				return createCustomParser(fileSourceContext, ctx, adminApiClient);
			}
		}
		return Optional.empty();
	}

	private Optional<String> getMiMeType(ReadingContext ctx, String fileName) {
		if (fileName.contains(ARCHIVE_CONTENT_DELIMITER) && ctx.nexlaFile.getMetadata().isPresent()) {
			return Optional.of(fileName);
		}

		return ctx.nexlaFile.getMetadata()
				.map(meta -> meta.get(MIME_TYPE))
				.map(Object::toString);
	}

	protected Optional<NexlaParser> createCustomParser(FileSourceContext fileSourceContext, ReadingContext ctx, AdminApiClient adminApiClient) {
		logger.info("Creating custom parser for source {} for file {}",
				fileSourceContext.config.sourceId, ctx.nexlaFile.getFullPath());
		return ParserUtils.createCustomParser(adminApiClient,
				fileSourceContext.config.sourceId, ctx.nexlaFile.getMetadata().orElse(emptyMap()),
				fileSourceContext.config.decryptKey);
	}

	public boolean isCustomParserEnabled() {
		return customParserEnabled;
	}

	public void setCustomParserEnabled(boolean customParserEnabled) {
		this.customParserEnabled = customParserEnabled;
	}

	private void setReadingContextParser(ReadingContext ctx, String fullPath, NexlaParser parser) {
		BiConsumer<ParseError, Exception> exceptionHandler = (position, e) -> {

			switch (probeService.analyzeException(e)) {
				case RETRY:
				case SKIP_FILE:
					rethrow(e); // will be handled on upper level
				case QUARANTINE:

					if (!ctx.skippingParserErrors) {
						ctx.errorCount++;

						if (position != null) {
							notificationSender.sendQuarantineRecord(ctx, e, position, Optional.ofNullable(fileSourceContext.runId));
						}

						if (ctx.errorMessages.size() < MAX_ERROR_MESSAGES_TO_LOG) {
							ctx.errorMessages.add(position != null ? (position.getMessageNumber() + ": " + e.getMessage()) : e.getMessage());
						}
					}
					break;

				default:
					throw new IllegalArgumentException("Unsupported state");
			}

		};
		parser.exceptionHandler(exceptionHandler);

		parser.withLoggerPrefix(logger.getPrefix(), "[" + fullPath + "]");

		ctx.parser = parser;
	}

	public static TransportFile toTransportFile(NexlaFile file, Map<String, Object> fileOffset) {

		long lastMessageNumber =
				((fileOffset.get(OFFSET_POSITION_KEY) == null) || (fileOffset.get(OFFSET_POSITION_KEY) instanceof String))
						? 0L
						: toLong(fileOffset.get(OFFSET_POSITION_KEY));

		boolean error = optBoolean(fileOffset.get(ERROR_KEY)).orElse(false);
		boolean skip = optBoolean(fileOffset.get(SKIP_KEY)).orElse(false);
		boolean eofReached = ofNullable(fileOffset.get(EOF_KEY)).map(a -> (boolean) a).orElse(false);

		Long size = ofNullable(fileOffset.get(SIZE_KEY)).map(ConverterUtils::toLong)
				.or(() -> Optional.ofNullable(file.getSize()))
				.orElse(null);
		String md5 = ofNullable(fileOffset.get(HASH_KEY)).map(Object::toString)
				.or(() -> Optional.ofNullable(file.getMd5()))
				.orElse(null);
		Long lastModified = ofNullable(fileOffset.get(LAST_MODIFIED_KEY)).map(ConverterUtils::toLong)
				.or(() -> Optional.ofNullable(file.getLastModified()))
				.orElse(null);

		NexlaFile readFile = new NexlaFile(
				file.getId(), file.getLinkedFileId(), file.getSource(),
				file.getFullPath(), size, file.getNameSpace(),
				md5, null, lastModified,
				file.getType());

		readFile.setMetadata(file.getMetadata());

		TransportFile transportFile = new TransportFile(readFile, lastMessageNumber, eofReached);
		transportFile.error = error;
		transportFile.skip = skip;

		return transportFile;
	}

	@SneakyThrows
	private void rethrow(Exception e) {
		throw e;
	}

	protected void logErrors(ReadingContext ctx) {
		if (ctx.errorCount > 0) {
			logger.warn("Skipped #={} malformed lines in file={}, offset sample={}",
					ctx.errorCount, ctx.nexlaFile.getFullPath(), ctx.errorMessages.toString());
		}
	}

	@Override
	public Queue<TransportFile> getFileObjects() {
		return fileObjects;
	}

	public void addFiles(List<TransportFile> transportFiles) {
		fileLock.lock();
		try {
			transportFiles.forEach(tf -> tf.ctx.nexlaFile = tf.nexlaFile);
			fileObjects.addAll(transportFiles);
		} finally {
			fileLock.unlock();
		}
	}

	private static ReadingContext emptyContextWithFile(ReadingContext ctx) {
		ReadingContext stubContext = new ReadingContext();
		stubContext.nexlaFile = ctx.nexlaFile;
		return stubContext;
	}
}
