package com.nexla.connector.file.source

import com.nexla.admin.client.CodeContainer.CodeConfig
import com.nexla.admin.client.FindOrCreateDataSetResult.Result
import com.nexla.admin.client.{AdminApiClient, CodeContainer, DataSet, DataSource, FindOrCreateDataSetResult}
import com.nexla.common.NexlaConstants._
import com.nexla.common.{NexlaConstants, NexlaFile, NexlaMessage}
import com.nexla.common.logging.NexlaLogger
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent
import com.nexla.common.notify.transport.{NexlaMessageProducer, NoopNexlaMessageTransport}
import com.nexla.common.parse.NexlaParser
import com.nexla.connect.common.SchemaDetectionResult
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.file.service.FileConnectorService
import com.nexla.listing.client.ListingClient
import com.nexla.parser.xml.AdditionalPathsParser
import com.nexla.parser.{JsonParser, PythonCustomParser}
import com.nexla.transform.schema.FormatDetector
import org.mockito.{ArgumentCaptor, ArgumentMatchers}
import org.mockito.ArgumentMatchers.{any, anyObject}
import org.mockito.Mockito.{mock, never, times, verify, when}
import org.scalatest.{BeforeAndAfterEach, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.slf4j.LoggerFactory

import java.io.File
import java.net.URL
import java.util
import java.util.Optional
import java.util.function.Consumer
import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class TransportFileReaderTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterEach{

  private val mockAdminApiClient: AdminApiClient = mock(classOf[AdminApiClient])
  private val testEncryptionKey = "key-to-the-kingdom"

  override protected def beforeEach(): Unit = {
    val testDataSource = new DataSource()
    testDataSource.setId(1)
    testDataSource.setCodeContainerId(10)
    testDataSource.setSourceConfig(new util.HashMap[String, Object]())

    when(mockAdminApiClient.getDataSource(any())).thenReturn(Optional.ofNullable(testDataSource))
    when(mockAdminApiClient.getCodeContainer(any())).thenReturn(new CodeContainer(1, "python", null, "efficient and beautiful code", null, Optional.empty[CodeConfig]()))
  }

  "getNexlaParser" should "get a custom parser when it is configured" in {
    val transportFileReader = new TransportFileReader(null, null, null,
      null, null, new NexlaLogger(LoggerFactory.getLogger(getClass)), null, null,
      null, createFileSourceContext(), null, false)
    transportFileReader.setCustomParserEnabled(true)

    val result: Optional[NexlaParser] = transportFileReader.getNexlaParser(createReadingContext("/aes_encrypted_data"), mockAdminApiClient)
    result.isPresent shouldEqual true
    result.get() shouldBe a[PythonCustomParser]
  }

  it should "not get a parser when custom parser is not enabled" in {
    val transportFileReader = new TransportFileReader(null, null, null,
      null, null, new NexlaLogger(LoggerFactory.getLogger(getClass)), null, null,
      null, createFileSourceContext(), null, false)
    transportFileReader.setCustomParserEnabled(false)

    val result: Optional[NexlaParser] = transportFileReader.getNexlaParser(createReadingContext("/aes_encrypted_data"), mockAdminApiClient)
    result shouldEqual Optional.empty()
  }

  "getNexlaParser" should "not take into consideration mimeType when it's a decompressed file with metadata" in {
    val transportFileReader = new TransportFileReader(null, null, null,
      null, null, new NexlaLogger(LoggerFactory.getLogger(getClass)), null, null,
      null, createFileSourceContext(), null, false)

    val context = createReadingContext("/1_row_json.json")
    val metadata = Map[String, Object]("mime_type" -> "application/zip", "display_path"-> "/nexla.zip")
    context.nexlaFile.setMetadata(Optional.of(metadata.asJava))
    context.nexlaFile.setFullPath("nexla.zip;1_row_json.json")
    context.isArchiveOrExtracted = true

    val result: Optional[NexlaParser] = transportFileReader.getNexlaParser(context, mockAdminApiClient)
    result.isPresent shouldEqual true
    result.get() shouldBe a[AdditionalPathsParser]
  }

  it should "return an empty batch for the first time to prevent parallelism issues" in {
    val testDataSource = new DataSource()
    testDataSource.setId(1)
    testDataSource.setCodeContainerId(10)
    testDataSource.setSourceConfig(new util.HashMap[String, Object]())
    testDataSource.getSourceConfig.put(NexlaConstants.DOCKER_INSTANCES, Integer.valueOf(2))
    when(mockAdminApiClient.getDataSource(any())).thenReturn(Optional.ofNullable(testDataSource))

    val mockNS = mock(classOf[FileSourceNotificationSender])
    val mockProbe = mock(classOf[FileConnectorService[BaseAuthConfig]])
    when(mockProbe.doesFileExists(anyObject(), anyObject())).thenReturn(true)
    when(mockProbe.readInputStream(anyObject(), anyObject())).thenReturn(this.getClass.getResourceAsStream("/simple-txt-file.txt"))
    val mockSchemaDetector = mock(classOf[SchemaDetectionUtils])
    val stubDS = new SchemaDetectionResult(1, "tc", new Consumer[util.List[NexlaMessage]] {
      override def accept(t: util.List[NexlaMessage]): Unit = println("accepted!")
    })
    when(mockSchemaDetector.updateOrCreateDataSet(anyObject(), anyObject(), anyObject())).thenReturn(stubDS)
    val mockListing = mock(classOf[ListingClient])

    val transportFileReader = new TransportFileReader(mockNS, mockProbe, mockSchemaDetector,
      null, null, new NexlaLogger(LoggerFactory.getLogger(getClass)), null, Optional.of(mockListing),
      Optional.empty(), createFileSourceContext(), null, false)

    val nf = new NexlaFile()
    nf.setFullPath("/simple-txt-file.txt")
    val tf = new TransportFile(nf, 0L, false)
    transportFileReader.addFiles(java.util.Collections.singletonList(tf))
    val msgConsumer = mock(classOf[BaseKafkaFileMessageReader])
    when(msgConsumer.acceptsMoreData()).thenReturn(true)

    // this will return empty batch as expected
    val result = transportFileReader.readNextBatch(msgConsumer, mockAdminApiClient)
    assert(result.messages == null)
    // and nothing will be sent further
    verify(msgConsumer, never()).acceptMessage(anyObject(), anyObject(), anyObject(), anyObject(), anyObject(), anyObject())
    // but on reading the next batch, we must accept a bunch of messages to our "kafka writer"
    val nextResult = transportFileReader.readNextBatch(msgConsumer, mockAdminApiClient)
    // and all of them must be consumed properly
    assert(nextResult.messages.isEmpty)

    // and let's capture and test them here
    val captor: ArgumentCaptor[NexlaMessage] = ArgumentCaptor.forClass(classOf[NexlaMessage])
    verify(msgConsumer, times(3)).acceptMessage(anyObject(), anyObject(), captor.capture(), anyObject(), anyObject(), anyObject())
    val msgs = captor.getAllValues
    msgs.forEach(msg => {
      println(s"checking msg $msg")
      assert(msg.getRawMessage.get("a") != null)
      assert(msg.getRawMessage.get("b") != null)
      assert(msg.getRawMessage.get("c") != null)
    })
  }

  it should "handle `dataset limit reached`" in {
    FormatDetector.initDefault()
    when(mockAdminApiClient.findOrCreateDataSet(any()))
      .thenReturn(new FindOrCreateDataSetResult(Result.DATASET_LIMIT_REACHED, -1))

    val testDataSource = new DataSource()
    testDataSource.setId(1)
    testDataSource.setSourceConfig(new util.HashMap[String, Object]())
    testDataSource.setDatasets(new util.ArrayList[DataSet]())

    when(mockAdminApiClient.getDataSource(any()))
      .thenReturn(Optional.ofNullable(testDataSource))

    val mockNS = mock(classOf[FileSourceNotificationSender])
    val mockProbe = mock(classOf[FileConnectorService[BaseAuthConfig]])
    when(mockProbe.doesFileExists(anyObject(), anyObject())).thenReturn(true)

    when(mockProbe.readInputStream(anyObject(), ArgumentMatchers.eq("/simple-txt-file-0.txt"))).thenReturn(this.getClass.getResourceAsStream("/simple-txt-file.txt"))
    when(mockProbe.readInputStream(anyObject(), ArgumentMatchers.eq("/simple-txt-file-1.txt"))).thenReturn(this.getClass.getResourceAsStream("/simple-txt-file.txt"))
    when(mockProbe.readInputStream(anyObject(), ArgumentMatchers.eq("/simple-txt-file-2.txt"))).thenReturn(this.getClass.getResourceAsStream("/simple-txt-file.txt"))


    var monitoringLogs = List[String]()
    val mockSchemaDetector = new SchemaDetectionUtils(
      1, mockAdminApiClient, new NexlaMessageProducer(new NoopNexlaMessageTransport() {
        override def publishMonitoringLog(monitoringLogEvent: NexlaMonitoringLogEvent): Unit = {
          monitoringLogs ::= monitoringLogEvent.getLog
        }
      }) , Optional.empty(), 1, 1, 1, false
    )

    val mockListing = mock(classOf[ListingClient])
    val transportFileReader = new TransportFileReader(mockNS, mockProbe, mockSchemaDetector,
      null, null, new NexlaLogger(LoggerFactory.getLogger(getClass)), null, Optional.of(mockListing),
      Optional.empty(), createFileSourceContext(), null, false)

    def newTF(path: String) = {
      val nf = new NexlaFile()
      nf.setFullPath(path)
      new TransportFile(nf, 0L, false)
    }

    transportFileReader.addFiles(java.util.List.of(
      newTF("/simple-txt-file-0.txt"),
      newTF("/simple-txt-file-1.txt"),
      newTF("/simple-txt-file-2.txt"),
    ))

    val msgConsumer = mock(classOf[BaseKafkaFileMessageReader])
    when(msgConsumer.acceptsMoreData()).thenReturn(true)

    var polls = 0;
    var result = transportFileReader.readNextBatch(msgConsumer, mockAdminApiClient)
    while (!result.listingIsEmpty) {
      assert(result.messages == null)
      polls += 1
      result = transportFileReader.readNextBatch(msgConsumer, mockAdminApiClient)
    }

    assert(polls == 3)

    assert(monitoringLogs.isEmpty)
  }

  private def createFileSourceContext(): FileSourceContext = {
    val properties = new util.HashMap[String, Any] {{
      put(SOURCE_ID, "34")
      put(SOURCE_TYPE, "s3")
      put(CREDS_ENC, "34")
      put(CREDS_ENC_IV, "34")
      put(UNIT_TEST, "true")
      put(CREDENTIALS_DECRYPT_KEY, new String(util.Arrays.copyOf(testEncryptionKey.getBytes, 16)))
    }}
    new FileSourceContext(new FileSourceConnectorConfig(properties), Optional.of(1), 123L)
  }

  private def createReadingContext(fileName: String) : ReadingContext = {
    val context = new ReadingContext
    val aes_encrypted_data: URL = getClass.getResource(fileName)
    context.localFile = new File(aes_encrypted_data.toURI)
    context.nexlaFile = new NexlaFile()
    context.nexlaFile.setFullPath(fileName)
    context
  }
}
