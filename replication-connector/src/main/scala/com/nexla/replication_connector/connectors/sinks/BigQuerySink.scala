package com.nexla.replication_connector.connectors.sinks

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials
import com.nexla.common.NexlaSslContext
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.connector.config.BaseConnectorConfig.FAST_MODE
import com.nexla.connector.config.big_query.BigQuerySinkConnectorConfig
import com.nexla.connector.push.sink.BigQuerySinkTask
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.connectors.sinks.ReplicationSinkConnector.{SinkUploadError, SinkUploadResult, SinkUploadSuccess}
import com.nexla.replication_connector.connectors.sinks.utils.EmptySinkTaskContext
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.pipeline.SourceDownloader.LocallyReplicatedFile
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.StrictNexlaLogging

import java.io.File
import scala.concurrent.{ExecutionContext, Future}

class BigQuerySink(pipelineContext: Context,
                   adminApiClient: AdminApiClient,
                   replicationMessageProducer: ReplicationMessageProducer,
                   props: AppProps,
                  )
                  (implicit val ec: ExecutionContext,
                   val system: ActorSystem,
                   val mat: Materializer)
  extends ReplicationSinkConnector
    with StrictNexlaLogging
    with ConfigEnricher {
  override val sendsItsOwnMetrics: Boolean = true

  private val configMap = fullDataSinkConfig(props.config, pipelineContext.sink, props.enrichSinkParams, BigQuerySinkConnectorConfig.configDef())
  enrichWithDataCredentials(adminApiClient, configMap)
  val config = new BigQuerySinkConnectorConfig(configMap)

  private val task = new BigQuerySinkTask() {
    override def saveErrorFile(localTempFile: File): Unit = {
      // do not save file locally
    }

    override def messageProducer(nexlaSslConfig: NexlaSslContext): NexlaMessageProducer = replicationMessageProducer

  }
  task.initialize(new EmptySinkTaskContext)
  task.setRunId(pipelineContext.runId)

  configMap.put(FAST_MODE, "true")
  task.start(configMap, "fast-bigquery-sink")

  def uploadFile(localFile: LocallyReplicatedFile): Future[SinkUploadResult] = {
    val result = Future(task.uploadFileFastConnector(localFile.file))

    result.map { _ =>
      SinkUploadSuccess(
        listedId = localFile.listedId,
        file = localFile.file,
        recordCount = localFile.recordCount,
        errorCount = 0,
        byteSize = localFile.byteSize,
        displayName = config.table
      )
    }.recover { case ex =>
      SinkUploadError(
        listedId = localFile.listedId,
        file = localFile.file,
        recordCount = localFile.recordCount,
        errorCount = 1,
        byteSize = localFile.byteSize,
        displayName = config.table,
        ex = ex,
      )
    }
  }

}
