package com.nexla.replication_connector.pipeline

import akka.NotUsed
import akka.actor.{ActorSystem, Cancellable, Scheduler}
import akka.stream.scaladsl.{Flow, Sink, Source}
import com.nexla.connector.config.{FileReadRetryConfig, SinkConnectorConfig}
import com.nexla.connector.config.file.AWSAuthConfig.getFilePath
import com.nexla.connector.config.file.{FileSinkConnectorConfig, FileSourceConnectorConfig}
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat
import com.nexla.file.service.FileConnectorService
import com.nexla.replication_connector.connectors.sinks.utils.DwhSinkFileConversion
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.flow_logs.FlowLogsSender
import com.nexla.replication_connector.metrics.MetricsSender
import com.nexla.replication_connector.pipeline.SourceDownloader.{LocalReplicationError, LocallyReplicated<PERSON>ile, SourceDownloadResult}
import com.nexla.replication_connector.pipeline.metadata.{FileToReplicate, RemoteReplicationMetadata}
import com.nexla.replication_connector.state.State
import com.nexla.sc.util.StrictNexlaLogging
import org.apache.commons.lang3.StringUtils.removeStart

import java.io.{File, FileOutputStream}
import java.nio.file.Files
import java.util.concurrent.atomic.{AtomicBoolean, AtomicReference}
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class SourceDownloader(
                        pipelineContext: Context,
                        pipelineState: State,
                        flowLogsSender: FlowLogsSender,
                        metricsSender: MetricsSender,
                      )
                      (implicit system: ActorSystem,
                       scheduler: Scheduler,
                       ec: ExecutionContext)
  extends StrictNexlaLogging {

  private val dwhSinkFileConversion = new DwhSinkFileConversion()

  private val TmpDir = Files.createTempDirectory("source_downloader_")
  logger.info(s"Created temporary directory for locally replicated files: $TmpDir")

  def downloadToLocal(remoteSource: RemoteReplicationMetadata, sinkConfig: SinkConnectorConfig): Source[LocallyReplicatedFile, NotUsed] =
    remoteSource
      .source
      .alsoTo(flowLogsSender.sourceStartingFlowLogs(pipelineContext.source, pipelineContext.runId))
      .alsoTo(notifyOnStart())
      .mapAsync(1) { files =>
        for {
          replicatedFiles <- replicateFileLocally(remoteSource.probeService, remoteSource.sourceConfig, files)
          downloadedFiles = replicatedFiles.collect { case l: LocallyReplicatedFile => l }
          joinedFiles <- maybeJoinFiles(downloadedFiles, sinkConfig)
        } yield replicatedFiles.collect { case f: LocalReplicationError => f }.toList ::: joinedFiles.toList
      }
      .mapConcat(identity)
      .alsoTo(metricsSender.sourceMetrics(pipelineContext.source, pipelineContext.dataset, pipelineContext.runId))
      .alsoTo(flowLogsSender.sourceEndingFlowLogs(pipelineContext.source, pipelineContext.runId))
      .alsoTo(notifyOnEnd())
      .collect { case l: LocallyReplicatedFile => l }

  private def maybeJoinFiles(files: Seq[LocallyReplicatedFile], sinkConfig: SinkConnectorConfig): Future[Seq[SourceDownloadResult]] = {
    val filesToJoin = files.map(_.file)
    val formats = files.flatMap(_.fileConfig).groupBy(x => x).keySet
    val containsOnlyCsvFormat = formats.size == 1 && formats.forall(_.format == WarehouseCopyFileFormat.Format.CSV_FORMAT)
    val joinedFilesEff = sinkConfig match {
      case config: FileSinkConnectorConfig if config.joinFiles && pipelineContext.source.getConnectionType.isWarehouseSource && containsOnlyCsvFormat =>
        logger.info(s"joining files, replication, warehouse, csv format, ... [files=${files.map(_.fileRelativePath)}]")
        Future {
          val records = files.map(_.recordCount).sum
          val format = formats.head
          val fstFile = filesToJoin.head

          val fileName = s"source-${pipelineContext.source.getId}-${pipelineContext.runId}.${format.extension}"
          val outputFile = new File(fstFile.getParentFile, fileName)
          if (format.compression == WarehouseCopyFileFormat.Compression.GZIP) {
            dwhSinkFileConversion.joinFilesCsvGzip(filesToJoin, outputFile)
          } else {
            dwhSinkFileConversion.joinFilesCsv(filesToJoin, outputFile)
          }
          List(LocallyReplicatedFile(None, fileName, None, outputFile, records, outputFile.length(), files.head.displayName))
        }
      case config: FileSinkConnectorConfig if config.joinFiles =>
        logger.info(s"Do not joining files: source dwh = ${pipelineContext.source.getConnectionType.isWarehouseSource} input files format == $formats")
        Future.successful(files)
      case _ => Future.successful(files)
    }
    joinedFilesEff.transform {
      case Success(r) => Success(r)
      case Failure(ex) =>
        logger.info(s"Joining files failed failed with", ex)
        Success(files.map(f => LocalReplicationError(f.recordCount, f.displayName, ex)))
    }
  }

  private def replicateFileLocally(probeService: FileConnectorService[_],
                                   sourceConfig: FileSourceConnectorConfig,
                                   files: Seq[FileToReplicate],
                                  ): Future[Seq[SourceDownloadResult]] = {
    Source(files.toList).mapAsync(1) { listedFile =>
      val tmpFileEff = listedFile.file match {
        case Some(tempFile) =>
          logger.info(s"File already replicated locally by source connector: ${tempFile.toPath.toString}")
          Future.successful(tempFile)
        case None =>
          val downloadFileEff = () => downloadFile(probeService, sourceConfig)(listedFile)
          maybeRetryForFiles(sourceConfig.fileReadRetryConfig.asScala, downloadFileEff)
            .recover { case e =>
              logger.error(s"Retries exhausted for ${listedFile.nativeStoragePath}", e)
              throw e
            }
      }
      val lineCountEff = (f: File) =>  Future(Files.lines(f.toPath).count()).recover { case ex =>
          logger.warn(s"Could not get number of lines for file ${f.toPath.toString}", ex)
          0L
        }

      tmpFileEff.transformWith {
        case Success(tempFile) =>
          lineCountEff(tempFile).map { recordCount =>
            val r = LocallyReplicatedFile(listedFile.listedId, getRelativePath(sourceConfig, listedFile), listedFile.fileConfig, tempFile, recordCount, tempFile.length(), listedFile.displayName)
            logger.info(s"File ${r.fileRelativePath} successfully replicated locally to ${r.file.toPath.toString}")
            r
          }
        case Failure(ex) =>
          val r = LocalReplicationError(0, listedFile.displayName, ex)
          logger.info(s"File ${r.displayName} local replication failed with", ex)
          Future.successful(r)
      }
    }.runWith(Sink.seq)
  }

  private def getRelativePath(sourceConfig: FileSourceConnectorConfig, fileToReplicate: FileToReplicate): String = {
    val removePrefix = getFilePath(sourceConfig.getConnectionType, sourceConfig.path)
    removeStart(fileToReplicate.nativeStoragePath, removePrefix)
  }

  private def downloadFile(probeService: FileConnectorService[_], sourceConfig: FileSourceConnectorConfig)
                          (listedFile: FileToReplicate): Future[File] = Future {
    val relativePath = getRelativePath(sourceConfig, listedFile)
    val tempFile = new File(TmpDir.toFile, relativePath)
    tempFile.getParentFile.mkdirs()

    val outputStream = new FileOutputStream(tempFile)

    val log = system.scheduler.scheduleWithFixedDelay(0.seconds, 1.minutes)(() => logger.info(s"Downloading $relativePath..."))

    val result = Try {
      probeService.readToOutputStream(sourceConfig, listedFile.nativeStoragePath, outputStream)
      tempFile
    }
    com.nexla.common.FileUtils.closeSilently(outputStream)
    val _ = log.cancel()
    result match {
      case Success(f) =>
        logger.info(s"File $relativePath downloaded successfully")
      f
      case Failure(e) =>
        logger.error(s"Downloading error", e)
        throw e
    }
  }

  private val heartbeatSwitch: AtomicReference[Cancellable] = new AtomicReference[Cancellable]()

  private def notifyOnStart(): Sink[Any, Unit] = {
    val wasExecuted = new AtomicBoolean(false)
    Sink.foreach[Any] { _ =>
      if (!wasExecuted.get()) {
        val cancellable = system.scheduler.scheduleWithFixedDelay(1.minute, 1.minute)(() => pipelineState.updateDataIngestionTs())
        pipelineState.notifyReadStart()
        pipelineState.markSourceAsNonEmpty()
        wasExecuted.set(true)
        heartbeatSwitch.set(cancellable)
      }
    }
  }.mapMaterializedValue(_ => ())

  private def notifyOnEnd(): Sink[Any, Unit] = Flow[Any]
    .watchTermination()((_, done) => done.onComplete {
      case Success(_) =>
        pipelineState.notifyReadDone()
        Option(heartbeatSwitch.get()).map(_.cancel())
      case Failure(_) =>
        pipelineState.notifyReadFailed()
        Option(heartbeatSwitch.get()).map(_.cancel())
    }).to(Sink.ignore)


  private def maybeRetryForFiles[T](maybeRetryConfig: Option[FileReadRetryConfig], eff: () => Future[T])(implicit ec: ExecutionContext, scheduler: Scheduler): Future[T] = maybeRetryConfig match {
    case Some(retryConfig) => akka.pattern.retry(eff, retryConfig.nrOfAttempts, Duration.fromNanos(retryConfig.minBackoff.toNanos), Duration.fromNanos(retryConfig.maxBackoff.toNanos), 0)
    case None => eff()
  }

}

object SourceDownloader {
    sealed trait SourceDownloadResult {
      val recordCount: Long
      val displayName: String
    }
    case class LocallyReplicatedFile(
                                      listedId: Option[Long],
                                      fileRelativePath: String,
                                      fileConfig: Option[WarehouseCopyFileFormat],
                                      file: File,
                                      recordCount: Long,
                                      byteSize: Long,
                                      displayName: String,
                                    ) extends SourceDownloadResult

    case class LocalReplicationError(
                                      recordCount: Long,
                                      displayName: String,
                                      ex: Throwable,
                                    ) extends SourceDownloadResult
}
