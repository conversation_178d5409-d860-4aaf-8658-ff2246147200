package com.nexla.replication_connector

import com.nexla.admin.client.config.EnrichedConfig.{EnrichSinkParams, EnrichSourceParams}
import com.nexla.common.NexlaConstants
import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.config._

import scala.compat.java8.OptionConverters._

class AppProps(val config: NexlaAppConfig)
  extends Vault
    with NexlaCreds
    with Zookeeper
    with KafkaProperties
    with NexlaClusterApplication
    with NexlaDecryptKey
    with NexlaAdminApi
    with NexlaEndpoints
    with SecretNames
    with AwsCredentials
    with TelemetryConfig
    with DataDog
    with Prometheus {

  val enrichSourceParams = new EnrichSourceParams(
    zookeeperConnect,
    bootstrapServers,
    vault.map(_.host).asJava,
    vault.map(_.token).asJava,
    1,
    1,
    config.getStore.getType,
    credentialsAwsRegion.asJava,
    credentialsAwsAccessKey.asJava,
    credentialsAwsSecretKey.asJava,
    credentialsAwsRoleArn.asJava,
    credentialsAwsIdentityTokenFile.asJava,
    secretNames.asJava
  )

  val enrichSinkParams = new EnrichSinkParams(
    bootstrapServers,
    config.getStore.getType,
    vault.map(_.host).asJava,
    vault.map(_.token).asJava,
    credentialsAwsRegion.asJava,
    credentialsAwsAccessKey.asJava,
    credentialsAwsSecretKey.asJava,
    secretNames.asJava
  )

  val imcFlowId: Int = config.getInt(NexlaConstants.DEDICATED_IMC_FLOW_ID)
  val imcRunId: Long = config.getLong(NexlaConstants.DEDICATED_IMC_RUN_ID.toUpperCase)
  val nodeTags: Option[Seq[String]] = config.getOptString("node.tags").map(_.split(",").toSeq)

  val trackerDisabled: Boolean = config.getOptBoolean("metadata.tracker.disabled").getOrElse(true)
}
