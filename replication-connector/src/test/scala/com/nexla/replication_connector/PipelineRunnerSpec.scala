package com.nexla.replication_connector

import akka.actor.{ActorSystem, Scheduler}
import com.nexla.admin.client._
import com.nexla.common.NexlaConstants.{TOPIC_CONTROL, TOPIC_METRICS, TOPIC_NOTIFY}
import com.nexla.common.notify.transport.NexlaMessageProducer
import com.nexla.common.{AppType, ConnectionType, NexlaSslContext, StreamUtils}
import com.nexla.control.message.{ControlEventType, ControlMessage, ControlResourceType, SourceControlMessage}
import com.nexla.file.service.FileConnectorService
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.kafka.KafkaMessageTransport
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.utils.{Fixtures, LocalFileSystemConnectorService, ShutdownActorSystem}
import com.nexla.sc.client.listing.CoordinationAppClient
import io.github.embeddedkafka.Codecs.stringDeserializer
import io.github.embeddedkafka.EmbeddedKafka.withConsumer
import io.github.embeddedkafka.{EmbeddedK, EmbeddedKafka, EmbeddedKafkaConfig}
import org.mockito.Mockito.mock
import org.scalatest.{Assertion, BeforeAndAfterEach}
import org.scalatest.concurrent.Eventually.eventually
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import java.nio.file.{Files, Paths}
import java.util
import scala.concurrent.ExecutionContextExecutor
import scala.concurrent.duration.DurationInt
import scala.jdk.CollectionConverters.{iterableAsScalaIterableConverter, mapAsScalaMapConverter}
import scala.util.Random

class PipelineRunnerSpec extends AnyFunSuite with Matchers with ShutdownActorSystem with BeforeAndAfterEach {
  implicit val system: ActorSystem = ActorSystem()
  private implicit val ec: ExecutionContextExecutor = system.dispatcher
  private implicit val sch: Scheduler = system.scheduler

  private val SourceRootPath = getClass.getResource("/test_data/input1").getPath
  private val DestinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString

  private var kafkaConfig: EmbeddedK = _

  override protected def beforeEach(): Unit = {
    super.beforeEach()
    kafkaConfig = EmbeddedKafka.start()(EmbeddedKafkaConfig(0, 0))
  }

  override protected def afterEach(): Unit = {
    super.afterEach()
    EmbeddedKafka.stop(kafkaConfig)
  }

  private def createDataSource(sourceId: Int, org: Org) = {
    val dataSource = new DataSource()
    dataSource.setId(sourceId)
    dataSource.setOrg(org)
    dataSource.setConnectionType(ConnectionType.FILE_UPLOAD)
    dataSource.setSourceConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", SourceRootPath)
      }
    })
    dataSource
  }

  private def createDataSink(sinkId: Int, org: Org) = {
    val dataSink = new DataSink()
    dataSink.setId(sinkId)
    dataSink.setOrg(org)
    dataSink.setConnectionType(ConnectionType.FTP)
    dataSink.setSinkConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", DestinationRootPath)
      }
    })
    val ds = new DataSet
    ds.setId(1)
    dataSink.setDataSet(ds)
    dataSink
  }


  test("successful pipeline should emit stop event at the end") {
    val flowId = Random.nextInt()
    val runId = Random.nextInt()
    val org = Fixtures.mockOrg(Random.nextInt())
    val dataSource = createDataSource(Random.nextInt(), org)
    val dataSink = createDataSink(Random.nextInt(), org)

    val srcFile1Path = Paths.get(SourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(SourceRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)
    val javaListingClient = mock(classOf[JavaListingClient])
    val coordinationClient = mock(classOf[CoordinationAppClient])
    val mockFileVault: FileVaultClient = mock(classOf[FileVaultClient])

    val props = Fixtures.stubProps
    val probeFactory = new ProbeFactory {
     override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
        if (sourceType.equals(ConnectionType.FTP)) {
          new LocalFileSystemConnectorService
        } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
          new LocalFileSystemConnectorService
        } else {
          throw new Exception(s"Unsupported type $sourceType")
        }
      }
    }

    implicit val embeddedKafkaConfig: EmbeddedKafkaConfig = kafkaConfig.config
      withConsumer[String, String, Assertion] { flowLogsConsumer =>
        flowLogsConsumer.subscribe(util.Arrays.asList(TOPIC_CONTROL))

        val messageProducer = new ReplicationMessageProducer(new NexlaMessageProducer(new KafkaMessageTransport(s"localhost:${kafkaConfig.config.kafkaPort}", NexlaSslContext.NOSSL_CONTEXT, TOPIC_METRICS, TOPIC_NOTIFY)))

        val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)

        val runner = new PipelineRunner(flowId, runId, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, coordinationClient, mockFileVault, probeFactory, props, messageProducer)
        runner.run(new PipelineKiller).futureValue(Timeout(30.seconds))
        messageProducer.flush()

        eventually {
          val records = flowLogsConsumer.poll(java.time.Duration.ofMillis(10.seconds.toMillis)).asScala.toList

          records.nonEmpty shouldBe true

          val receivedEvents = records.map(_.value()).map(ctrlMessage => StreamUtils.jsonUtil().stringToType(ctrlMessage, classOf[ControlMessage]))

          receivedEvents.length shouldEqual 1
          receivedEvents.head shouldBe a[SourceControlMessage]
          val firstEvent = receivedEvents.head.asInstanceOf[SourceControlMessage]
          firstEvent.getResourceId shouldEqual dataSource.getId
          firstEvent.getEventType shouldEqual ControlEventType.PAUSE
          firstEvent.getResourceType shouldEqual ControlResourceType.SOURCE
          firstEvent.getConnectionType shouldEqual dataSource.getConnectionType
          firstEvent.getOrigin shouldEqual AppType.REPLICATION_CONNECTOR.appName
          firstEvent.getContext.asScala shouldEqual Map("HARD_STOP" -> "true")
        }
        succeed

      }

  }
}
