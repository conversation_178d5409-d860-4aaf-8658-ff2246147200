# Changelog

## [3.2.0] - 2025-05-29
- Changed - [NEX-16644](https://nexla1.atlassian.net/browse/NEX-16644) explicitly pass script name to ray request. Different name is used when byo-optimized flow is used

## [3.2.0] - 2025-05-23
- Changed – [NEX-16679](https://nexla1.atlassian.net/browse/NEX-16679) Enforce flushing by using `doFlush(ReadyToFlush.FLUSH)` for RedisSinkTask
 
## [3.2.0] - 2025-05-23
- Changed – Use overridden `NoopOffsetWriter` with `FileSourceTask`.
- Changed – [NEX-16481](https://nexla1.atlassian.net/browse/NEX-16481) Set `error` flag for failed files in `CustomRtTransportFileReader.java`. Only files without this flag are returned to IMC, ensuring that error messages for failed files are preserved.
 
## [3.2.0] - 2025-05-21
- Added - include "referenced_resource_ids" read from source in ray request - https://nexla1.atlassian.net/browse/NEX-16506
- Added - include "sink_id" in ray request
- Added - lowered the default batch size for files heartbeating. The value can be read from source property: 'imc.file.heartbeat.batch.siz' - https://nexla1.atlassian.net/browse/NEX-16562
- Added - special mode for BYO pipelines which use optimized flow (files download and upload happens on Ray) - https://nexla1.atlassian.net/browse/NEX-16500.
  Available only for file-file BYO flows. Disabled by default. Activated by source property `imc.byo.optimized.flow.enabled`

## [3.2.0] - 2025-05-06
- Fixed - use a separate execution context for file uploads in BYO pipelines. Previously, uploading large files caused the liveness probe to become unresponsive

## [3.2.0] - 2025-05-05
- Fixed - runId was used as an option when building a ray input path which resulted in paths like: 'ray-input/Some(123)/...'. This has been fixed

## [3.2.0] - 2025-04-30
- Removed - support for BYO sink config parameter `imc.byo.replication.parallelism`
- Added - support for `imc.byo.replication.parallelism.enabled`; when set to `true`, sink parallelism is determined via the `calculateSinkParallelism` method in `FileConnectorService.java` - https://nexla1.atlassian.net/browse/NEX-16231
- Fixed - improved error handling and retry logic in file replication by separating retries for mezzanine download and final destination upload
- Improved - failures during BYO sink file replication no longer fails replication of other files
- 
## [3.2.0] - 2025-04-25
- Changed - exposed information about whether the source should be stopped via BaseSourceTask, replacing the use of SourceStoppingCondition.
- Added - exposed readyToFlush method from BaseSinkTask
- Changed - replaced `flush` with `doFlush` method for intermediate flushing
- Changed - made the early shutdown monitor async. It does not slow pipeline even if AdminApi cache is disabled
- Fixed - file statuses handling; during intermediate flush, offsets are passed downstream only if doFlush returns true.
- Fixed - file statuses handling; use listing app which does not update file statuses. It is to prevent file statuses to be updated from withing source task connectors.
- Fixed - reenabled transformations parallelism

## [3.2.0] - 2025-04-24
- Added - support for overwriting existing files in BYO flow - https://nexla1.atlassian.net/browse/NEX-16057
- Added - configurable retries for BYO sink file uploader

## [3.2.0] - 2025-04-09
- Added - terminate pending Ray jobs on app shutdown - https://nexla1.atlassian.net/browse/NEX-15939
- Fixes - termination logic now correctly waits for the pipeline to shut down

## [3.2.0] - 2025-04-07
- Added - clean up local tmp files after replication in BYO flow
- Fixed - use paginated api for fetching list of files in a bucket - https://nexla1.atlassian.net/browse/NEX-15983

## [3.2.0] - 2025-04-03
- Fixed - limited the number of parallel file downloads in BYO sink - https://nexla1.atlassian.net/browse/NEX-15944

## [3.2.0] - 2025-03-24
- FIXED - paths for dry run - https://nexla1.atlassian.net/browse/NEX-15771
- CHANGED - output paths in BYO flow are now preserved - https://nexla1.atlassian.net/browse/NEX-15175
 
## [3.2.0] - 2025-02-24
### Changed
- Added "schema.detection.once": "true" to FlatMapper configuration. Sources for IMC with Ray have this setting always set to true.

## [3.2.0] - 2025-02-19
### Fixed
- Support for parquet parser. Added the `iceberg-probe` dependency which contains the avro parquet package.

## [3.2.0] - 2025-02-14
### Added
- Added support for the IMC_DRY_RUN_ADDITIONAL_METADATA environment variable for dry runs. It is intended to hold a JSON object with additional information. Currently, it is expected to contain fileMetadata for schema detection mode. Json structure will allow us to add arbitrary metadata in the future.

## [3.2.0] - 2025-02-11
### Changed
- If message in SetFileStatusCoordination is None, replace it with empty string "". This is so we always override the previous message in listing database.

## [3.2.0] - 2025-02-11
### Changed
- dry run mode logic for reading and parsing file was changed. For file without an extension in a path [GDrive, Sharepoint, etc.], we need to explicitly set a parser. Auto Detect won't work

## [3.2.0] - 2025-02-04
### Changed
- Increased host-connection-pool.max-open-requests to 256

## [3.2.0] - 2025-01-28
### Fixed
- Fix rest sink - https://nexla1.atlassian.net/browse/NEX-14927
  - removed calling setRunId in sinks setup. RunId should be read from message metadata

## [3.2.0] - 2025-01-24
### Fixed
- Fix handling flows which are triggered by another flows - https://nexla1.atlassian.net/browse/NEX-14485

## [3.2.0] - 2025-01-23
### Added
- Support for Google Drive sources in DryRun mode - https://nexla1.atlassian.net/browse/NEX-14865

