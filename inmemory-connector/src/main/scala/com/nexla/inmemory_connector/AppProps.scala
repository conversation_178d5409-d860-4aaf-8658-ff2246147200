package com.nexla.inmemory_connector

import com.nexla.admin.client.config.EnrichedConfig.{EnrichSinkParams, EnrichSourceParams}
import com.nexla.common.NexlaConstants
import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.config._

import java.util.concurrent.TimeUnit
import scala.compat.java8.OptionConverters._
import scala.concurrent.duration.FiniteDuration
import scala.util.Try

class AppProps(val config: NexlaAppConfig)
  extends Vault
    with NexlaCreds
    with NexlaCredsEncoded
    with RedisCreds
    with Zookeeper
    with KafkaProperties
    with NexlaClusterApplication
    with NexlaDecryptKey
    with NexlaSslConfig
    with NexlaAdminApi
    with NexlaEndpoints
    with SecretNames
    with MetricsTopic
    with AwsCredentials
    with NexlaTasks
    with TelemetryConfig
    with DataDog
    with Prometheus
    with FeatureFlags {

  val validateOutputSchema: Boolean = config.getBoolean("validate.output.schema")

  val enrichSourceParams = new EnrichSourceParams(
    zookeeperConnect,
    bootstrapServers,
    vault.map(_.host).asJava,
    vault.map(_.token).asJava,
    1,
    1,
    config.getStore.getType,
    credentialsAwsRegion.asJava,
    credentialsAwsAccessKey.asJava,
    credentialsAwsSecretKey.asJava,
    credentialsAwsRoleArn.asJava,
    credentialsAwsIdentityTokenFile.asJava,
    secretNames.asJava
  )

  val enrichSinkParams = new EnrichSinkParams(
    bootstrapServers,
    config.getStore.getType,
    vault.map(_.host).asJava,
    vault.map(_.token).asJava,
    credentialsAwsRegion.asJava,
    credentialsAwsAccessKey.asJava,
    credentialsAwsSecretKey.asJava,
    secretNames.asJava
  )

  val imcFlowId: Option[Int] = config.getOptInt(NexlaConstants.DEDICATED_IMC_FLOW_ID)
  val imcRunId: Option[Long] = config.getOptString(NexlaConstants.DEDICATED_IMC_RUN_ID.toUpperCase).flatMap(s => Try(s.toLong).toOption)

  val imcMezzanineCredsId: Option[Int] = config.getOptInt("IMC_MEZZANINE_CREDS_ID")
  val imcMezzanineBucketPrefix: String = config.getOptString("IMC_MEZZANINE_BUCKET_PREFIX").getOrElse("nexla-shared-s3/")
  val imcDryRunExtraData: Option[String] = config.getOptString("IMC_DRY_RUN_EXTRA_DATA")
  val imcDryRunSrcConfig: Option[String] = config.getOptString("IMC_DRY_RUN_SRC_CONFIG")
  val imcDryRunUploadPath: Option[String] = config.getOptString("IMC_DRY_RUN_MEZZANINE_SAMPLE_PATH")

  val imcDryRunEnabled: Boolean = config.getOptBoolean("IMC_DRY_RUN_ENABLED").getOrElse(false)
  val imcDryRunSourceCredsId: Option[Int] = config.getOptInt("IMC_DRY_RUN_SOURCE_CREDS_ID")
  val imcDryRunInputFile: Option[String] = config.getOptString("IMC_DRY_RUN_INPUT_FILE")
  val imcDryRunAdditionalMetadata: Option[String] = config.getOptString("IMC_DRY_RUN_ADDITIONAL_METADATA")
  val imcDryRunOutputFileName: Option[String] = config.getOptString("IMC_DRY_RUN_OUTPUT_FILENAME")
  val imcDryRunDriverFunction: Option[String] = config.getOptString("IMC_DRY_RUN_DRIVER_FUNCTION")
  val imcDryRunPackages: Option[String] = config.getOptString("IMC_DRY_RUN_PACKAGES")
  val imcDryRunCustomCode: Option[String] = config.getOptString("IMC_DRY_RUN_CUSTOM_CODE")

  val trackerDisabled: Boolean = config.getOptBoolean("metadata.tracker.disabled").getOrElse(true)

  val sinkBatchTime: FiniteDuration = FiniteDuration.apply(config.getOptInt("sink.batch.seconds").getOrElse(5), TimeUnit.SECONDS)
  val sinkBatchSize: Int = config.getOptInt("sink.batch.size").getOrElse(1000)
  val txParallelismDefault: Int = config.getOptInt("tx.parallelism.default").getOrElse(1)
  val txRecordTransformTimeoutSecondsDefault: Int = config.getOptInt("tx.record.transform.timeout.seconds.default").getOrElse(30)
  val defaultFileHeartbeatBatchSize: Int = config.getOptInt("imc.file.heartbeat.batch.size").getOrElse(5000)

  lazy val rayApiServer: String = config.getString("ray.api.server.url")
}
