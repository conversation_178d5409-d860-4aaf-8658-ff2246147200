package com.nexla.inmemory_connector

import akka.actor.{ActorSystem, Scheduler}
import com.nexla.admin.client.AdminApiClient
import com.nexla.common.ResourceType
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.inmemory_connector.context.Context
import com.nexla.inmemory_connector.context.Context.{DryRunContext, PipelineContext}
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.monitoring.{MetricsOffsetReporterImpl, MetricsSenderImpl}
import com.nexla.inmemory_connector.pipeline.byo.ByoMetricSender
import com.nexla.inmemory_connector.pipeline.flow.newimpl.ImcListingClient
import com.nexla.inmemory_connector.pipeline.flow.{DryPipelineBuilder, PipelineBuilder, RealPipelineBuilder}
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryMessageProducer
import com.nexla.inmemory_connector.pipeline.sink.offsets.{File<PERSON><PERSON>et<PERSON>ommitter, MapOffsetCommitter}
import com.nexla.inmemory_connector.state.{DryRunStateReporter, FilesState, PipelineStateReporter, StateReporter}
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.FileVaultClient
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.listing.{CoordinationAppClient, ListingAppClient}
import com.nexla.sc.util.StrictNexlaLogging
import com.nexla.telemetry.Telemetry
import com.nexla.transform.TransformService

import java.util.Optional
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


class PipelineRunner(adminApi: AdminApiClient,
                     listingClient: ListingAppClient,
                     javaListingClient: ImcListingClient,
                     coordinationClient: CoordinationAppClient,
                     fileVault: FileVaultClient,
                     probeFactory: ProbeFactory,
                     props: AppProps,
                     transformServiceImpl: TransformService,
                     offsetSaver: OffsetSaver,
                     messageProducer: InMemoryMessageProducer,
                     telemetry: Telemetry,
                     intermediateFlushDelay: FiniteDuration = 60.seconds,
                     maxIntermediateFlushAwaitInterval: FiniteDuration = 10.minutes,
                    )(implicit system: ActorSystem, ec: ExecutionContext, sch: Scheduler) extends StrictNexlaLogging {


  private val progressTracker = new InMemoryProgressTracker

  def run(pipelineContext: Context, pipelineKiller: PipelineKiller, inFlightFilesState: FilesState): Future[Unit] = {
    val stateReporter: StateReporter = pipelineContext match {
      case prc: PipelineContext =>
        val metricsSender = new MetricsSenderImpl(messageProducer, prc.flowType, prc.runId)
        val fileOffsetCommitter = FileOffsetCommitter.withKafka(messageProducer)
        new PipelineStateReporter(prc, inFlightFilesState)(coordinationClient, fileOffsetCommitter, metricsSender, messageProducer, initialDelay = 10.seconds, heartbeatPeriod = 1.minutes)
      case _: DryRunContext => new DryRunStateReporter
    }
    logger.info("Pipeline started")

    val buildAndRun = () => Future.fromTry(createPipelineBuilder(pipelineContext, inFlightFilesState).build()).flatMap { blueprint =>
      stateReporter.start(blueprint.resourceStates)
      pipelineKiller.addKillFunction(() => {
        blueprint.stop()
        true
      })

      blueprint.run()
    }

    buildAndRun()
      .transformWith {
        case Success(_) =>
          logger.info("Pipeline ended with success")
          Future.unit
        case Failure(ex) =>
          logger.error("Pipeline ended with error", ex)
          Future.failed(ex)
      }
      .transformWith { r =>
        val result = stateReporter.finalReportAndFinish().flatMap(_ => Future.fromTry(r))
        invalidateDataInAdminApi(pipelineContext)     // Invalidation in admin api is required for adaptive flow tasks
        result
      }
  }

  private def createPipelineBuilder(context: Context, inFlightFilesState: FilesState): PipelineBuilder = {
    context match {
      case prc: PipelineContext =>
        val metricsSender = new MetricsSenderImpl(messageProducer, prc.flowType, prc.runId)
        val fileOffsetCommitter = FileOffsetCommitter.withKafka(messageProducer)
        val mapOffsetSender = MapOffsetCommitter.withCoordinationClient(coordinationClient)
        val byoMetricSender = new ByoMetricSender(metricsSender)
        val metricsOffsetReporter = new MetricsOffsetReporterImpl(fileOffsetCommitter, mapOffsetSender, inFlightFilesState, metricsSender)
        val inMemoryTelemetry = new InMemoryTelemetry(telemetry, prc.flow.getId, prc.flowType, prc.runId)
        new RealPipelineBuilder(listingClient, adminApi, coordinationClient, javaListingClient, fileVault, transformServiceImpl, messageProducer, props, offsetSaver, metricsOffsetReporter, prc, inFlightFilesState, probeFactory, progressTracker, byoMetricSender, inMemoryTelemetry, intermediateFlushDelay, maxIntermediateFlushAwaitInterval)
      case drc: DryRunContext => new DryPipelineBuilder(props, drc)
    }
  }

  private def invalidateDataInAdminApi(pipelineContext: Context): Unit = {
    pipelineContext match {
      case prc: PipelineContext =>
        adminApi.invalidate(prc.dataSource.getId, Optional.ofNullable(prc.dataSource.getFlowNodeId), ResourceType.SOURCE);
        prc.dataSets.foreach(ds => adminApi.invalidate(ds.getId, Optional.ofNullable(ds.getFlowNodeId), ResourceType.DATASET))
        adminApi.invalidate(prc.dataSink.getId, Optional.ofNullable(prc.dataSink.getFlowNodeId), ResourceType.SINK)
      case _ => // Do nothing
    }
  }


}
