package io.tabular.iceberg.connect.channel;

import com.nexla.admin.client.DataSink;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import io.tabular.iceberg.connect.IcebergSinkConfig;
import io.tabular.iceberg.connect.data.Utilities;
import java.util.Collection;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;
import org.apache.iceberg.catalog.Catalog;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.slf4j.Logger;

/**
 * Adapted from {@link TaskImpl} which is distributed under the Apache License 2.0
 * <p></p>
 * <p>
 *  Default worker task that will use the Nexla commit coordinator
 * </p>
 */
public class <PERSON>bergCdcWorker implements Task, AutoCloseable {

  private final Catalog catalog;
  private final Writer writer;
  private final Committer committer;
  private final AtomicLong recordsAccumulator;

  public IcebergCdcWorker(SinkTaskContext context, IcebergSinkConnectorConfig nexlaConfig, IcebergSinkConfig config,
                          Supplier<Long> runIdSupplier, DataSink dataSink, NexlaMessageProducer nexlaMessageProducer,
                          Optional<OffsetsTracker> offsetsTracker, PostponedFlush postponedFlush, Logger logger, Catalog catalog,
                          Runnable postCommitCallback) {
    this.catalog = catalog;
    this.writer = new Worker(config, this.catalog);
    this.recordsAccumulator = new AtomicLong(0);
    this.committer = new NexlaCommitterImpl(context, config, nexlaConfig, runIdSupplier, dataSink, nexlaMessageProducer,
        this.catalog, new KafkaClientFactory(config.kafkaProps()),
         offsetsTracker.orElse(null), postponedFlush, logger, this.recordsAccumulator, postCommitCallback);
  }

  /**
   * Write records and attempt a commit. Using the Nexla commit coordinator, commit readiness will be goverened by
   * {@link PostponedFlush} logic. A call to put with an empty collection just triggers another commit check.
   *
   * @param sinkRecords Records destined for the destination table
   */
  @Override
  public void put(Collection<SinkRecord> sinkRecords) {
    this.writer.write(sinkRecords);
    this.recordsAccumulator.addAndGet(sinkRecords.size());
    this.committer.commit(writer);
  }

  @Override
  public void close() throws Exception {
    Utilities.close(this.writer);
    Utilities.close(this.catalog);
    Utilities.close(this.committer);
  }
}
