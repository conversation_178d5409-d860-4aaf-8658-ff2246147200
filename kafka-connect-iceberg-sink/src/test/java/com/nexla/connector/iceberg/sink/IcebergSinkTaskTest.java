package com.nexla.connector.iceberg.sink;

import static com.nexla.common.NexlaConstants.CREDENTIALS_DECRYPT_KEY;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.LISTING_ENABLED;
import static com.nexla.common.NexlaConstants.MONITOR_POLL_MS;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.NexlaConstants.SINK_TYPE;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.file.AWSAuthConfig.SERVICE_ENDPOINT;
import static com.nexla.connector.config.file.FileSinkConnectorConfig.SPARK_SESSION_CONFIGS;
import static com.nexla.connector.config.file.S3Constants.ACCESS_KEY_ID;
import static com.nexla.connector.config.file.S3Constants.REGION;
import static com.nexla.connector.config.file.S3Constants.SECRET_KEY;
import static com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.TABLE_NAME;
import static com.nexla.connector.properties.FileConfigAccessor.FILE_NAME_PREFIX;
import static com.nexla.connector.properties.FileConfigAccessor.MAX_FILE_SIZE_MB;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.testcontainers.containers.localstack.LocalStackContainer.Service.S3;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.Fixtures;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import com.nexla.listing.client.ListingClient;
import com.nexla.probe.iceberg.IcebergConnectorService;
import com.nexla.test.IntegrationTests;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import lombok.SneakyThrows;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.apache.spark.sql.SparkSession;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.ClassRule;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.Mockito;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;

@Category(IntegrationTests.class)
public class IcebergSinkTaskTest extends BaseKafkaTest {
  private static final String TEST_TABLE_NAME = "warcraft.expansions";
  private static final String TEST_TABLE_CAST_LONG_TO_STRING = "castlong.tostring";
  private static final String TEST_TABLE_TYPE_EVOLUTION_NAME = "column.evolution";
  public static KafkaContainer kafka = new KafkaContainer("7.2.11");
  static DockerImageName localstackImage = DockerImageName.parse("localstack/localstack:3.7.0");

  private IcebergSinkTask task;

  @ClassRule
  public static LocalStackContainer localstack = new LocalStackContainer(localstackImage)
      .withServices(S3);

  @SneakyThrows
  @BeforeClass
  public static void setup() {
    localstack.execInContainer("awslocal", "s3", "mb", "s3://nexla-data-lake");
    kafka.start();
    init(kafka);
  }

  @AfterClass
  public static void teardown() {
    SparkSession.active().close();
  }

  @Before
  public void onBefore() {
    AdminApiClient adminApiClient = Fixtures.adminApiWithSourceSinkDataset(1);
    AdminApiClientBuilder.INSTANCE = adminApiClient;

    this.task = new IcebergSinkTask() {
      @Override
      public void doStart() {
        super.doStart();
        this.listingClient = Mockito.mock(ListingClient.class);
        this.offsetsSender = Optional.of(Mockito.mock(OffsetsTracker.class));
        this.postponedFlush = Mockito.mock(PostponedFlush.class);
        Mockito.when(this.postponedFlush.readyToFlush()).thenReturn(ReadyToFlush.FLUSH);
      }
    };

    SinkTaskContext taskContext = Mockito.mock(SinkTaskContext.class);
    Mockito.when(taskContext.assignment()).thenReturn(Collections.emptySet());
    task.initialize(taskContext);
  }

  @After
  @SneakyThrows
  public void after() {
    if (task != null
        && task.getNexlaMessageProducer() != null) {
      task.getNexlaMessageProducer().close();
      task.stop();
    }
  }

  @Test
  public void shouldBeAbleToAppendToTableInS3() {
    var connectorConfig = getSinkConnectorConfigProps();
    task.start(connectorConfig);

    NexlaMessage message = createMessage(new LinkedHashMap<>(
        Map.of("title", "World of Warcraft", "release_date", "2004-11-23")
    ));

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null, JsonUtils.toJsonString(message), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    var probe = new IcebergConnectorService();
    SparkSession spark = probe.getSparkSession(new IcebergSinkConnectorConfig(connectorConfig), Collections.emptyMap());
    var df = spark.table("warcraft.expansions");
    var rows = df.sqlContext().sql("SELECT * FROM warcraft.expansions").collectAsList();
    Assert.assertEquals("World of Warcraft", rows.get(0).<String>getAs("title"));
    Assert.assertEquals("2004-11-23", rows.get(0).<String>getAs("release_date"));
  }

  @Test
  public void shouldBeAbleToUpsertToTableInS3() {
    var connectorConfig = getSinkConnectorConfigProps();
    connectorConfig.put("iceberg.insert.mode", "upsert");
    connectorConfig.put("iceberg.id-fields", "title");
    task.start(connectorConfig);

    NexlaMessage message = createMessage(new LinkedHashMap<>(
        Map.of("title", "World of Warcraft", "release_date", "2004-11-23")
    ));

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null, JsonUtils.toJsonString(message), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    NexlaMessage upsertMessage = createMessage(new LinkedHashMap<>(
        Map.of("title", "World of Warcraft", "release_date", "2019-08-26")
    ));

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null, JsonUtils.toJsonString(upsertMessage), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    var probe = new IcebergConnectorService();
    SparkSession spark = probe.getSparkSession(new IcebergSinkConnectorConfig(connectorConfig), Collections.emptyMap());
    var df = spark.table("warcraft.expansions");
    var rows = df.sqlContext().sql("SELECT * FROM warcraft.expansions").collectAsList();
    var count = df.sqlContext().sql("SELECT * FROM warcraft.expansions").count();
    Assert.assertEquals(1, count);
    Assert.assertEquals("World of Warcraft", rows.get(0).<String>getAs("title"));
    Assert.assertEquals("2019-08-26", rows.get(0).<String>getAs("release_date"));
  }

  @Test
  public void shouldHandleCastLongToString() {
    var connectorConfig = getSinkConnectorConfigProps();
    connectorConfig.put(TABLE_NAME, TEST_TABLE_CAST_LONG_TO_STRING);
    task.start(connectorConfig);

    // age column will be defined as string
    NexlaMessage messageNumeric = createMessage(new LinkedHashMap<>(
            Map.of("person", "sue", "age", "32")
    ));
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null, JsonUtils.toJsonString(messageNumeric), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    // long can be cast to string
    NexlaMessage messageString = createMessage(new LinkedHashMap<>(
            Map.of("person", "sue", "age", 40)
    ));
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null, JsonUtils.toJsonString(messageString), 2)));

    Exception thrown = assertThrows(RuntimeException.class, () -> task.doFlush(ReadyToFlush.FLUSH));
    assertTrue(thrown.getMessage().contains("Cannot change column type: age: string -> long"));
// TODO broken fix
//    var probe = new IcebergConnectorService();
//    SparkSession spark = probe.getSparkSession(new IcebergSinkConnectorConfig(connectorConfig), Collections.emptyMap());
//    var df = spark.table(TEST_TABLE_TYPE_EVOLUTION_NAME);
//    var rows = df.sqlContext().sql(String.format("SELECT * FROM %s", TEST_TABLE_TYPE_EVOLUTION_NAME)).collectAsList();
//
//    Assert.assertEquals(2, rows.size());
//    Assert.assertEquals("sue", rows.get(0).<String>getAs("person"));
//    Assert.assertEquals("32", rows.get(0).<String>getAs("age"));
//    Assert.assertEquals("sue", rows.get(1).<String>getAs("person"));
//    Assert.assertEquals("40", rows.get(1).<String>getAs("age"));
  }

  @Test
  public void shouldHandleTypeEvolutionLongToString() {
    var connectorConfig = getSinkConnectorConfigProps();
    connectorConfig.put(TABLE_NAME, TEST_TABLE_TYPE_EVOLUTION_NAME);
    task.start(connectorConfig);

    // age column will be defined as long
    NexlaMessage messageNumeric = createMessage(new LinkedHashMap<>(
            Map.of("person", "sue", "age", 32)
    ));
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null, JsonUtils.toJsonString(messageNumeric), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    // temp table to be merged will have age as string, failing the cast of string to long
    NexlaMessage messageString = createMessage(new LinkedHashMap<>(
            Map.of("person", "sue", "age", "40")
    ));
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null, JsonUtils.toJsonString(messageString), 2)));

    Exception thrown = assertThrows(RuntimeException.class, () -> task.doFlush(ReadyToFlush.FLUSH));
    assertTrue(thrown.getMessage().contains("Cannot change column type: age: long -> string"));
// TODO broken fix
//    var probe = new IcebergConnectorService();
//    SparkSession spark = probe.getSparkSession(new IcebergSinkConnectorConfig(connectorConfig), Collections.emptyMap());
//    var df = spark.table(TEST_TABLE_TYPE_EVOLUTION_NAME);
//    var rows = df.sqlContext().sql(String.format("SELECT * FROM %s", TEST_TABLE_TYPE_EVOLUTION_NAME)).collectAsList();
//
//    Assert.assertEquals(2, rows.size());
//    Assert.assertEquals("sue", rows.get(0).<String>getAs("person"));
//    Assert.assertEquals("32", rows.get(0).<String>getAs("age"));
//    Assert.assertEquals("sue", rows.get(1).<String>getAs("person"));
//    Assert.assertEquals("40", rows.get(1).<String>getAs("age"));
  }


  private Map<String, String> getSinkConnectorConfigProps() {
    return new HashMap<>() {{
      put("bootstrap.servers", BOOTSTRAP_SERVERS);
      put(UNIT_TEST, "true");
      put(SINK_ID, "1");
      put(CREDS_ENC, "1");
      put(CREDS_ENC_IV, "1");
      put(CREDENTIALS_DECRYPT_KEY, "1");
      put(LISTING_ENABLED, "false");
      put(SINK_TYPE, ConnectionType.S3_ICEBERG.name());
      put(ACCESS_KEY_ID, localstack.getAccessKey());
      put(SECRET_KEY, localstack.getSecretKey());
      put(REGION, localstack.getRegion());
      put(SERVICE_ENDPOINT, localstack.getEndpointOverride(S3).toString());
      put(TABLE_NAME, TEST_TABLE_NAME);
      put("iceberg.warehouse.dir", "nexla-data-lake");
      put(SPARK_SESSION_CONFIGS, "spark.driver.bindAddress=127.0.0.1");
      put(MAX_FILE_SIZE_MB, "0");
      put(FILE_NAME_PREFIX, "my-dataset");
      put(MONITOR_POLL_MS, "1");
    }};
  }

  static NexlaMessage createMessage(LinkedHashMap<String, Object> rawMessage) {
    NexlaMessage message = new NexlaMessage(rawMessage);
    NexlaMetaData nexlaMetaData = new NexlaMetaData();
    nexlaMetaData.setRunId(1L);
    nexlaMetaData.setIngestTime(System.currentTimeMillis());
    message.setNexlaMetaData(nexlaMetaData);
    return message;
  }
}