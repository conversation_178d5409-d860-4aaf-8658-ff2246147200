package com.nexla.probe.s3;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.SdkBaseException;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.RegionUtils;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.AmazonS3EncryptionClientBuilder;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CryptoConfiguration;
import com.amazonaws.services.s3.model.CryptoMode;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.KMSEncryptionMaterialsProvider;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.MultiObjectDeleteException;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.model.SSEAwsKeyManagementParams;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import com.google.common.annotations.VisibleForTesting;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ListingResource;
import com.nexla.common.ListingResourceType;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.ResourceType;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.probe.ExceptionResolution;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.AWSAuthConfig.BucketPrefix;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.file.NexlaAWSCredentialsProvider;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.listing.client.ListingClient;
import lombok.SneakyThrows;
import net.sf.ehcache.util.NamedThreadFactory;
import one.util.streamex.AbstractStreamEx;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.AbstractConfig;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.amazonaws.regions.RegionUtils.getRegion;
import static com.nexla.common.ConnectionType.S3;
import static com.nexla.common.ListingResourceType.FOLDER;
import static com.nexla.common.datetime.DateTimeUtils.timed;
import static com.nexla.common.probe.ExceptionResolution.RETHROW;
import static com.nexla.common.probe.ExceptionResolution.RETRY;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.connector.config.file.CustomS3ServiceCredentialProvider.MERCURY_AUTH_TYPE;
import static com.nexla.connector.config.file.CustomS3ServiceCredentialProvider.NEPTUNE_AUTH_TYPE;
import static com.nexla.connector.config.file.CustomS3ServiceCredentialProvider.*;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.connector.config.file.DirScanningMode.DIRECTORIES;
import static com.nexla.connector.config.file.DirScanningMode.FILES;
import static com.nexla.connector.config.file.S3Constants.NEXLA_TEMP_FILE_NAME;
import static com.nexla.file.service.FileWalk.LevelFile;
import static com.nexla.file.service.FileWalk.walkFileTreeDfs;
import static java.lang.String.format;
import static java.util.Collections.emptyList;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.removeStart;
import static org.joor.Reflect.on;

public class S3ConnectorService extends FileConnectorService<AWSAuthConfig> {

	public static final String NX_EXTERNAL_MERCURY_HOST = "nx_external_mercury_host";
	public static final String NX_EXTERNAL_HOST = "nx_external_host";
	private static Logger logger = LoggerFactory.getLogger(S3ConnectorService.class);

	static final ForkJoinPool EXECUTOR = new ForkJoinPool(30);

	// The STS tokens need to be refreshed every 60 mins, using this to refresh every 55 mins
	private static final Integer MAX_TOKEN_DURATION = 55 * 60 * 1000;
	private static final Integer MINIMUM_MULTIPART_UPLOAD_THREADS = 5;
	private static final Integer MINIMUM_SINK_PARALLELISM_THREADS = 10;
	private static final Integer MAX_CONNECTIONS = ClientConfiguration.DEFAULT_MAX_CONNECTIONS;
	private DateTime lastRefreshTime;
	private AmazonS3 s3Client;
	private int adjustedMultipartUploadParallelism = -1;

	public S3ConnectorService() {
		this(null, null, null);
	}

	public S3ConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
		super(adminApiClient, listingClient, credentialsDecryptKey);
	}

	public static AmazonS3 customS3Client(AWSAuthConfig authConfig, AWSCredentialsProvider awsCredentialsProvider) {
		String externalHostURL = null;
		if (authConfig.originals().containsKey(NX_EXTERNAL_MERCURY_HOST)) {
			externalHostURL = authConfig.originals().get(NX_EXTERNAL_MERCURY_HOST).toString();
		} else {
			externalHostURL = authConfig.originals().get(NX_EXTERNAL_HOST).toString();
		}

		ClientConfiguration config = new ClientConfiguration();
		config.setProtocol(Protocol.HTTPS);
		config.setSignerOverride("S3SignerType");
		AwsClientBuilder.EndpointConfiguration endpointConfiguration = new AwsClientBuilder.EndpointConfiguration(
			externalHostURL, "");

		return AmazonS3ClientBuilder
			.standard()
			.withClientConfiguration(config)
			.withCredentials(awsCredentialsProvider)
			.withPathStyleAccessEnabled(true)
			.withEndpointConfiguration(endpointConfiguration)
			.build();
	}

	public static AmazonS3 createS3ClientFromCreds(AWSAuthConfig authConfig, String region) {
		AWSCredentialsProvider credentialsProvider = NexlaAWSCredentialsProvider.getCredentialsProvider(authConfig);
		if (MERCURY_AUTH_TYPE.equalsIgnoreCase(authConfig.customAuthType) ||
			NEPTUNE_AUTH_TYPE.equalsIgnoreCase(authConfig.customAuthType) ||
			MIN_IO_AUTH_TYPE.equalsIgnoreCase(authConfig.customAuthType)) {
			return customS3Client(authConfig, credentialsProvider);
		} else {
			return genericS3Client(authConfig, region, credentialsProvider);
		}
	}

	private static AmazonS3 genericS3Client(AWSAuthConfig authConfig, String region, AWSCredentialsProvider credentialsProvider) {
		if ((region == null) && authConfig.region != null) {
			region = authConfig.region;
		}
		if (region == null) {
			region = "us-east-1";
		}
		String finalRegion = region;
		AmazonS3 client = authConfig
			.cryptoMode
			.map(x -> {
				try {
					return CryptoMode.valueOf(x);
				} catch (Exception e) {
					return null;
				}
			})
			.<AmazonS3>map(cm -> {
					AmazonS3EncryptionClientBuilder builder = AmazonS3EncryptionClientBuilder
						.standard()
						.withCredentials(credentialsProvider)
						.withForceGlobalBucketAccessEnabled(true)
						.withCryptoConfiguration(new CryptoConfiguration(cm).withAwsKmsRegion(getRegion(finalRegion)))
						.withEncryptionMaterials(new KMSEncryptionMaterialsProvider(authConfig.kmsKey.get()));

					authConfig.serviceEndpoint
						.ifPresentOrElse(
							x -> builder.setEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(x, finalRegion)),
							() -> builder.setRegion(finalRegion));

					return builder.build();
				}
			)
			.orElseGet(() -> {
				AmazonS3ClientBuilder builder = AmazonS3ClientBuilder.standard()
					.withCredentials(credentialsProvider)
					.withForceGlobalBucketAccessEnabled(true);

				authConfig.serviceEndpoint
					.ifPresentOrElse(
						x -> builder.setEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(x, finalRegion)),
						() -> builder.setRegion(finalRegion));

				return builder.build();
			});
		//this is w/a to update region afterwards
		on(client).set("isImmutable", false);

		return client;
	}

	@VisibleForTesting
	AmazonS3 getS3ClientFromCreds(AWSAuthConfig authConfig, String region) {

		try {
			// Return same client if the
			if (!(MERCURY_AUTH_TYPE.equalsIgnoreCase(authConfig.customAuthType) ||
				NEPTUNE_AUTH_TYPE.equalsIgnoreCase(authConfig.customAuthType) ||
				MIN_IO_AUTH_TYPE.equalsIgnoreCase(authConfig.customAuthType)) &&
				s3Client != null &&
				DateTime.now().isBefore(lastRefreshTime.getMillis() + MAX_TOKEN_DURATION)) {

				if (region != null) {
					s3Client.setRegion(RegionUtils.getRegion(region));
				}
				return s3Client;
			}
			s3Client = createS3ClientFromCreds(authConfig, region);
			lastRefreshTime = DateTime.now();
			return s3Client;

		} catch (Exception e) {
			throw new ProbeRetriableException(e);
		}
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	@Override
	public AuthResponse authenticate(AWSAuthConfig authConfig) {
		try {
			AmazonS3 s3Client = getS3ClientFromCreds(authConfig, authConfig.region);
			if (authConfig.testPath.isPresent()) {
				BucketPrefix bucketPrefix = toBucketPrefix(authConfig.testPath.get(), true);
				listObjects(s3Client, bucketPrefix.bucket, bucketPrefix.prefix);
			} else {
				s3Client.listBuckets();
			}
			s3Client.shutdown();
			return SUCCESS;
		} catch (Exception e) {
			logger.error("Exception while authenticating, credsId={}", authConfig.getCredsId(), e);
			return authError(e);
		}
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;

		if (config.listingMultipathEnabled) {
			logger.warn("listing multipath mode enabled");
			String[] rawPaths = config.path.split("#");

			ArrayList<StreamEx<NexlaFile>> components = new ArrayList<>();
			for (String currentPath: rawPaths) {
				StreamEx<NexlaFile> subListing = listBucketContentsInternal(currentPath, config);
				components.add(subListing);
			}
			// then, merge all the things
			Optional<StreamEx<NexlaFile>> maybeFinal = components.stream().reduce(AbstractStreamEx::prepend);
			if (maybeFinal.isPresent()) {
				return maybeFinal.get();
			} else {
				logger.warn("empty merge result, returning empty streamex");
				return StreamEx.of();
			}
		} else {
			return listBucketContentsInternal(config.path, config);
		}
	}

	private StreamEx<NexlaFile> listBucketContentsInternal(String path, FileSourceConnectorConfig config) {
		BucketPrefix bucketPrefix = toBucketPrefix(path, true);

		StreamEx<S3ObjectSummary> objectStream = listS3Objects(config, bucketPrefix.bucket, bucketPrefix.prefix);

		if (config.dirScanningMode == FILES) {
			return objectStream
				.filter(file -> !file.getKey().endsWith("/"))
				.filter(file -> com.nexla.common.FileUtils.filterIfDeltaFile(config.getConnectionType(), file.getKey()))
				.map(file -> toNexlaFile(file, FILES));
		} else {
			return objectStream.map(file -> toNexlaFile(file, DIRECTORIES));
		}
	}

	public StreamEx<S3ObjectSummary> listS3Objects(AWSAuthConfig config, String region, String bucket, String prefix) {
		AmazonS3 s3Client = getS3ClientFromCreds(config, region);
		return StreamEx
			.iterate(listObjects(s3Client, bucket, prefix), s3Client::listNextBatchOfObjects)
			.takeWhileInclusive(ObjectListing::isTruncated)
			.flatMap(objectListing -> objectListing.getObjectSummaries().stream());
	}

	public StreamEx<S3ObjectSummary> listS3Objects(FileSourceConnectorConfig config, String bucket, String prefix) {
		return listS3Objects(config.getAuthConfig().asAWS(), config.region, bucket, prefix);
	}

	private NexlaFile toNexlaFile(S3ObjectSummary s, DirScanningMode scanningMode) {
		ListingResourceType mode = scanningMode == FILES ? ListingResourceType.FILE : FOLDER;
		long time = s.getLastModified().getTime();
		return new NexlaFile(s.getKey(), s.getSize(), s.getBucketName(), s.getETag(), time, time, mode);
	}

	@Override
	public boolean doesFileExistsInternal(FileConnectorAuth config, String fileNameNoBucket) {

		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);
		AmazonS3 s3 = getS3ClientFromCreds(config.getAuthConfig().asAWS(), config.getRegion());
		return s3.doesObjectExist(bucketPrefix.bucket, fileNameNoBucket);
	}

	@Override
	public int calculateSinkParallelism(FileConnectorAuth c) {
		if (!(c instanceof FileSinkConnectorConfig)) {
			return super.calculateSinkParallelism(c);
		}

		FileSinkConnectorConfig config = (FileSinkConnectorConfig) c;

		int requested;
		requested = config.sinkParallelism * config.s3uploadParallelism;

		if (requested <= MAX_CONNECTIONS) {
			return config.sinkParallelism;
		}

		int tunedSinkParallelism = config.sinkParallelism;
		int tunedMultipartParallelism = config.s3uploadParallelism;

		// sink parallelism should be set with respect to the dataset and its partition keys so reduce multipart first
		while ((requested > MAX_CONNECTIONS)
				&& (tunedMultipartParallelism > MINIMUM_MULTIPART_UPLOAD_THREADS)) {
			tunedMultipartParallelism -= 1;
			requested = tunedSinkParallelism * tunedMultipartParallelism;
		}

		while ((requested > MAX_CONNECTIONS)
				&& (tunedSinkParallelism > MINIMUM_SINK_PARALLELISM_THREADS)) {
			tunedSinkParallelism -= 1;
			requested = tunedSinkParallelism * tunedMultipartParallelism;
		}

		// ! store the tuned multipart thread count for use later during writing
		this.adjustedMultipartUploadParallelism = tunedMultipartParallelism;

		return tunedSinkParallelism;
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
		AmazonS3 s3Client = getS3ClientFromCreds(connectorConfig.getAuthConfig().asAWS(), connectorConfig.region);
		return StreamEx
			.of(s3Client.listBuckets())
			.map(bucket -> new NexlaBucket(bucket.getName()));
	}

	@Override
	public InputStream readInputStreamInternal(FileConnectorAuth config, String file) {
		return rethrowRetriableException(() -> {
			String bucket = toBucketPrefix(config.getPath(), true).bucket;
			AmazonS3 s3Client = getS3ClientFromCreds(config.getAuthConfig().asAWS(), config.getRegion());
			S3Object object = s3Client.getObject(new GetObjectRequest(bucket, file));
			return object.getObjectContent();
		});
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig c) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		AmazonS3 s3Client = getS3ClientFromCreds(config.getAuthConfig().asAWS(), config.getRegion());

		String bucket = toBucketPrefix(config.getPath(), false).bucket;
		rethrowRetriableException(() -> {
			s3Client.putObject(bucket, NEXLA_TEMP_FILE_NAME, "test");
			s3Client.deleteObject(bucket, NEXLA_TEMP_FILE_NAME);
			return null;
		});
		return true;
	}

	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String key, File file) {
		timed(() -> {
			BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);

			AmazonS3 s3Client = getS3ClientFromCreds(config.getAuthConfig().asAWS(), config.getRegion());

			if (config instanceof FileSinkConnectorConfig) {
				FileSinkConnectorConfig fileSinkConfig = (FileSinkConnectorConfig) config;
				int parallelism = adjustedMultipartUploadParallelism == -1 ?
						fileSinkConfig.s3uploadParallelism : adjustedMultipartUploadParallelism;
				ExecutorService executorService = Executors.newFixedThreadPool(
					parallelism,
					new NamedThreadFactory("s3-upload-sink" + fileSinkConfig.sinkId + "-" + key));

				TransferManager tm = TransferManagerBuilder
					.standard()
					.withS3Client(s3Client)
					.withExecutorFactory(() -> executorService)
					.withMultipartUploadThreshold(fileSinkConfig.s3uploadPartMb * 1024L * 1024L)
					.build();

				try {
					rethrowRetriableException(() -> {
                        uploadToS3(config, bucketPrefix, tm, key, file);
						return null;
					});
				} finally {
					tm.shutdownNow(false);
				}
			} else {
				rethrowRetriableException(() -> s3Client.putObject(bucketPrefix.bucket, key, file));
			}
			if (config.getAuthConfig().asAWS().bucketOwnerFullControl) {
				s3Client.setObjectAcl(bucketPrefix.bucket, key, CannedAccessControlList.BucketOwnerFullControl);
			}
		}, format("Upload of file " + key + " size=%.2f mb", ((double) file.length() / 1024 / 1024)));

		return new FileDetails(key, empty(), empty());
	}

    @SneakyThrows
    private void uploadToS3(FileConnectorAuth config, BucketPrefix bucketPrefix, TransferManager tm, String key, File file) {
        Upload upload;
        if (config.getAuthConfig().asAWS().sseEnabled) {
            PutObjectRequest putRequest;
            if (config.getAuthConfig().asAWS().sseKmsKey.isPresent()) {

                putRequest = new PutObjectRequest(bucketPrefix.bucket, key, file)
                        .withSSEAwsKeyManagementParams(new SSEAwsKeyManagementParams(config.getAuthConfig().asAWS().sseKmsKey.get()));
            } else {
                byte[] objectBytes = FileUtils.readFileToByteArray(file);

                ObjectMetadata objectMetadata = new ObjectMetadata();
                objectMetadata.setContentLength(objectBytes.length);
                objectMetadata.setSSEAlgorithm(ObjectMetadata.AES_256_SERVER_SIDE_ENCRYPTION);

                putRequest = new PutObjectRequest(bucketPrefix.bucket,
                        key,
                        new ByteArrayInputStream(objectBytes),
                        objectMetadata);
            }

            upload = tm.upload(putRequest);
        } else {
            upload = tm.upload(bucketPrefix.bucket, key, file);
        }

        upload.waitForUploadResult();
    }

	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String key, InputStream inputStream) {
		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);
		AmazonS3 s3Client = getS3ClientFromCreds(config.getAuthConfig().asAWS(), config.getRegion());
		rethrowRetriableException(() -> s3Client.putObject(bucketPrefix.bucket, key, inputStream, null));
		return new FileDetails(key, empty(), empty());
	}

	private <T> T rethrowRetriableException(Supplier<T> supplier) {
		try {
			return supplier.get();
		} catch (AmazonServiceException e) {
			if ((e.getStatusCode() == 500) && e.getErrorMessage().toLowerCase().contains("try again")) {
				throw new ProbeRetriableException(e);
			} else {
				throw e;
			}
		}
	}

	@Override
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig configTemp) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) configTemp;

		AWSAuthConfig authConfig = config.getAuthConfig().asAWS();
		AmazonS3 s3Client = getS3ClientFromCreds(authConfig, config.region);

		String defaultRegion = Optional.ofNullable(config.region).orElse("us-east-1");

		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);
		if (authConfig.testPath.isPresent() || StringUtils.isNotEmpty(bucketPrefix.bucket)) {

			final String bucket;
			final String prefix;
			if (StringUtils.isNotEmpty(bucketPrefix.bucket)) {
				bucket = bucketPrefix.bucket;
				prefix = bucketPrefix.prefix;
			} else {
				BucketPrefix testBucketPrefix = toBucketPrefix(authConfig.testPath.get(), true);
				bucket = testBucketPrefix.bucket;
				prefix = testBucketPrefix.prefix;
			}

			return StreamEx
				.of(
					new RegionHelper()
						.withRegionRetrySimple(
							s3Client,
							client -> listPrefixesAndFiles(
								client,
								bucket,
								prefix,
								config.depth).toList())
						.orElse(emptyList())
				)
				.map(res -> toNexlaFile(bucket, res));

		} else {

			StreamEx<NexlaBucket> buckets = listBuckets(config);
			if (config.depth == 1) {
				return buckets.map(b -> toNexlaFile(b.getName(), new ListingResource("", FOLDER, empty())));
			} else {
				Map<String, Function<AmazonS3, List<ListingResource>>> processingMap = buckets.toMap(
					NexlaBucket::getName,
					b -> client -> listPrefixesAndFiles(client, b.getName(), bucketPrefix.prefix, config.depth - 1).toList());

				Map<String, List<ListingResource>> bucketToListingMap = new RegionHelper()
					.withRegionRetryMultiThreaded(s3Client, defaultRegion, authConfig, processingMap);

				return EntryStream
					.of(bucketToListingMap)
					.flatMapKeyValue((bucket, listing) -> listing.isEmpty()
						? StreamEx.of(toNexlaFile(bucket, new ListingResource("", FOLDER, empty())))
						: StreamEx.of(listing).map(res -> toNexlaFile(bucket, res)));
			}
		}
	}

	private NexlaFile toNexlaFile(String bucket, ListingResource<S3ObjectSummary> commonPrefix) {
		Long lastModified = commonPrefix.getSummary().map(x -> x.getLastModified().getTime()).orElse(null);
		Long createdAt = lastModified;
		Long size = commonPrefix.getSummary().map(x -> x.getSize()).orElse(null);
		return new NexlaFile(bucket + "/" + commonPrefix.getValue(), size, null, null, createdAt, lastModified, commonPrefix.getType());
	}

	@VisibleForTesting
	StreamEx<ListingResource> listPrefixesAndFiles(AmazonS3 s3Client, String bucket, String prefix, int maxDepth) {

		Supplier<StreamEx<LevelFile<ListingResource>>> firstLayer = () ->
			listLevel(s3Client, bucket, prefix)
				.map(file -> new LevelFile<>(1, file, null, file.getType() == FOLDER));

		StreamEx<LevelFile<ListingResource>> fileStream =
			walkFileTreeDfs(
				BOTH,
				firstLayer,
				file -> nextStream(s3Client, bucket, file, maxDepth));

		if (maxDepth > 1) {
			fileStream.parallel(EXECUTOR);
		}

		return fileStream.map(f -> f.file);
	}


	private StreamEx<ListingResource> listLevel(AmazonS3 s3Client, String bucket, String prefix) {
		ListObjectsRequest listObjectsRequest = new ListObjectsRequest()
			.withBucketName(bucket)
			.withPrefix(removeStart(prefix, "/"))
			.withDelimiter("/");

		List<ListingResource> resources = new ArrayList<>();
		ObjectListing listing;

		do {
			listing = s3Client.listObjects(listObjectsRequest);
			List<String> commonPrefixes = listing.getCommonPrefixes();

			resources.addAll(
				StreamEx.of(listing.getObjectSummaries())
					.filter(this::isFile)
					.map(obj -> new ListingResource(obj.getKey(), ListingResourceType.FILE, ofNullable(obj)))
					.toList()
			);

			resources.addAll(
				StreamEx.of(commonPrefixes)
					.map(commonPrefix -> new ListingResource(commonPrefix, FOLDER, empty()))
					.toList()
			);

			listObjectsRequest.setMarker(listing.getNextMarker());
		} while (listing.isTruncated());

		return StreamEx.of(resources);
	}

	private StreamEx<LevelFile<ListingResource>> nextStream(AmazonS3 s3Client, String bucket, LevelFile<ListingResource> levelFile, int maxDepth) {
		ListingResource currFile = levelFile.file;
		if (levelFile.level == maxDepth || currFile.getType() == ListingResourceType.FILE) {
			return StreamEx.empty();
		} else {
			return StreamEx
				.of(listLevel(s3Client, bucket, currFile.getValue()))
				.map(f -> new LevelFile(levelFile.level + 1, f, f.getValue(), f.getType() == FOLDER));
		}
	}

	private boolean isFile(S3ObjectSummary key) {
		return !key.getKey().endsWith("/");
	}

	@Override
	public void deleteByName(AbstractConfig c, final List<String> keys) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		AmazonS3 s3Client = getS3ClientFromCreds(config.getAuthConfig().asAWS(), config.getRegion());
		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);

		DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(bucketPrefix.bucket);
		deleteRequest.setKeys(keys.stream().map(DeleteObjectsRequest.KeyVersion::new).collect(Collectors.toList()));
		s3Client.deleteObjects(deleteRequest);
	}

	@Override
	public void clearBucket(AbstractConfig c) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		AmazonS3 s3Client = getS3ClientFromCreds(config.getAuthConfig().asAWS(), config.getRegion());
		ObjectListing objectListing = null;
		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);

		do {
			if (objectListing == null) {
				objectListing = listObjects(s3Client, bucketPrefix.bucket, bucketPrefix.prefix);
			} else {
				objectListing = s3Client.listNextBatchOfObjects(objectListing);
			}

			List<S3ObjectSummary> objectSummaries = objectListing.getObjectSummaries();
			if (!objectSummaries.isEmpty()) {
				DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(bucketPrefix.bucket);
				deleteRequest.setKeys(
					objectSummaries.stream()
						.map(s3ObjectSummary -> new DeleteObjectsRequest.KeyVersion(s3ObjectSummary.getKey()))
						.collect(Collectors.toList()));

				logger.info("Deleting {} files to clear the bucket {}", deleteRequest.getKeys().size(), bucketPrefix.bucket);
				try {
					s3Client.deleteObjects(deleteRequest);
				} catch (MultiObjectDeleteException e) {
					List<MultiObjectDeleteException.DeleteError> errors = e.getErrors();
					errors.forEach(error -> logger.error("Error to delete {} file: [{}] {}", error.getKey(), error.getCode(), error.getMessage()));
				}
			}

		} while (objectListing.isTruncated());
	}

	private ObjectListing listObjects(AmazonS3 s3Client, String bucket, String prefixNullable) {
		String prefix = ofNullable(prefixNullable)
			.map(p -> removeStart(p, "/"))
			.orElse("");

		ListObjectsRequest listObjectsRequest = new ListObjectsRequest()
			.withBucketName(bucket)
			.withPrefix(prefix);

		return s3Client.listObjects(listObjectsRequest);
	}

	public void deleteObject(AWSAuthConfig config, String bucketName, String key){
		AmazonS3 client = getS3ClientFromCreds(config, config.region);
		client.deleteObject(bucketName, key);
	}

	public void putObject(AWSAuthConfig config, String bucketName, String key, File file){
		AmazonS3 client = getS3ClientFromCreds(config, config.region);
		client.putObject(bucketName, key, file);
	}

	@Override
	public void createDestination(AbstractConfig c, Optional<Integer> sourceId) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		AWSAuthConfig authConfig = config.getAuthConfig().asAWS();
		AmazonS3 s3Client = getS3ClientFromCreds(authConfig, authConfig.region);
		BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);

		String destination = bucketPrefix.prefix;

		// All folders in S3 should end with /
		if (!destination.endsWith("/")) {
			destination += "/";
		}

		s3Client.putObject(bucketPrefix.bucket, destination, "");
	}

	@Override
	protected Optional<ExceptionResolution> findResolution(Throwable e) {
		if (e instanceof SdkBaseException) {
			if (e instanceof AmazonS3Exception) {
				AmazonS3Exception s3Exception = (AmazonS3Exception) e;
				if (s3Exception.getStatusCode() == 403) {
					return Optional.of(RETHROW);
				}
			}
			return Optional.of(RETRY);
		} else {
			return empty();
		}
	}
}
