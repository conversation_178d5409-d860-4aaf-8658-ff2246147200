package com.nexla.connector.file.source;

import com.google.common.annotations.VisibleForTesting;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.client.DataSourceRunId;
import com.nexla.admin.client.OwnerAndOrg;
import com.nexla.common.NexlaFile;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.connect.common.ConnectWorkarounds;
import com.nexla.connect.common.NexlaConnectorUtils;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.kafka.KafkaMessageTransport;
import com.nexla.listing.client.ListedFile;
import com.nexla.listing.client.ListingClient;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.AllArgsConstructor;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.kafka.connect.connector.ConnectorContext;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;

import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.ListingResourceType.FILE;
import static com.nexla.common.NexlaFile.FileSourceType.REINGEST;
import static com.nexla.common.NotificationEventType.EMPTY_DATA;
import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.NotificationUtils.notifyErrorMessage;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.connector.config.file.AWSAuthConfig.getDirectoryPath;
import static com.nexla.connector.file.source.FileMonitoringUtils.getFilesFromStorage;
import static com.nexla.connector.file.source.OffsetUtils.createOffsetMap;
import static com.nexla.connector.file.source.OffsetUtils.createPartitionMap;
import static com.nexla.connector.file.source.RebalanceResolution.*;
import static com.nexla.connector.file.source.TransportFileReader.toTransportFile;
import static com.nexla.connector.properties.FileConfigAccessor.PARTITION_KEY;

import static com.nexla.control.ListingFileStatus.*;
import static java.util.Collections.singletonMap;
import static java.util.Optional.*;
import static java.util.stream.Collectors.toList;

/**
 * Task that monitors for changes to a s3 bucket that this connector should
 * load data from.
 */
public class RebalancingTask {

	private final Logger logger;

	private final FileSourceConnectorConfig config;
	private final ConnectorContext context;

	private final FileListing fileListing = new FileListing();

	private final OffsetStorageReader offsetReader;

	private final ConnectorService<?> probeService;

	private final ConnectWorkarounds workarounds;
	private AdminApiClient adminApiClient;

	private NexlaMessageProducer nexlaMessageProducer;

	private final ReentrantLock fileStateLock = new ReentrantLock();

	private ListingClient listingClient;

	private static class FileListing {
		private Map<String, TransportFile> current = new HashMap<>();
		private Map<String, TransportFile> next = new HashMap<>();
	}

	RebalancingTask(
			AdminApiClient adminApiClient,
			FileSourceConnectorConfig config,
			ConnectorContext context,
			OffsetStorageReader offsetReader,
			ConnectorService probeService,
			ConnectWorkarounds workarounds,
			RestTemplate restTemplate,
			NexlaSslContext sslContext
	) {
		this.config = new FileSourceConnectorConfig(config.originals());

		config.dirScanningMode = DirScanningMode.FILES;

		this.workarounds = workarounds;

		this.context = context;
		this.offsetReader = offsetReader;

		this.probeService = probeService;

		this.logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()), new NexlaLogKey(SOURCE, config.sourceId, Optional.empty()));

		setListingClient( new ListingClient(config.listingAppServer, config.nexlaUsername, config.nexlaPassword, restTemplate));
		initKafkaProducer(sslContext);
		setAdminApiClient(adminApiClient);
	}

	@VisibleForTesting
	void initKafkaProducer(NexlaSslContext sslContext) {
		this.nexlaMessageProducer = new NexlaMessageProducer(
				new KafkaMessageTransport(config.bootstrapServers, sslContext, config.topicMetrics, config.topicNotify));
	}

	@VisibleForTesting
	void setListingClient(ListingClient listingClient) {
		this.listingClient = listingClient;
	}

	@VisibleForTesting
	void setAdminApiClient(AdminApiClient adminApiClient) {
		this.adminApiClient = adminApiClient;
	}

	public void run() {
		boolean shouldRebalance = updateFiles().shouldRebalance;

		if (shouldRebalance) {
			refreshCurrentFiles();
			context.requestTaskReconfiguration();
		}
	}

	@VisibleForTesting
	void refreshCurrentFiles() {
		fileListing.current = new HashMap<>(fileListing.next);
	}

	synchronized List<TransportFile> files() {
		fileStateLock.lock();
		try {
			return new ArrayList<>(fileListing.current.values());
		} finally {
			fileStateLock.unlock();
		}
	}

	List<TransportFile> updateAndRefresh() {
		fileStateLock.lock();
		try {
			updateFiles();
			refreshCurrentFiles();
			return files();
		} finally {
			fileStateLock.unlock();
		}
	}

	/**
	 * Update files and return resolution if rebalance is needed
	 */
	@VisibleForTesting
	RebalanceResolution updateFiles() {
		fileStateLock.lock();
		try {
			Map<String, NexlaFile> filesFromStorage = getFilesFromStorage(config, probeService);
			Optional<ReingestionInfo> reingestionInfo = collectFilesToReingest();

			final CategorizedFiles cat;

			Map<String, NexlaFile> reingestFiles = reingestionInfo.map(a -> a.reingestFiles).orElseGet(Collections::emptyMap);
			cat = divideFilesByCategories(filesFromStorage, fileListing.current, reingestFiles);
			handleEmptyFiles(cat);
			logFiles(cat);
			updateFileStates(cat);

			boolean hasUpdates = !cat.newFiles.isEmpty() || !cat.changedFiles.isEmpty();
			boolean hasErrorFiles = !cat.erroneousFiles.isEmpty();
			boolean hasInProgressFiles = !cat.filesInProgress.isEmpty();

			if (hasUpdates) {
				Map<String, TransportFile> files = new HashMap<>(cat.changedFiles);
				files.putAll(cat.newFiles);
				publishMetricsForNewOrUpdatedFiles(files.values());
			}

			// wait until current files are processed completely, not reconfiguring
			if (hasInProgressFiles) {
				logger.info("NOT RECONFIGURING: Waiting until current files are processed completely, not reconfiguring");
				return NO_REBALANCE_FILES_IN_PROGRESS;
			}

			if (hasUpdates) {
				// marking re-ingest request as processed only when re-balancing caused by new files is initiated
				reingestionInfo
						.filter(info -> !info.requestIds.isEmpty())
						.ifPresent(info ->
								info.requestIds.forEach(fileId ->
										listingClient.setFileStatus(config.sourceId, fileId, DONE, empty(), empty())));

				logger.info("RECONFIGURING: There are new/changed files to continue to read");
				return REBALANCE_HAS_UPDATES;
			}

			if (hasErrorFiles) {
				logger.info("RECONFIGURING: There are erroneous files to retry to read");
				return REBALANCE_HAS_ERROR_FILES_TO_RETRY;
			}

			logger.info("NOT RECONFIGURING");
			return NO_REBALANCE_NO_CHANGES;

		} catch (Exception e) {
			logger.error("Error in monitoring thread, will wait for next iteration", e);

			String directoryPath = getDirectoryPath(config.sourceType, config.path);

			NexlaConnectorUtils.sendNexlaNotificationEvent(
					nexlaMessageProducer, ERROR, 0L, SOURCE, config.sourceId, directoryPath,
					0L, notifyErrorMessage(getErrorDetails(e, empty())));
			publishMonitoringLog(
					0L,
					notifyErrorMessage(getErrorDetails(e, empty())),
					NexlaMonitoringLogType.LOG,
					NexlaMonitoringLogSeverity.ERROR);

			return NO_REBALANCE_EXCEPTION;
		} finally {
			fileStateLock.unlock();
		}
	}

	private void publishMonitoringLog(Long runId, String log, NexlaMonitoringLogType logType, NexlaMonitoringLogSeverity severity) {
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				adminApiClient.getDataSource(config.sourceId).get().getOrg().getId(),
				runId,
				config.sourceId,
				SOURCE,
				log,
				logType,
				severity,
				System.currentTimeMillis());
		nexlaMessageProducer.publishMonitoringLog(monitoringLogEvent);
	}

	private void updateFileStates(CategorizedFiles cat) {
		fileListing.next.clear();
		fileListing.next.putAll(cat.filesInProgress);
		fileListing.next.putAll(cat.newFiles);
		fileListing.next.putAll(cat.changedFiles);
		fileListing.next.putAll(cat.filesToContinue);
		fileListing.next.putAll(cat.erroneousFiles);
		fileListing.next.putAll(cat.skippedFiles);
	}

	private void logFiles(CategorizedFiles cat) {
		logger.info(
				"FILE STATE\n" +
						"files in progress: {}\n" +
						"new files: {}\n" +
						"changed files: {}\n" +
						"files to continue reading: {}\n" +
						"erroneous files: {}\n" +
						"skipped files: {}\n",
				cat.filesInProgress.size(),
				cat.newFiles.size(),
				cat.changedFiles.size(),
				cat.filesToContinue.size(),
				cat.erroneousFiles.size(),
				cat.skippedFiles.size());
	}

	private void handleEmptyFiles(CategorizedFiles categorized) {
		List<TransportFile> newEmptyFiles = selectNewEmptyFiles(categorized);
		newEmptyFiles.removeIf(file -> fileListing.next.containsKey(file.nexlaFile.getFullPath()));

		newEmptyFiles.forEach(file ->
				workarounds.writeOffsetDirectly(
						createPartitionMap(config.listingEnabled, file.nexlaFile),
						createOffsetMap(file.nexlaFile, true, 0L, false, false)));

		publishMetricsForNewOrUpdatedFiles(newEmptyFiles);

		sendNewEmptyFileAlert(newEmptyFiles);
		removeEmptyFiles(categorized);
	}

	@AllArgsConstructor
	static class ReingestionInfo {
		final Map<String, NexlaFile> reingestFiles;
		final List<Long> requestIds;
	}

	Optional<ReingestionInfo> collectFilesToReingest() {
		try {
			List<ListedFile> filesToReingest = listingClient.listFiles(config.sourceId, of(REINGEST), newHashSet(NEW), empty(), empty(), empty(), empty(), empty());
			filesToReingest.forEach(file -> {
				logger.info("Reingestion request: {}", file.fullPath);

				// Probably not a best way to set null/0 for size/hash/lastModified.
				// On the other hand, odds are in favor of real metadata read in getFilesFromStorage() method
				NexlaFile nexlaFile = new NexlaFile(file.fullPath, 0L, null, null, null, null, FILE);

				// zeroing offsets to initiate re-ingestion
				workarounds.writeOffsetDirectly(
						createPartitionMap(false, nexlaFile),
						createOffsetMap(nexlaFile, false, 0L, false, false));
			});

			List<Long> requestIds = StreamEx.of(filesToReingest).map(f -> f.id).toList();

			Map<String, NexlaFile> reingestFiles = StreamEx.of(filesToReingest)
					.map(file -> new NexlaFile(file.fullPath, 0L, null, null, null, null, FILE))
					.toMap(NexlaFile::getFullPath, Function.identity());

			return of(new ReingestionInfo(reingestFiles, requestIds));

		} catch (Exception e) {
			logger.error("Failed to process re-ingestion requests", e);
			return empty();
		}
	}

	private void sendNewEmptyFileAlert(List<TransportFile> transportFiles) {
		transportFiles.forEach(file -> {
			NexlaConnectorUtils.sendNexlaNotificationEvent(nexlaMessageProducer, EMPTY_DATA, 0L,
					SOURCE, config.sourceId, file.nexlaFile.getFullPath(), file.nexlaFile.getSize(), "Empty file in source");
			publishMonitoringLog(0L,"Empty file in source", NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO);
		});
	}

	private List<TransportFile> selectNewEmptyFiles(CategorizedFiles categorized) {
		return categorized.newFiles.values().stream()
				.filter(val -> val.nexlaFile.getSize() == 0)
				.map(file -> new TransportFile(file.nexlaFile, file.lastMessageNumber, true))
				.collect(toList());
	}

	private void removeEmptyFiles(CategorizedFiles categorized) {
		categorized.changedFiles.values().removeIf(val -> val.nexlaFile.getSize() == 0);
		categorized.filesInProgress.values().removeIf(val -> val.nexlaFile.getSize() == 0);
		categorized.erroneousFiles.values().removeIf(val -> val.nexlaFile.getSize() == 0);
		categorized.filesToContinue.values().removeIf(val -> val.nexlaFile.getSize() == 0);
		categorized.newFiles.values().removeIf(val -> val.nexlaFile.getSize() == 0);
	}

	@VisibleForTesting
	void publishMetricsForNewOrUpdatedFiles(Collection<TransportFile> files) {

		Optional<DataSource> dataSource = adminApiClient.getDataSource(config.sourceId);
		List<DataSourceRunId> runIds = dataSource.get().getRunIds();

		Long runId = StreamEx.of(runIds)
				.map(DataSourceRunId::getId)
				.reverseSorted()
				.limit(1)
				.toList()
				.get(0);

		files.forEach(file -> {
			Optional<Integer> dataSetId = ofNullable(file.ctx).map(x -> x.schema).map(x -> x.dataSetId);
			String fullPath = file.ctx.nexlaFile.getFullPath();
			Integer orgId = dataSource.map(OwnerAndOrg::getOrgId).orElse(null);
			Integer ownerId = dataSource.map(OwnerAndOrg::getOwnerId).orElse(null);
			NexlaConnectorUtils.publishMetrics(nexlaMessageProducer, SOURCE, config.sourceId, fullPath, 0L, 0L, 0L,
					DateTimeUtils.nowUTC().getMillis(), ofNullable(runId), Optional.of(file.eofReached),
					Optional.ofNullable(file.ctx.nexlaFile.getMd5()), Optional.empty(), dataSetId, Optional.of(file.ctx.nexlaFile.getLastModified()), Optional.of(file.ctx.messageNumber), Optional.empty(),
					Optional.of(fullPath), FlowType.STREAMING, orgId, ownerId);
		});
	}

	CategorizedFiles divideFilesByCategories(
			Map<String, NexlaFile> filesFromStorage,
			Map<String, TransportFile> fileMap,
			Map<String, NexlaFile> filesToReingest
	) {
		CategorizedFiles categorized = new CategorizedFiles();

		Map<String, Optional<TransportFile>> kafkaVersions = getFileReadingState(filesFromStorage);

		filesFromStorage.forEach((fullPath, fileFromStorage) -> {

			if (filesToReingest.containsKey(fullPath)) {
				// re-ingesting file
				categorized.newFiles.put(fullPath, new TransportFile(fileFromStorage, 0, false));
				return;
			}

			Optional<TransportFile> kafkaVersionOpt = kafkaVersions.get(fullPath);
			TransportFile connectorVersion = fileMap.get(fullPath);
			if (kafkaVersionOpt.isPresent()) {

				TransportFile kafkaVersion = kafkaVersionOpt.get();

				if (kafkaVersion.skip) {
					categorized.skippedFiles.put(kafkaVersion.nexlaFile.getFullPath(), kafkaVersion);
					return;
				}

				if (kafkaVersion.error) {
					categorized.erroneousFiles.put(kafkaVersion.nexlaFile.getFullPath(), kafkaVersion);
					return;
				}

				if (!kafkaVersion.eofReached) {

					// in progress or erroneous files
					if (connectorVersion != null) {

						categorized.filesInProgress.put(kafkaVersion.nexlaFile.getFullPath(), kafkaVersion);

					} else {

						// no connector version and EOF not reached - perhaps, after connector restart
						boolean wasChanged = isFileChanged(fileFromStorage, kafkaVersion.nexlaFile);

						// check if file was changed to determine either to continue reading from last processed line of from the beginning
						if (wasChanged) {
							if (config.appendMode) {
								logger.info("File changed, start reading from last message={} (append mode), file={}",
										kafkaVersion.lastMessageNumber, kafkaVersion.nexlaFile.getFullPath());

								TransportFile transportFile = new TransportFile(fileFromStorage, kafkaVersion.lastMessageNumber, false);
								categorized.changedFiles.put(transportFile.nexlaFile.getFullPath(), transportFile);
								return;
							} else {
								logger.info("File changed, start reading from beginning, file={}", fullPath);
								TransportFile transportFile = new TransportFile(fileFromStorage, 0, false);
								categorized.changedFiles.put(transportFile.nexlaFile.getFullPath(), transportFile);
								return;
							}
						} else {
							// not changed => continue reading
							categorized.filesToContinue.put(kafkaVersion.nexlaFile.getFullPath(), kafkaVersion);
							return;
						}
					}

				} else {

					// eof reached => check if file changed to read it again
					boolean wasChanged = isFileChanged(fileFromStorage, kafkaVersion.nexlaFile);

					// check if file was changed to determine either to continue reading from last processed line of from the beginning
					if (wasChanged) {
						if (config.appendMode) {
							logger.info("File={} was changed, start reading from last message={} (append mode)",
									kafkaVersion.nexlaFile.getFullPath(), kafkaVersion.lastMessageNumber);

							TransportFile transportFile = new TransportFile(fileFromStorage, kafkaVersion.lastMessageNumber, false);
							categorized.changedFiles.put(transportFile.nexlaFile.getFullPath(), transportFile);
							return;
						} else {
							logger.info("File={} was changed, start reading from beginning", fullPath);
							TransportFile transportFile = new TransportFile(fileFromStorage, 0, false);
							categorized.changedFiles.put(transportFile.nexlaFile.getFullPath(), transportFile);
							return;
						}
					} else {
						// not changed => no action
						return;
					}
				}

			} else {

				if (connectorVersion != null) {
					// connector version is not null, but no kafka version => 2 options:
					// 1) error occurred and no offsets stored
					// 2) very first batch is being read right now, hence offsets just have not committed yet

					// assume error
					categorized.filesInProgress.put(connectorVersion.nexlaFile.getFullPath(), connectorVersion);
					return;

				} else {
					//new file was created
					categorized.newFiles.put(fullPath, new TransportFile(fileFromStorage, 0, false));
					return;
				}
			}
		});
		return categorized;
	}

	boolean isFileChanged(NexlaFile a, NexlaFile b) {
		return !(Objects.equals(a.getLastModified(), b.getLastModified()) &&
				Objects.equals(a.getMd5(), b.getMd5()) &&
				Objects.equals(a.getSize(), b.getSize()));
	}

	Map<String, Optional<TransportFile>> getFileReadingState(Map<String, NexlaFile> filesFromStorage) {

		Map<String, Map<String, String>> fileToPartitionMap = EntryStream.of(filesFromStorage)
				.mapValues(file -> singletonMap(PARTITION_KEY, file.getFullPath()))
				.toMap();

		Map<Map<String, String>, Map<String, Object>> fileOffsetMap = offsetReader.offsets(fileToPartitionMap.values());

		return EntryStream.of(filesFromStorage)
				.<Optional<TransportFile>>mapValues(file -> {
					Map<String, String> filePartition = fileToPartitionMap.get(file.getFullPath());
					Map<String, Object> fileOffset = fileOffsetMap.get(filePartition);
					if (fileOffset == null) {
						return empty();
					} else {
						return of(toTransportFile(file, fileOffset));
					}
				})
				.toMap();
	}

	public void shutdown() {
		try {
			probeService.close();
		} catch (Exception e) {
			logger.error("", e);
		}
	}
}