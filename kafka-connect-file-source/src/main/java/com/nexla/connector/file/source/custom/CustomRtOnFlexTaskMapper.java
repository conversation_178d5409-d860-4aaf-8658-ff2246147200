package com.nexla.connector.file.source.custom;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.admin.client.CodeContainer;
import com.nexla.admin.client.DataSource;
import com.nexla.common.ConnectionType;
import com.nexla.common.CredentialType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.Metric;
import com.nexla.connector.config.file.CustomRtMode;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.file.source.CustomRtOnFlexTransportFileReader;
import com.nexla.connector.file.source.FileReadResult;
import com.nexla.connector.file.source.FileSourceContext;
import com.nexla.connector.file.source.FileSourceOffsetWriter;
import com.nexla.connector.file.source.NexlaMessageFile;
import com.nexla.connector.file.source.ReadingContext;
import com.nexla.connector.file.source.SchemaContext;
import com.nexla.connector.file.source.custom.model.ExtraData;
import com.nexla.connector.file.source.custom.model.Payload;
import com.nexla.connector.file.source.custom.model.State;
import com.nexla.connector.file.source.custom.model.Task;
import org.apache.kafka.connect.data.Schema;
import org.apache.kafka.connect.source.SourceRecord;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.nexla.common.NexlaMetaData.createSourceMetadata;
import static java.util.stream.Collectors.toList;

public class CustomRtOnFlexTaskMapper implements FileReadResult<SourceRecord> {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomRtOnFlexTaskMapper.class);

    private static final String DRIVER_FUNCTION = "process";
    private static final String VALUE_SEPARATOR = ",";

    private final FileSourceOffsetWriter offsetWriter;
    private final FileSourceContext fileSourceContext;
    private final NexlaLogger logger;
    private final Task.CustomCode customCode;
    private final Task.Metadata metadata;
    private final Map<String, Object> sourceConfig;
    private Map<String, Object> jobConfig = Map.of();

    private final TaskRegistry taskRegistry;

    private List<NexlaMessageFile> records;

    private final Task.Destination destination;
    private final String taskTopic;
    private final Task.Source sourcePrototype;

    public CustomRtOnFlexTaskMapper(TaskRegistry registry, FileSourceOffsetWriter offsetWriter, FileSourceContext fileSourceContext, NexlaLogger logger, String taskTopic, DataSource dataSource, CodeContainer codeContainer, CustomRtMode mode) {
        this.offsetWriter = offsetWriter;
        this.fileSourceContext = fileSourceContext;
        this.logger = logger;
        this.taskTopic = taskTopic;
        this.taskRegistry = registry;

        FileSourceConnectorConfig fscc = fileSourceContext.config;

        this.sourceConfig = dataSource.getSourceConfig();

        this.metadata = Task.Metadata.builder()
                .orgId(dataSource.getOrgId())
                .orgId(dataSource.getFlowNodeId())
                .runId(fileSourceContext.runId)
                .sourceId(dataSource.getId())
                .build();

        Task.CustomCode.CustomCodeBuilder codeBuilder = Task.CustomCode.builder()
                .base64Code(codeContainer.getCode())
                .driverFunction(DRIVER_FUNCTION);

        codeContainer.getCodeConfig()
                .flatMap(CodeContainer.CodeConfig::getPackages)
                .ifPresent(x -> {
                    List<String> packageList = x.isEmpty() ? Collections.emptyList() : Arrays.asList(x.split(VALUE_SEPARATOR));
                    codeBuilder.packages(packageList);
                });

        try {
            ExtraData extraData = codeContainer.getCodeConfig()
                    .flatMap(CodeContainer.CodeConfig::getExtraData)
                    .filter(x -> !x.isBlank())
                    .map(x -> JsonUtils.stringToType(x, ExtraData.class))
                    .orElse(null);

            if (extraData != null) {
                if (extraData.getCustomCode() != null) {
                    ExtraData.CustomCode cc = extraData.getCustomCode();

                    codeBuilder.args(cc.getArgs());
                    codeBuilder.kwargs(cc.getKwargs());

                    if (cc.getPrivatePackages() != null) {
                        ExtraData.PrivatePackages pps = cc.getPrivatePackages();

                        codeBuilder.pvtPackages(
                                Task.PrivatePackages
                                        .builder()
                                        .credentialId(pps.getCredentialId())
                                        .urls(pps.getUrls())
                                        .build()
                        );
                    }

                    this.jobConfig = extraData.getJobConfig();
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to parse extra data", e);
        }

        this.customCode = codeBuilder
                .build();

        Optional<ConnectionType> optConnectionType = fscc.customRtAuthConfig.credentialsType.flatMap(CredentialType::asConnectionType);
        if (optConnectionType.isEmpty()) {
            throw new IllegalArgumentException("Invalid customRT credentials type=" + fscc.customRtAuthConfig.credentialsType);
        }

        ConnectionType connectionType = optConnectionType.get();
        if (fscc.schemaDetectionOnce) {
            this.destination = Task.Destination.builder()
                    .type("webhook")
                    .build();

        } else if (connectionType.isFile()) {
            this.destination = Task.Destination.builder()
                    .type(connectionType.name.toLowerCase())
                    .credentials(fscc.customRtAuthConfig.originalsStrings())
                    .outputPath(fscc.customRtDestination + "/" + fscc.sourceId + "/" + DateTime.now().toString("yyyy-MM-dd") + "/post-processed/" + fileSourceContext.runId) // todo
                    .build();
        } else {
            throw new IllegalArgumentException("Invalid connection type=" + connectionType);
        }

        String sourceConnectionType = fileSourceContext.config.customRtAuthConfig.credentialsType
                .flatMap(CredentialType::asConnectionType)
                .map(ConnectionType::name)
                .map(String::toLowerCase)
                .orElse(null);

        this.sourcePrototype = Task.Source.builder()
                .credentials(fileSourceContext.config.customRtAuthConfig.originalsStrings())
                .type(sourceConnectionType)
                .fileType(toSourceFileType(mode))
                .build();

        newRecordsBuffer();
    }

    private static Task.Source.FileType toSourceFileType(CustomRtMode mode) {
        if (mode == CustomRtMode.OFF) {
            LOGGER.warn("CustomRT mode is OFF for CustomRt Task Mapper! Falling back to PARSED file type");
            return Task.Source.FileType.PARSED;
        }

        return mode == CustomRtMode.REPLICATION
                ? Task.Source.FileType.REPLICATED
                : Task.Source.FileType.PARSED;
    }

    @Override
    public List<SourceRecord> removeResult() {
        List<SourceRecord> result = records.stream()
                .map(r -> {
                    Task task = makeTask(r);

                    taskRegistry.register(task);

                    return new SourceRecord(
                            Map.of(), Map.of(), // XXX check these
                            taskTopic,
                            Schema.STRING_SCHEMA,
                            JsonUtils.toJsonString(
                                    Payload.builder()
                                            .task(task)
                                            .state(State.INGESTED)
                                            .build()
                            )
                    );
                })
                .collect(toList());

        newRecordsBuffer();

        return result;
    }

    private Task makeTask(NexlaMessageFile r) {
        String filePath = (String) r.message.getRawMessage()
                .get(CustomRtOnFlexTransportFileReader.PROP_FILE_PATH);
        String originalFilePath = (String) r.message.getRawMessage()
                .get(CustomRtOnFlexTransportFileReader.PROP_ORIGINAL_FILE_PATH);

        Task task = Task.builder()
                .customCode(customCode)
                .sourceConfig(sourceConfig)
                .source(
                        this.sourcePrototype.toBuilder()
                                .inputFile(filePath)
                                .originalFile(originalFilePath)
                                .build()
                )
                .metadata(metadata.withFileId(r.file.getId()))
                .jobConfig(jobConfig)
                .destination(destination)
                .build();

        return task;
    }

    @Override
    public boolean acceptsMoreData() {
        return true;
    }

    @Override
    public void acceptMessage(SchemaContext schema, ReadingContext ctx, NexlaMessage message, boolean eof, long messageNumber, Map<Integer, Metric> datasetMetrics) {
        FileSourceConnectorConfig sourceCfg = fileSourceContext.config;

        NexlaMetaData nexlaMetaData = createSourceMetadata(
                sourceCfg.sourceType,
                sourceCfg.sourceId,
                sourceCfg.version,
                ctx.getNexlaFile(),
                eof,
                messageNumber,
                "", // XXX wants dataset topic name
                fileSourceContext.runId,
                message.getNexlaMetaData(),
                null // XXX wants datasetId
        );

        message.setNexlaMetaData(nexlaMetaData);

        records.add(new NexlaMessageFile(message, ctx.getNexlaFile(), null));
    }

    @Override
    public void onEof(ReadingContext ctx, boolean skip, boolean error) {
        // special case where there is no record that could pass offset with eof=true to kafka-connect
        // thus, we need to update offset directly
        if (records.isEmpty()) {
            logger.info("File={}: storing eof=true offset directly", ctx.getNexlaFile().getFullPath());
            offsetWriter.writeEofOffset(ctx, skip, error);
        } else {
            // last message of file might be unparseable, it is required to set eof=true to the last available record
            NexlaMessageFile lastRecord = records.get(records.size() - 1);
            lastRecord.message.getNexlaMetaData().setEof(true);
        }
    }

    public void onSuccess(ReadingContext ctx, boolean fileEofReached, Map<Integer, Metric> datasetMetrics) {
        logger.info("File={}: successfully read file", ctx.getNexlaFile().getFullPath());
    }

    public void onException(Exception e, ReadingContext ctx) {
        logger.error("File={}: failed to read file", ctx.getNexlaFile().getFullPath(), e);
    }

    private void newRecordsBuffer() {
        this.records = new ArrayList<>(1); // not always 1
    }

}
