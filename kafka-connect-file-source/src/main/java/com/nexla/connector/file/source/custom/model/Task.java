package com.nexla.connector.file.source.custom.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Task {
    @JsonProperty("custom_code")
    private CustomCode customCode;
    @JsonProperty("destination")
    private Destination destination;
    @JsonProperty("job_config")
    private Map<String, Object> jobConfig;
    @JsonProperty("source_config")
    private Map<String, Object> sourceConfig;
    @JsonProperty("metadata")
    private Metadata metadata;
    @JsonProperty("source")
    private Source source;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomCode {
        @JsonProperty("args")
        @Builder.Default
        private List<String> args = new ArrayList<>();
        @JsonProperty("base64_code")
        private String base64Code;
        @JsonProperty("driver_function")
        private String driverFunction;
        @JsonProperty("kwargs")
        @Builder.Default
        private Map<String, String> kwargs = new HashMap<>();
        @JsonProperty("packages")
        private List<String> packages;
        @JsonProperty("pvt_packages")
        private PrivatePackages pvtPackages;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrivatePackages {
        @JsonProperty("credential_id")
        private List<String> credentialId;
        @JsonProperty("urls")
        private List<String> urls;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Destination {
        @JsonProperty("type")
        private String type;
        @JsonProperty("credentials")
        private Map<String, String> credentials;
        @JsonProperty("output_path")
        private String outputPath;
    }

    @Data
    @Builder
    @With
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Metadata {
        @JsonProperty("org_id")
        private int orgId;
        @JsonProperty("run_id")
        private long runId;
        @JsonProperty("source_id")
        private int sourceId;
        @JsonProperty("flow_id")
        private int flowId;
        @JsonProperty("file_id")
        private Long fileId;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Source {
        @JsonProperty("credentials")
        private Map<String, String> credentials;
        @JsonProperty("input_file")
        private String inputFile;
        @JsonProperty("original_file")
        private String originalFile;
        @JsonProperty("type")
        private String type;

        @JsonProperty("file_type")
        private FileType fileType;

        public enum FileType {
            @JsonProperty("replicated")
            REPLICATED,
            @JsonProperty("parsed")
            PARSED
        }
    }
}
