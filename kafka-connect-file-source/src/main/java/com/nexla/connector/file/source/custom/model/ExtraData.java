package com.nexla.connector.file.source.custom.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ExtraData {
    @JsonProperty("custom_code")
    private CustomCode customCode;
    @JsonProperty("job_config")
    private Map<String, Object> jobConfig;


    @Data
    public static class CustomCode {
        @JsonProperty("args")
        private List<String> args;
        @JsonProperty("kwargs")
        private Map<String, String> kwargs;
        @JsonProperty("pvt_packages")
        private PrivatePackages privatePackages;
    }

    @Data
    public static class PrivatePackages {
        @JsonProperty("credential_id")
        private List<String> credentialId;
        @JsonProperty("urls")
        private List<String> urls;
    }
}
