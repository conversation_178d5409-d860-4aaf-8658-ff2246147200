package com.nexla.connector.file.source;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.Org;
import com.nexla.admin.client.oauth2.RefreshingTokenProvider;
import com.nexla.common.ConnectionType;
import com.nexla.common.Resource;
import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.StreamUtils;
import com.nexla.connect.common.BaseSourceTask;
import com.nexla.connect.common.CollectRecordsResult;
import com.nexla.connect.common.ConnectWorkarounds;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.file.CustomRtMode;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.file.source.custom.config.Utils;
import com.nexla.file.service.FileConnectorService;
import com.nexla.listing.client.FileVaultClient;
import com.nexla.listing.client.ListingClient;
import com.nexla.probe.ftp.FileConnectorServiceBuilder;
import lombok.SneakyThrows;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.source.SourceRecord;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.connector.config.FileEncryptConfig.ENCRYPT_PRIVATE_KEY;
import static com.nexla.connector.config.FileEncryptConfig.ENCRYPT_PRIVATE_PASSWORD;
import static com.nexla.connector.config.vault.CredentialsStore.CONNECTOR_POLICY;
import static com.nexla.connector.config.vault.CredentialsStore.SECRET;
import static com.nexla.connector.file.source.custom.config.Utils.*;
import static com.nexla.connector.properties.FileConfigAccessor.FILE_LIST;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

public class FileSourceTask extends BaseSourceTask<FileSourceConnectorConfig> {

	private final Supplier<ConnectWorkarounds> workarounds =
			Suppliers.memoize(() -> new ConnectWorkarounds(context.offsetStorageReader()));

	private static ScheduledExecutorService SCHEDULED_POOL = Executors.newScheduledThreadPool(1);

	private FileReadResult<SourceRecord> consumer;
	private FileSourceOffsetWriter offsetWriter;
	private KafkaFileSourceMessageGrouper messageGrouper;
	private ITransportFileReader fileReader;

	@VisibleForTesting
	protected FileConnectorService probeService;

	public FileSourceTask(SchemaDetectionUtils schemaDetection) {
		this.schemaDetection = schemaDetection;
	}

	public FileSourceTask() {
	}

	@Override
	public ConfigDef configDef() {
		return FileSourceConnectorConfig.configDef();
	}

	@Override
	public void doStart(Map<String, String> props) throws ConnectException {
		this.sourceTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(config.sourceId, config.sourceType.name(), SOURCE, true));
		RefreshingTokenProvider tokenProvider = new RefreshingTokenProvider(adminApiClient, config.decryptKey);
		FileVaultClient fileVault = new FileVaultClient(config.fileVaultServer, config.nexlaUsername, config.nexlaPassword, new RestTemplateBuilder().withSSL(config.sslContext).build());

		this.probeService = getProbeService(
			this.adminApiClient,
			this.listingClient,
			config.decryptKey,
			config.sourceType,
			fileVault);

		this.probeService.initLogger(SOURCE, config.sourceId, Optional.empty());

		final FileSourceContext fileSourceContext = new FileSourceContext(
				config,
				getDataSetForSource(dataSource.getDatasets()),
				runId,
				Optional.ofNullable(dataSource.getOrg())
						.stream()
						.map(Org::getId)
						.findAny()
						.orElse(0),
				dataSource.getOwnerId());

		FileSourceNotificationSender kafkaMessageSender = new FileSourceNotificationSender(nexlaMessageProducer, FlowType.STREAMING, fileSourceContext, logger);
		FileSourceOffsetWriter offsetWriter = createOffsetWriter(kafkaMessageSender);
		KafkaFileSourceMessageGrouper messageGrouper = new KafkaFileSourceMessageGrouper(fileSourceContext, logger, runId);

		RestTemplate restTemplate = new RestTemplateBuilder().withSSL(config.sslContext).build();

		Optional<ListingClient> listingClient = config.listingEnabled
			? of(createListingClient(config, restTemplate))
			: empty();

		TransportFileReader fileReader = null;
		if (config.fileBatchingEnabled) {
			fileReader = new BatchingTransportFileReaderImpl(
					kafkaMessageSender,
					probeService,
					schemaDetection,
					offsetWriter,
					messageGrouper,
					logger,
					of(context.offsetStorageReader()),
					listingClient,
					empty(),
					fileSourceContext,
					Optional.of(this::heartbeat),
					config.listingEnabled,
					config.fileBatchingBatchSize
			);
		} else if (isCustomRuntimeMode() || Utils.hasRayAsPostProcessor(dataSource.getSourceConfig())) {
			fileReader = new CustomRtTransportFileReader(
					kafkaMessageSender,
					probeService,
					schemaDetection,
					offsetWriter,
					messageGrouper,
					logger,
					of(context.offsetStorageReader()),
					listingClient,
					empty(),
					fileSourceContext,
					Optional.of(this::heartbeat),
					config.listingEnabled,
					deduceCustomRtMode(dataSource.getSourceConfig())
							.orElse(config.customRtMode)
			);
		} else {
			fileReader = new TransportFileReader(
					kafkaMessageSender,
					probeService,
					schemaDetection,
					offsetWriter,
					messageGrouper,
					logger,
					of(context.offsetStorageReader()),
					listingClient,
					empty(),
					fileSourceContext,
					Optional.of(this::heartbeat),
					config.listingEnabled
			);
		}

		fileReader.setCustomParserEnabled(true);

		if (!config.listingEnabled) {
			String fileList = props.get(FILE_LIST);
			List<TransportFile> transportFiles = StreamUtils.jsonUtil().stringToType(fileList, new TypeReference<List<TransportFile>>() {
			});

			fileReader.addFiles(transportFiles);
		}
		
		this.fileReader = fileReader;
		this.consumer = new KafkaFileMessageResultReader(
				offsetWriter,
				messageGrouper,
				fileSourceContext,
				kafkaMessageSender,
				logger,
				probeService);
	}

	@NotNull
	protected ListingClient createListingClient(BaseConnectorConfig config, RestTemplate restTemplate) {
		return new ListingClient(config.listingAppServer, config.nexlaUsername, config.nexlaPassword, restTemplate);
	}

	private boolean isCustomRuntimeMode() {
		return config.customRtMode != CustomRtMode.OFF;
	}

	private Optional<Integer> getDataSetForSource(List<DataSet> datasets) {
		if (datasets.isEmpty() || datasets.size() > 1) {
			return empty();
		}
		return ofNullable(datasets.get(0).getId());
	}

	public Map<String, String> customConnectorProperties(int sourceId) {
		Map<String, String> result = Maps.newHashMap();

		try {
			String connectorPrefix = SECRET + "_" + CONNECTOR_POLICY + "_" + "source_" + sourceId;
			Optional<Map<String, String>> vaultProps = this.nexlaCredentialsStore
					.getValuesMap(connectorPrefix);

			vaultProps
					.map(x -> x.get(ENCRYPT_PRIVATE_KEY))
					.ifPresent(x -> result.put(ENCRYPT_PRIVATE_KEY, x));

			vaultProps
					.map(x -> x.get(ENCRYPT_PRIVATE_PASSWORD))
					.ifPresent(x -> result.put(ENCRYPT_PRIVATE_PASSWORD, x));
		} catch (Exception e) {
			logger.error("Failed to load customConnectorProperties from CredentialsStore");
		}

		return result;
	}

	public static FileConnectorService getProbeService(
		AdminApiClient adminApiClient,
		ListingClient listingClient,
		String decryptKey,
		ConnectionType sourceType,
		FileVaultClient fileVault
	) {
		FileConnectorServiceBuilder fileConnectorServiceBuilder = new FileConnectorServiceBuilder(adminApiClient, listingClient, decryptKey);
		fileConnectorServiceBuilder.setFileVault(fileVault);
		return fileConnectorServiceBuilder.createFileConnectorService(sourceType);
	}

	protected FileSourceOffsetWriter createOffsetWriter(FileSourceNotificationSender kafkaMessageSender) {
		return new KafkaFileSourceOffsetWriter(kafkaMessageSender, workarounds, logger, runId, config.listingEnabled);
	}

	@Override
	protected FileSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new FileSourceConnectorConfig(props);
	}

	@SneakyThrows
	@Override
	public CollectRecordsResult collectRecords() {
		long startedQuery = System.currentTimeMillis();
		ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(() ->
						ctrlClient
								.filter(x -> System.currentTimeMillis() - startedQuery < config.queryHeartbeatPeriodMs)
								.ifPresent(jpc -> {
									logger.info("Heartbeat. trace=false");
									jpc.heartbeatConnector(new Resource(config.sourceId, SOURCE), runId, false);
								}),
				1, 1, TimeUnit.MINUTES);
		try {
			ReadBatchResult<SourceRecord> sourceRecordReadBatchResult = fileReader.readNextBatch(consumer, adminApiClient);
			return new CollectRecordsResult(sourceRecordReadBatchResult.messages) {
				@Override
				// added for cases of files with no records
				// we dont want to back off if there are files listed
				public boolean hasMoreData() {
					return !sourceRecordReadBatchResult.listingIsEmpty;
				}
			};
		} finally {
			heartbeat.cancel(false);
		}
	}

	public ITransportFileReader getFileReader() {
		return fileReader;
	}

	@Override
	public void stop() throws ConnectException {
		if (fileReader != null) {
			fileReader.stop();
		}
		super.stop();
	}

}
