package com.nexla.connector.file.source.custom.stage;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ListingResourceType;
import com.nexla.common.NexlaFile;
import com.nexla.common.Resource;
import com.nexla.connector.file.source.KafkaFileMessageResultReader;
import com.nexla.connector.file.source.ReadBatchResult;
import com.nexla.connector.file.source.TransportFileReader;
import com.nexla.connector.file.source.custom.ProcessingStage;
import com.nexla.connector.file.source.custom.TaskRegistry;
import com.nexla.connector.file.source.custom.bus.TaskBus;
import com.nexla.connector.file.source.custom.model.Payload;
import com.nexla.connector.file.source.custom.model.Task;
import com.nexla.control.ListingFileStatus;
import com.nexla.listing.client.ListingClient;
import com.nexla.listing.client.TmpFile;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.kafka.connect.source.SourceRecord;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class IngestionStage implements ProcessingStage {
    private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(IngestionStage.class);

    private final ListingClient listing;
    private final TransportFileReader fileReader;
    private final KafkaFileMessageResultReader consumer;
    private final TaskBus taskBus;
    private final TaskRegistry registry;

    private final List<PostProcessedFile> files;
    private final AdminApiClient adminApiClient;

    @Data
    @AllArgsConstructor
    private static class PostProcessedFile {
        private final Long id;
        private final String fullPath;
    }

    public IngestionStage(ListingClient listing, TransportFileReader fileReader, KafkaFileMessageResultReader consumer, TaskBus taskBus, TaskRegistry registry, AdminApiClient adminApiClient) {
        this.listing = listing;
        this.fileReader = fileReader;
        this.consumer = consumer;
        this.taskBus = taskBus;
        this.registry = registry;
        this.adminApiClient = adminApiClient;

        this.files = new ArrayList<>();
    }

    public ReadBatchResult<SourceRecord> poll() {
        // we can start parallel processing
        if (files.isEmpty()) {
            Optional<Payload> optPayload = this.taskBus.fetchOne();
            if (optPayload.isPresent()) {
                Payload payload = optPayload.get();

                Task.Metadata metadata = payload.getTask().getMetadata();
                if (registry.has(metadata.getFileId())) {
                    LOGGER.info("Removing task from registry: {}", metadata.getFileId());

                    registry.remove(metadata.getFileId());

                    listing.setFileStatus(metadata.getSourceId(), metadata.getFileId(), ListingFileStatus.DONE, Optional.empty(), Optional.empty());

                    LOGGER.info("Querying tmp files for meta: {}", metadata);
                    List<TmpFile> tmpFiles = listing.listTmpFiles(
                            Resource.source(metadata.getSourceId()),
                            metadata.getFileId()
                    );

                    LOGGER.info("Found tmp files: {}", tmpFiles.size());

                    files.addAll(
                            tmpFiles
                                    .stream()
                                    .map(x -> new PostProcessedFile(x.getId(), x.getFullPath()))
                                    .collect(Collectors.toList())
                    );
                } else {
                    LOGGER.info("Skipping task: unknown to this instance fileId = {}", payload.getTask().getMetadata().getFileId());
                }
            }
        }

        if (files.isEmpty()) {
            LOGGER.info("No files to process in this poll. Known tasks: {}", registry.size());
            return new ReadBatchResult<>(List.of(), null, false, registry.isEmpty());
        }

        if (fileReader.getFileObjects().isEmpty()) {
            NexlaFile file = toNexlaFile(files.remove(0));
            LOGGER.info("Adding file to file reader: {}", file.getFullPath());

            fileReader.addFiles(List.of(
                    TransportFileReader.toTransportFile(file, Map.of())
            ));
        }

        ReadBatchResult<SourceRecord> innerResult = fileReader.readNextBatch(consumer, adminApiClient);
        if (innerResult.messages == null) {
            return new ReadBatchResult<>(List.of(), null, false, registry.isEmpty());
        }

        return new ReadBatchResult<>(
                innerResult.messages,
                innerResult.schemaContext,
                innerResult.archiveListingUpdated,
                registry.isEmpty()
        );
    }

    @Override
    public void stop() {
        this.fileReader.stop();

        this.taskBus.close();
    }

    private static NexlaFile toNexlaFile(PostProcessedFile next) {
        return new NexlaFile(null, null, NexlaFile.FileSourceType.LISTING, next.getFullPath(), 0L, null, null, null, null, ListingResourceType.FILE);
    }
}
