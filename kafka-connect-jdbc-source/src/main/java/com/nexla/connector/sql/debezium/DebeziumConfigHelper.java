package com.nexla.connector.sql.debezium;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaNamingUtils;
import com.nexla.connector.config.jdbc.CDCUtils;
import com.nexla.connector.config.jdbc.HostPortDb;
import com.nexla.connector.config.jdbc.JdbcCDCTableConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.kafka.control.listener.conf.SslContextApplier;
import com.nexla.kafka.service.TopicMetaService;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.util.CdcMode;
import io.debezium.connector.mysql.MySqlConnector;
import io.debezium.connector.mysql.MySqlConnectorConfig;
import io.debezium.connector.oracle.OracleConnector;
import io.debezium.connector.oracle.OracleConnectorConfig;
import io.debezium.connector.postgresql.PostgresConnector;
import io.debezium.connector.postgresql.PostgresConnectorConfig;
import io.debezium.connector.sqlserver.SqlServerConnector;
import io.debezium.connector.sqlserver.SqlServerConnectorConfig;
import io.debezium.storage.kafka.history.KafkaSchemaHistory;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.TopicConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.ConnectionType.POSTGRES;
import static com.nexla.common.StreamUtils.map;
import static connect.jdbc.sink.dialect.DbDialect.getDbDialect;

@RequiredArgsConstructor
public class DebeziumConfigHelper {
    private static final Logger logger = LoggerFactory.getLogger(DebeziumConfigHelper.class);

    public final JdbcSourceConnectorConfig config;

    public void addTableSpecificProps(Properties props) {
        if(config.cdcMode.equals(CdcMode.MULTI_TABLE.getMode())) {
            JdbcCDCTableConfig tableConfig = config.cdcTableConfig;

            if (tableConfig == null) {
                return;
            }

            String include = getTablesString(tableConfig.includeTables, tableConfig.includePattern);
            String exclude = getTablesString(tableConfig.excludeTables, tableConfig.excludePattern);
            if (StringUtils.isNotBlank(include)) {
                props.setProperty("table.include.list", include);
            } else if (StringUtils.isNotBlank(exclude)) {
                props.setProperty("table.exclude.list", exclude);
            }
        } else {
            props.setProperty("table.include.list", getTablePrefix() + config.table.get());
        }
    }

    private String getTablesString(List<String> tables, List<String> pattern) {
        List<String> finalList = new ArrayList<>();
        finalList.addAll(tables.stream()
                .map(s -> getTablePrefix() + s)
                .collect(Collectors.toList()));

        finalList.addAll(pattern.stream()
                .map(s -> getTablePrefix() + s)
                .collect(Collectors.toList()));

        return String.join(",", finalList);
    }

    public void addDBSpecificProps(Properties props) {
        String schema = getSchema();

        if (config.authConfig.dbType.equals(ORACLE)) {
            props.setProperty(OracleConnectorConfig.DATABASE_NAME.name(), getConnectionConfig().dbName);
            props.setProperty(OracleConnectorConfig.SCHEMA_INCLUDE_LIST.name(), schema);
            props.setProperty(OracleConnectorConfig.SNAPSHOT_MODE.name(),
                config.cdcSnapshotEnabled
                    ? OracleConnectorConfig.SnapshotMode.INITIAL.getValue()
                    : OracleConnectorConfig.SnapshotMode.SCHEMA_ONLY.getValue());
            props.setProperty(OracleConnectorConfig.SNAPSHOT_LOCK_TIMEOUT_MS.name(), "600000");
            props.setProperty(OracleConnectorConfig.INTERVAL_HANDLING_MODE.name(), OracleConnectorConfig.IntervalHandlingMode.STRING.getValue());
        } else if (config.authConfig.dbType.equals(SQLSERVER)) {
            props.setProperty("database.names", getConnectionConfig().dbName);
            props.setProperty(SqlServerConnectorConfig.SCHEMA_INCLUDE_LIST.name(), schema);
            props.setProperty(SqlServerConnectorConfig.SNAPSHOT_MODE.name(),
                config.cdcSnapshotEnabled
                    ? SqlServerConnectorConfig.SnapshotMode.INITIAL.getValue()
                    : SqlServerConnectorConfig.SnapshotMode.SCHEMA_ONLY.getValue());
            props.setProperty(SqlServerConnectorConfig.SNAPSHOT_LOCK_TIMEOUT_MS.name(), "600000");
        } else if (config.authConfig.dbType.equals(MYSQL)) {
            if (config.authConfig.jdbcParameters.containsKey("serverTimezone")) {
                props.setProperty("database.serverTimezone", config.authConfig.jdbcParameters.get("serverTimezone"));
            }

            props.setProperty(MySqlConnectorConfig.SERVER_ID.name(), this.getDatabaseServerId());
            props.setProperty(MySqlConnectorConfig.INCLUDE_SQL_QUERY.name(), "true");
            props.setProperty(MySqlConnectorConfig.DATABASE_INCLUDE_LIST.name(), getConnectionConfig().dbName);
            props.setProperty(MySqlConnectorConfig.SNAPSHOT_MODE.name(),
                config.cdcSnapshotEnabled
                    ? MySqlConnectorConfig.SnapshotMode.INITIAL.getValue()
                    : MySqlConnectorConfig.SnapshotMode.SCHEMA_ONLY.getValue());
            props.setProperty(MySqlConnectorConfig.SNAPSHOT_LOCK_TIMEOUT_MS.name(), "600000");
        } else if (config.authConfig.dbType.equals(POSTGRES)) {
            props.setProperty(PostgresConnectorConfig.PLUGIN_NAME.name(), config.cdcPostgresLogicalDecoder);
            props.setProperty(PostgresConnectorConfig.SLOT_NAME.name(), "debezium_" + config.sourceId);
            props.setProperty(PostgresConnectorConfig.DATABASE_NAME.name(), getConnectionConfig().dbName);
            props.setProperty(PostgresConnectorConfig.SCHEMA_INCLUDE_LIST.name(), schema);
            props.setProperty(PostgresConnectorConfig.SNAPSHOT_MODE.name(),
                config.cdcSnapshotEnabled
                    ? PostgresConnectorConfig.SnapshotMode.INITIAL.getValue()
                    : PostgresConnectorConfig.SnapshotMode.NEVER.getValue());
            props.setProperty(PostgresConnectorConfig.SNAPSHOT_LOCK_TIMEOUT_MS.name(), String.valueOf(PostgresConnectorConfig.DEFAULT_SNAPSHOT_LOCK_TIMEOUT_MILLIS));
            props.setProperty(PostgresConnectorConfig.HSTORE_HANDLING_MODE.name(), PostgresConnectorConfig.HStoreHandlingMode.JSON.getValue());
            props.setProperty(PostgresConnectorConfig.INTERVAL_HANDLING_MODE.name(), PostgresConnectorConfig.IntervalHandlingMode.STRING.getValue());
        }
    }

    public void addDBHistoryProps(Properties props) {
        if (!config.authConfig.dbType.equals(ConnectionType.POSTGRES)) {
            String historyTopic = NexlaNamingUtils.dbHistoryTopicName(config.sourceId, getConnectionConfig().dbName);

            TopicMetaService topicMetaService = new TopicMetaService(config.bootstrapServers, config.sslContext);

            logger.info("KafkaDatabaseHistory config: \nbootstrap.servers={}, \npartitions={}, \nreplications={}, \ntopic={}",
                    config.bootstrapServers, config.datasetPartitions, config.datasetReplication, historyTopic);
            if (!topicMetaService.isExistingTopic(historyTopic)) {
                topicMetaService.createTopic(historyTopic, config.datasetPartitions, config.datasetReplication, map(TopicConfig.RETENTION_MS_CONFIG, "-1"));
            }

            props.setProperty("schema.history.internal", KafkaSchemaHistory.class.getName());
            props.setProperty("schema.history.internal.kafka.bootstrap.servers", config.bootstrapServers);
            props.setProperty("schema.history.internal.kafka.topic", historyTopic);

            logger.info("Applying Debezium producer/consumer SSL config: {}", JsonUtils.toJsonString(config.sslContext));
            props.putAll(new SslContextApplier(config.sslContext)
                    .applyDebezium(new HashMap<>())
            );
        }
    }

    public String getConnectorClass() {
        if (config.authConfig.dbType.equals(POSTGRES)) {
            return PostgresConnector.class.getName();
        } else if (config.authConfig.dbType.equals(MYSQL)) {
            return MySqlConnector.class.getName();
        } else if (config.authConfig.dbType.equals(SQLSERVER)) {
            return SqlServerConnector.class.getName();
        } else if (config.authConfig.dbType.equals(ORACLE)) {
            return OracleConnector.class.getName();
        }
        throw new IllegalArgumentException();
    }

    public String getTablePrefix() {
        ConnectionConfig connectionConfig = getConnectionConfig();
        if (config.authConfig.dbType.equals(MYSQL)) {
            return connectionConfig.dbName + ".";
        } else if (config.authConfig.dbType.equals(POSTGRES) || config.authConfig.dbType.equals(SQLSERVER) || config.authConfig.dbType.equals(ORACLE)) {
            String schema = getSchema();
            return schema == null ? "" : schema + ".";
        }
        throw new IllegalArgumentException();
    }

    private String getSchema() {
        DbDialect dbDialect = getDbDialect(config.authConfig.dbType);
        return config.authConfig.schemaName == null ? dbDialect.getDefaultSchemaName().orElse(null) : config.authConfig.schemaName;
    }

    @SneakyThrows
    public ConnectionConfig getConnectionConfig() {
        HostPortDb hpd = CDCUtils.getHostPortDb(config.authConfig);
        return new ConnectionConfig(hpd.getHost(), hpd.getPort(), hpd.getDbName());
    }

    /**
     * @return A numeric ID of this database client, which must be unique across all currently-running
     * database processes in the DB cluster. Should be 5-6 digits positive number.
     */
    private String getDatabaseServerId() {
        final int defaultDatabaseServerId = 184_054;

        return String.valueOf(new Random()
                .ints(10_001, 999_999)
                .findFirst()
                .orElse(defaultDatabaseServerId));
    }

    @RequiredArgsConstructor
    public static class ConnectionConfig {
        public final String host;
        public final String port;
        public final String dbName;
    }
}
