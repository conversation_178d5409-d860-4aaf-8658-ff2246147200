package com.nexla.connector.config.file

import com.nexla.common.NexlaConstants.{SINK_ID, SINK_TYPE, CLIENT_SSL_ENABLED, SSL_KAFKA_ENABLED}
import com.nexla.common.time.VarUtils
import com.nexla.connector.properties.FileConfigAccessor.FILE_NAME_PREFIX
import org.joda.time.DateTime
import org.junit.Assert.assertEquals
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util
import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class FileSinkConnectorConfigTest
  extends AnyFlatSpecLike
    with Matchers {

  it should "substitute now in fileNamePrefix" in {

    val params = Map(
      SINK_TYPE -> "dropbox",
      SINK_ID -> "-1",
      FILE_NAME_PREFIX -> "dataset-{now}",
      CLIENT_SSL_ENABLED -> "false",
      SSL_KAFKA_ENABLED -> "false"
    )

    val config = new FileSinkConnectorConfig(params.asJava)
    val varInfo = config.fileNamePrefix.get
    val now = config.dateFormat.print(DateTime.now)
    val fileNamePrefix  = VarUtils.replaceVars(varInfo, util.Map.of[String, String], false)
    assertEquals("dataset-" + now, fileNamePrefix)
  }

  it should "substitute now + 1 in fileNamePrefix" in {

    val params = Map(
      SINK_TYPE -> "dropbox",
      SINK_ID -> "-1",
      FILE_NAME_PREFIX -> "dataset-{now+1}",
      CLIENT_SSL_ENABLED -> "false",
      SSL_KAFKA_ENABLED -> "false"
    )

    val config = new FileSinkConnectorConfig(params.asJava)
    val varInfo = config.fileNamePrefix.get
    val nowPlusOne = config.dateFormat.print(DateTime.now.plusDays(1))
    val fileNamePrefix  = VarUtils.replaceVars(varInfo, util.Map.of[String, String], false)
    assertEquals("dataset-" + nowPlusOne, fileNamePrefix)
  }
}
