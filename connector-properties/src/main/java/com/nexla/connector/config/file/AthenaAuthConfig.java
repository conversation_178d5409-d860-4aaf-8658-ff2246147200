package com.nexla.connector.config.file;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.BaseAuthConfig;

import java.util.Map;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class AthenaAuthConfig extends BaseAuthConfig {

    public static final String S3_OUTPUT_LOCATION = "s3.output.location";
    public static final String ACCESS_KEY_ID = "access.key.id";
    public static final String SECRET_KEY = "secret.key";
    public static final String AWS_REGION = "aws.region";
    
    public final String s3OutputLocation;
    public final String accessKeyId;
    public final String secretKey;
    public final String awsRegion;
    
    
    public AthenaAuthConfig(Map<String, ?> parsedConfig, Integer credsId) {
        super(configDef(), parsedConfig, credsId);
        
        this.s3OutputLocation = getString(S3_OUTPUT_LOCATION);
        this.accessKeyId = getString(ACCESS_KEY_ID);
        this.secretKey = getString(SECRET_KEY);
        this.awsRegion = getString(AWS_REGION);
    }

    public static NexlaConfigDef configDef() {
        return AWSAuthConfig.authConfigDef()
                
                .withKey(nexlaKey(S3_OUTPUT_LOCATION, STRING, null)
                        .documentation("Path of the AWS S3 location where  to store query results")
                        .displayName("S3 Output Location"))
                
                .withKey(nexlaKey(ACCESS_KEY_ID, STRING, null)
                        .documentation("AWS Access Key ID to Access Athena Database")
                        .displayName("AWS Access Key ID to Access Athena Database"))
                
                .withKey(nexlaKey(SECRET_KEY, STRING, null)
                        .documentation("AWS Secret Key to Access Athena Database")
                        .displayName("AWS Secret Key to Access Athena Database"))

                .withKey(nexlaKey(AWS_REGION, STRING, null)
                        .documentation("AWS Region of Athena Database")
                        .displayName("AWS Region of Athena Database"));
    }
}
