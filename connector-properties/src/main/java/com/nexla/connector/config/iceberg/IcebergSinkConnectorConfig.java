package com.nexla.connector.config.iceberg;

import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RecourceAccessCallback;
import java.util.Map;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.kafka.common.config.ConfigDef;

@ToString
@EqualsAndHashCode(callSuper = true)
public class IcebergSinkConnectorConfig extends SinkConnectorConfig implements FileConnectorAuth {

    public static final String WAREHOUSE_DIR = "iceberg.warehouse.dir";
    public static final String TABLE_NAME = "iceberg.table.name";
    public static final String PARTITION_KEYS = "iceberg.partition-keys";
    public static final String ID_FIELDS = "iceberg.id-fields";
    public static final String COMMIT_INTERVAL_MS = "iceberg.cdc.commit.interval-ms";
    public static final String COMMIT_POLL_TIMEOUT_MS = "iceberg.cdc.commit.poll-timeout-ms";
    public static final String ALTER_TABLE_ACCEPT_ANY_SCHEMA = "iceberg.alter.table.accept.any.schema";

    public static final String INSERT_MODE = "iceberg.insert.mode";

    public static final String ALLOW_NO_PK_TABLES = "iceberg.allow-no-pk-tables";

    public final AWSAuthConfig authConfig;
    public final String warehouseDir;
    public final String tableName;
    public final String idFields;
    public final Long commitIntervalMs;
    public final Long commitPollTimeoutMs;
    public final Optional<String> partitionKeys;
    public final InsertMode insertMode;
    public final boolean allowNoPkTables;
    public final boolean alterTableAcceptAnySchema;

    public IcebergSinkConnectorConfig(Map<String, String> originals) {
        super(configDef(), originals);

        this.warehouseDir = (String) this.values().get(WAREHOUSE_DIR);
        this.tableName = (String) this.values().get(TABLE_NAME);
        this.partitionKeys = Optional.ofNullable((String) this.values().get(PARTITION_KEYS));
        this.idFields = (String) this.values().get(ID_FIELDS);
        this.commitIntervalMs = (Long) this.values().get(COMMIT_INTERVAL_MS);
        this.commitPollTimeoutMs = (Long) this.values().get(COMMIT_POLL_TIMEOUT_MS);
        this.insertMode = InsertMode.valueOf(((String) this.values().get(INSERT_MODE)).toUpperCase());
        this.allowNoPkTables = (Boolean) this.values().get(ALLOW_NO_PK_TABLES);
        this.alterTableAcceptAnySchema = (Boolean) this.values().get(ALTER_TABLE_ACCEPT_ANY_SCHEMA);

        Map<String, String> authMap = !unitTest ? getCreds(decryptKey, credsEnc, credsEncIv) : originals;
        this.authConfig = new AWSAuthConfig(authMap, credsId);
    }

    public static NexlaConfigDef configDef() {
        NexlaConfigDef configDef = new NexlaConfigDef(sinkConfigDef());

        configDef = configDef.withKey(
            nexlaKey(TABLE_NAME, ConfigDef.Type.STRING, null)
                .documentation("Name of the table where data will be appended. "
                    + "Nexla uses the path-based Hadoop catalog to write to tables in S3. "
                    + "e.g. A path value of s3://my-nexla-bucket with a table name product.sales will "
                    + "append to or create an iceberg table at s3://my-nexla-bucket/product/sales/")
                .group("iceberg-sink")
                .displayName("Table name")).withKey(
            nexlaKey(WAREHOUSE_DIR, ConfigDef.Type.STRING, null)
                .documentation("Location in remote storage where the warehouse is located")
                .group("iceberg-sink")
                .displayName("Warehouse location")).withKey(
            nexlaKey(PARTITION_KEYS, ConfigDef.Type.STRING, null)
                .documentation("Comma separated list of column names to use for partitioning "
                    + "when creating a new table. If the table already exists we will not modify the table spec and "
                    + "this option will be ignored.")
                .group("iceberg-sink")
                .displayName("Partition keys")).withKey(
            nexlaKey(ID_FIELDS, ConfigDef.Type.STRING, null)
                .documentation("Comma separated list of id fields to use for upserts and CDC operations")
                .group("iceberg-sink")
                .displayName("Identity fields")).withKey(
            nexlaKey(INSERT_MODE, ConfigDef.Type.STRING, "insert")
                .documentation("Whether records should always be appended or if they should be upserted based on id fields.")
                .group("iceberg-sink")
                .displayName("Mode")).withKey(
            nexlaKey(COMMIT_INTERVAL_MS, ConfigDef.Type.LONG, 300000L)
                .documentation("How often (in milliseconds) should the CDC commit coordinator attempt a table commit? "
                    + "A higher commit interval leads to more records per commit and longer latency to the destination, "
                    + "but fewer individual commits to manage in the table. Increasing the commit interval may require "
                    + "increasing the commit coordinator poll timeout.")
                .group("iceberg-sink")
                .displayName("CDC commit interval (milliseconds)")).withKey(
            nexlaKey(COMMIT_POLL_TIMEOUT_MS, ConfigDef.Type.LONG, 30000L)
                .documentation("Duration to wait (in milliseconds) for the CDC committer to process written data files."
                    + "A higher value might be required when the commit interval is increased, as more time is needed "
                    + "to process all of the data files.")
                .group("iceberg-sink")
                .displayName("CDC commit coordinator poll timeout (milliseconds)")).withKey(
            nexlaKey(ALLOW_NO_PK_TABLES, ConfigDef.Type.BOOLEAN, false)
                .documentation("Enables processing of tables without primary key definition for CDC flows.")
                .group("iceberg-sink")
                .displayName("Enable no Pk table processing for multi table CDC pipelines")).withKey(
            nexlaKey(ALTER_TABLE_ACCEPT_ANY_SCHEMA, ConfigDef.Type.BOOLEAN, true)
                .documentation("Enables automatic alter table to accept any schema. "
                    + "It will set ('write.spark.accept-any-schema'='true') in Iceberg table properties.")
                .group("iceberg-sink")
                .displayName("Enables automatic alter table to accept any schema")
        );

        return configDef;
    }

    @Override
    public BaseAuthConfig getAuthConfig() {
        return authConfig;
    }

    @Override
    public String getRegion() {
        return authConfig.region;
    }

    @Override
    public String getPath() {
        return warehouseDir;
    }

    @Override
    public ConnectionType getConnectionType() {
        return ConnectionType.S3_ICEBERG;
    }

    @Override
    public Optional<RecourceAccessCallback> recourceAccessCallback() {
        return Optional.empty();
    }

    @Override
    public BaseConnectorConfig getConnectorConfig() {
        return this;
    }

    public static enum InsertMode {
        INSERT,
        UPSERT
    }
}
