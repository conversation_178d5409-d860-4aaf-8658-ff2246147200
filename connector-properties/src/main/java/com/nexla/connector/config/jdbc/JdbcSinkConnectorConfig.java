package com.nexla.connector.config.jdbc;

import com.nexla.common.storage.WarehouseTempStorageType;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.AzureAuthConfig;
import com.nexla.connector.config.jdbc.column.ColumnNormalizer;
import com.nexla.connector.config.ssh.tunnel.ConfigWithAuth;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.ConfigUtils.optInt;
import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.NexlaConstants.AZURE_CREDS_ENC;
import static com.nexla.common.NexlaConstants.AZURE_CREDS_ENCIV;
import static com.nexla.common.NexlaConstants.S3_CREDS_ENC;
import static com.nexla.common.NexlaConstants.S3_CREDS_ENCIV;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.common.StreamUtils.toLinkedHashMap;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.databricks.DatabricksCloudType.AZURE;
import static com.nexla.connector.config.file.AWSAuthConfig.IAM_ROLE;
import static com.nexla.connector.config.file.AzureAuthConfig.KEY_CONNECTION_STRING;
import static com.nexla.connector.config.file.AzureAuthConfig.STORAGE_ACCOUNT_NAME;
import static com.nexla.connector.config.file.AzureAuthConfig.STORAGE_ACCOUNT_KEY;
import static com.nexla.connector.config.file.S3Constants.ACCESS_KEY_ID;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig.FILE_FORMAT;
import static com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig.TEMP_STORAGE_TYPE;
import static com.nexla.connector.properties.SqlConfigAccessor.*;
import static java.util.Collections.emptyList;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.trimToNull;
import static org.apache.commons.lang3.StringUtils.wrap;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

public class JdbcSinkConnectorConfig extends SinkConnectorConfig implements ConfigWithAuth {

	public static final String JDBC_SINK_GROUP = "";
	public static final String TEMP_S3_UPLOAD_BUCKET = "temp.s3.upload.bucket";
	public static final String TEMP_S3_UPLOAD_PREFIX = "temp.s3.upload.prefix";
	public static final String STOP_ON_ERROR = "stop.on.error";
	public static final String TEMP_S3_DELETE = "temp.s3.delete";
	public static final String COPY_ALLOWED = "copy.allowed";

	public static final String TEMP_AZURE_BLOB_UPLOAD_BUCKET = "temp.azure.blob.upload.bucket";
	public static final String TEMP_AZURE_BLOB_UPLOAD_PREFIX = "temp.azure.blob.upload.prefix";
	public static final String TEMP_AZURE_BLOB_DELETE = "temp.azure.blob.delete";

	public static final String UPSERT_NULLS = "upsert.nulls";
	public static final String EMPTY_TO_NULL = "empty.to.null";
	public static final String NULL_AS = "null.as";

	public static final String REDSHIFT_TIME_FORMAT = "redshift.time.format";
	public static final String REDSHIFT_SERVERLESS = "redshift.serverless";
	public static final String REDSHIFT_DATE_FORMAT = "redshift.date.format";

	public static final String PARALLELISM = "parallelism";
	public static final String BATCH_SIZE = "batch.size";
	public static final String DWH_BATCH_SIZE = "dwh.batch.size";
	public static final String TRUNCATE_BEFORE_LOAD = "truncate.before.load";

	public static final String FIREBOLT_TABLE_TYPE = "firebolt.table.type";

	public static final String ORACLE_DATE_FORMAT = "oracle.date.format";
	public static final String ORACLE_TIMESTAMP_FORMAT = "oracle.timestamp.format";

	public static final String DIRECT_UPLOAD = "direct.upload";

	public static final String AUTO = "AUTO";
	public static final String INTERMEDIATE_FILE_FORMAT = "intermediate.file.format";

	public static final String INTERMEDIATE_FILE_QUOTE_CHAR = "intermediate.file.quote.char";

	public static final String INTERMEDIATE_FILE_DELIMITER = "intermediate.file.delimiter";
	public static final String REJECT_LIMIT = "reject.limit";

	public static final String ORACLE_AUTONOMOUS_OBSERVABLE_EXCEPTIONS = "oracle.autonomous.observable.exceptions";
	public static final String PARTITIONING_COLUMN = "partitioning.column";

	public static final String POST_FLUSH_SQL_SCRIPT = "post.flush.sql.script";

	public static final String CLUSTERING_COLUMNS = "clustering.columns";

	public static final String REDSHIFT_DIST_KEY = "redshift.distkey";

	public static final String REDSHIFT_SORT_KEYS = "redshift.sortkeys";

	public static final String SKIP_HEADER = "skip.header";

	public static final String SKIP_COPY_ERROR_HANDLING = "skip.copy.error.handling";

	public static final String NLS_DATE_FORMAT = "nls.date.format";

	public static final String NLS_TIMESTAMP_FORMAT = "nls.timestamp.format";

	public static final String WAREHOUSE_DATE_FORMAT = "warehouse.date.format";

	public static final String WAREHOUSE_TIMESTAMP_FORMAT = "warehouse.timestamp.format";

	public static final String FALLBACK_SINGLE_RECORD_MODE = "fallback.single.record.mode";

	public static final String DATABASE = "database";

	public final List<String> primaryKey;
	public final InsertMode insertMode;
	public final Optional<Integer> flushIntervalMin;
	public final String table;
	public final Optional<String> database;
	public final JdbcAuthConfig authConfig;
	public final boolean copyAllowed;
	public final String redshiftTimeFormat;
	public final String redshiftDateFormat;
	public final boolean redshiftServerless;
	public AWSAuthConfig s3AuthConfig;
	public AzureAuthConfig azureAuthConfig;
	public final boolean tempS3Delete;
	public final String tempS3UploadBucket;
	public final String tempS3UploadPrefix;
	public final boolean tempAzureBlobDelete;
	public final String tempAzureBlobUploadBucket;
	public final String tempAzureBlobUploadPrefix;
	public final String fireboltTableType;
	public final boolean stopOnError;
	public final boolean upsertNulls;
	public final boolean emptyToNull;
	public final String nullAs;
	public final int parallelism;
	public final int batchSize;
	public final int dwhBatchSize;
	public final Boolean truncOnLoad;
	public final Optional<WarehouseCopyFileFormat> fileFormat;
	public final String oracleDateFormat;
	public final String oracleTimestampFormat;
	public final Optional<String> partitioningColumn;
	public final List<String> clusteringingColumns;
	public final String distKey;
	public final List<String> sortKeys;
	public boolean directUpload;
	public final String intermediateFileFormat;
	public final int rejectLimit;
	public final List<String> oracleAutonomousObservableExceptions;

	public final String intermediateFileDelimiter;

	public final String intermediateFileQuoteChar;

	public boolean skipHeader;

	// Contains SQL script to be executed after flush operation for stateful sinks ONLY
	public final Optional<String> postFlushSqlScript;

	public WarehouseTempStorageType warehouseTempStorageType;

	public final boolean skipCopyErrorHandling;

	public final String nlsDateFormat;

	public final String nlsTimestampFormat;

	public final boolean fallbackSingleRecordMode;

	public final String warehouseDateFormat;

	public final String warehouseTimestampFormat;

	public JdbcSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		this.table = getString(TABLE);
		this.database = ofNullable(trimToNull(getString(DATABASE)));

		this.insertMode = InsertMode.valueOf(getString(INSERT_MODE).toUpperCase());
		flushIntervalMin = optInt(getInt(FLUSH_INTERVAL_MIN));

		Map<String, String> parsedConfig = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		this.authConfig = new JdbcAuthConfig(parsedConfig, credsId);

		this.warehouseTempStorageType = WarehouseTempStorageType.valueOf(getString(TEMP_STORAGE_TYPE).toUpperCase());
		if (isWareHouseWithS3Support()) {

			if (originals.containsKey(AZURE_CREDS_ENC)) {
				this.azureAuthConfig = new AzureAuthConfig(unitTest ? originals :
					getCreds(decryptKey, getString(AZURE_CREDS_ENC), getString(AZURE_CREDS_ENCIV)),
					credsId);
			}

			if (originals.containsKey(S3_CREDS_ENC) ||
				originals.containsKey(ACCESS_KEY_ID) ||
				originals.containsKey(IAM_ROLE)) {
				this.s3AuthConfig = new AWSAuthConfig(unitTest ? originals :
					getCreds(decryptKey, getString(S3_CREDS_ENC), getString(S3_CREDS_ENCIV)),
					credsId);
			}
		}

		if (AZURE_SYNAPSE.equals(authConfig.dbType)  ||
			(DATABRICKS.equals(authConfig.dbType) && AZURE.equals(authConfig.databricksCloudType))) {
			this.azureAuthConfig = new AzureAuthConfig(unitTest ? originals : getCreds(decryptKey, getString(AZURE_CREDS_ENC), getString(AZURE_CREDS_ENCIV)), credsId);
		}

		ColumnNormalizer columnNormalizer = ColumnNormalizer.getInstance(authConfig.dbType);
		this.primaryKey = StreamEx.of(getList(PRIMARY_KEY)).map(columnNormalizer::toStorage).toList();

		normalizeMappingConfig(columnNormalizer);

		this.tempS3UploadBucket = getString(TEMP_S3_UPLOAD_BUCKET);
		this.tempS3UploadPrefix = getString(TEMP_S3_UPLOAD_PREFIX);
		this.tempS3Delete = getBoolean(TEMP_S3_DELETE);

		this.tempAzureBlobUploadBucket = getString(TEMP_AZURE_BLOB_UPLOAD_BUCKET);
		this.tempAzureBlobUploadPrefix = getString(TEMP_AZURE_BLOB_UPLOAD_PREFIX);
		this.tempAzureBlobDelete = getBoolean(TEMP_AZURE_BLOB_DELETE);

		this.fireboltTableType = getString(FIREBOLT_TABLE_TYPE);

		this.copyAllowed = getBoolean(COPY_ALLOWED);


		if ((UPSERT.equals(insertMode)) && primaryKey.isEmpty() && (!cdcEnabled && !isEltFlowType())) {
			throw new ConfigException("Primary key is required for upsert mode");
		}

		this.stopOnError = getBoolean(STOP_ON_ERROR);
		this.upsertNulls = getBoolean(UPSERT_NULLS);
		this.emptyToNull = getBoolean(EMPTY_TO_NULL);
		this.nullAs = getString(NULL_AS);

		this.redshiftTimeFormat = wrap(getString(REDSHIFT_TIME_FORMAT), "'");
		this.redshiftDateFormat = wrap(getString(REDSHIFT_DATE_FORMAT), "'");
		this.redshiftServerless = getBoolean(REDSHIFT_SERVERLESS);

		this.parallelism = getInt(PARALLELISM);
		this.batchSize = getInt(BATCH_SIZE);
		this.dwhBatchSize = getInt(DWH_BATCH_SIZE);
		this.truncOnLoad = getBoolean(TRUNCATE_BEFORE_LOAD);

		this.oracleDateFormat = getString(ORACLE_DATE_FORMAT);
		this.oracleTimestampFormat = getString(ORACLE_TIMESTAMP_FORMAT);
		this.fileFormat = opt(getString(FILE_FORMAT))
			.map(WarehouseCopyFileFormat::fromString);
		this.partitioningColumn = opt(getString(PARTITIONING_COLUMN));
		this.clusteringingColumns = StreamEx.of(getList(CLUSTERING_COLUMNS)).map(columnNormalizer::toStorage).toList();
		this.distKey = opt(getString(REDSHIFT_DIST_KEY))
			.orElse("");
		this.sortKeys = StreamEx.of(getList(REDSHIFT_SORT_KEYS))
			.map(columnNormalizer::toStorage)
			.toList();
		this.directUpload = getBoolean(DIRECT_UPLOAD);
		this.rejectLimit = getInt(REJECT_LIMIT);
		this.oracleAutonomousObservableExceptions = StreamEx.of(getList(ORACLE_AUTONOMOUS_OBSERVABLE_EXCEPTIONS))
			.map(columnNormalizer::toStorage).toList();
		this.intermediateFileFormat = getString(INTERMEDIATE_FILE_FORMAT);
		this.skipHeader = getBoolean(SKIP_HEADER);
		this.skipCopyErrorHandling = getBoolean(SKIP_COPY_ERROR_HANDLING);

		this.nlsDateFormat = getString(NLS_DATE_FORMAT);
		this.nlsTimestampFormat = getString(NLS_TIMESTAMP_FORMAT);

		this.warehouseDateFormat = getString(WAREHOUSE_DATE_FORMAT);
		this.warehouseTimestampFormat = getString(WAREHOUSE_TIMESTAMP_FORMAT);

		this.fallbackSingleRecordMode = getBoolean(FALLBACK_SINGLE_RECORD_MODE);

		this.postFlushSqlScript = opt(getString(POST_FLUSH_SQL_SCRIPT));
		this.intermediateFileDelimiter = getString(INTERMEDIATE_FILE_DELIMITER);
		String quoteChar = getString(INTERMEDIATE_FILE_QUOTE_CHAR);

		if (Objects.isNull(quoteChar) && ORACLE_AUTONOMOUS.equals(authConfig.dbType)) {
			this.intermediateFileQuoteChar = "\"";
		} else {
			this.intermediateFileQuoteChar = quoteChar;
		}

	}

	private void normalizeMappingConfig(ColumnNormalizer columnNormalizer) {
		mappingConfig.ifPresent(mapping -> {
			mapping.setMapping(toLinkedHashMap(
				EntryStream
					.of(mapping.getMapping())
					.mapValues(columns ->
						EntryStream.of(columns)
							.mapKeys(columnNormalizer::toStorage)
							.toMap()
					)));
		});
	}

	public static NexlaConfigDef configDef() {

		return new NexlaConfigDef(sinkConfigDef())

			.withKey(nexlaKey(TABLE, STRING, null)
				.documentation("Table")
				.group(JDBC_SINK_GROUP)
				.displayName("Table"))

			.withKey(nexlaKey(DATABASE, STRING, null)
				.documentation("Database")
				.group(JDBC_SINK_GROUP)
				.displayName("Database"))

			.withKey(nexlaKey(PRIMARY_KEY, ConfigDef.Type.LIST, emptyList())
				.documentation("Primary key columns, comma-separated")
				.group(JDBC_SINK_GROUP)
				.displayName("Primary key"))

			.withKey(nexlaKey(INSERT_MODE, ConfigDef.Type.STRING, ConfigDef.NO_DEFAULT_VALUE)
				.validator(SourceConnectorConfig.EnumValidator.in(InsertMode.values()))
				.documentation("Insert mode")
				.group(JDBC_SINK_GROUP)
				.displayName("Insert mode"))

			.withKey(nexlaKey(TEMP_S3_UPLOAD_BUCKET, ConfigDef.Type.STRING, "temp-nexla")
				.importance(LOW)
				.documentation("S3 upload bucket")
				.group(JDBC_SINK_GROUP)
				.displayName("S3 upload bucket"))

			.withKey(nexlaKey(TEMP_S3_UPLOAD_PREFIX, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("S3 upload prefix")
				.group(JDBC_SINK_GROUP)
				.displayName("S3 upload prefix"))

			.withKey(nexlaKey(TEMP_AZURE_BLOB_UPLOAD_BUCKET, ConfigDef.Type.STRING, "temp-db-copy")
				.importance(LOW)
				.documentation("azure blob upload bucket")
				.group(JDBC_SINK_GROUP)
				.displayName("azure blob upload bucket"))

			.withKey(nexlaKey(TEMP_AZURE_BLOB_UPLOAD_PREFIX, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("azure blob upload prefix")
				.group(JDBC_SINK_GROUP)
				.displayName("azure blob upload prefix"))

			.withKey(nexlaKey(TEMP_AZURE_BLOB_DELETE, ConfigDef.Type.BOOLEAN, true)
				.importance(LOW)
				.documentation("Delete temporary files after upload")
				.group(JDBC_SINK_GROUP)
				.displayName("Delete temporary files after upload"))

			.withKey(nexlaKey(STOP_ON_ERROR, ConfigDef.Type.BOOLEAN, false)
				.documentation("Stop in case of error")
				.group(JDBC_SINK_GROUP)
				.displayName("Stop on error"))

			.withKey(nexlaKey(BATCH_SIZE, ConfigDef.Type.INT, 10000)
				.documentation("Number of rows to be flushed at once")
				.group(JDBC_SINK_GROUP)
				.displayName("Batch size"))

			.withKey(nexlaKey(DWH_BATCH_SIZE, ConfigDef.Type.INT, 1_000_000)
				.documentation("Number of rows to be flushed at once to DWH")
				.group(JDBC_SINK_GROUP)
				.displayName("Batch size"))

			.withKey(nexlaKey(PARALLELISM, ConfigDef.Type.INT, 1)
				.documentation("Level of pallelism for batches being flushed")
				.group(JDBC_SINK_GROUP)
				.displayName("Flush parallelism"))

			.withKey(nexlaKey(TEMP_S3_DELETE, ConfigDef.Type.BOOLEAN, true)
				.importance(LOW)
				.documentation("Delete temporary files after upload")
				.group(JDBC_SINK_GROUP)
				.displayName("Delete temporary files after upload"))

			.withKey(nexlaKey(COPY_ALLOWED, ConfigDef.Type.BOOLEAN, true)
				.importance(LOW)
				.documentation("Allow COPY")
				.group(JDBC_SINK_GROUP)
				.displayName("Allow COPY operation for bulk data loading"))

			.withKey(nexlaKey(S3_CREDS_ENC, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("S3 Creds enc")
				.group(JDBC_SINK_GROUP)
				.displayName("S3 Creds enc"))
			.withKey(nexlaKey(S3_CREDS_ENCIV, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("S3 Creds enc IV")
				.group(JDBC_SINK_GROUP)
				.displayName("S3 Creds enc IV"))

			.withKey(nexlaKey(AZURE_CREDS_ENC, STRING, null)
				.importance(LOW)
				.documentation("Azure Creds enc")
				.group(JDBC_SINK_GROUP)
				.displayName("Azure Creds enc"))
			.withKey(nexlaKey(AZURE_CREDS_ENCIV, STRING, null)
				.importance(LOW)
				.documentation("Azure Creds enc IV")
				.group(JDBC_SINK_GROUP)
				.displayName("Azure Creds enc IV"))

			.withKey(nexlaKey(UPSERT_NULLS, BOOLEAN, true)
				.importance(LOW)
				.documentation("Upsert to null based on a transformed value")
				.group(JDBC_SINK_GROUP)
				.displayName("Update Nulls"))

			.withKey(nexlaKey(EMPTY_TO_NULL, BOOLEAN, false)
				.importance(LOW)
				.documentation("Convert empty values to null(Redshift only)")
				.group(JDBC_SINK_GROUP)
				.displayName("Convert empty values to null(Redshift only)"))

			// https://docs.aws.amazon.com/redshift/latest/dg/r_DATEFORMAT_and_TIMEFORMAT_strings.html
			// https://stackoverflow.com/questions/28287434/how-to-insert-timestamp-column-into-redshift
			.withKey(nexlaKey(REDSHIFT_TIME_FORMAT, STRING, "auto")
				.importance(LOW)
				.documentation("Redshift time format")
				.group(JDBC_SINK_GROUP)
				.displayName("Redshift time format"))

			.withKey(nexlaKey(REDSHIFT_SERVERLESS, BOOLEAN, false)
				.importance(LOW)
				.documentation("Redshift serverless instance")
				.group(JDBC_SINK_GROUP)
				.displayName("Redshift serverless instance"))

			.withKey(nexlaKey(REDSHIFT_DATE_FORMAT, STRING, "auto")
				.importance(LOW)
				.documentation("Redshift date format")
				.group(JDBC_SINK_GROUP)
				.displayName("Redshift date format"))

			.withKey(nexlaKey(NULL_AS, STRING, "'null'")
				.importance(LOW)
				.documentation("Null value format")
				.group(JDBC_SINK_GROUP)
				.displayName("Null value format"))

			.withKey(nexlaKey(TRUNCATE_BEFORE_LOAD, BOOLEAN, false)
				.importance(LOW)
				.documentation("Truncate destination table before load new data")
				.group(JDBC_SINK_GROUP)
				.displayName("Truncate destination table before load new data"))

			.withKey(nexlaKey(FIREBOLT_TABLE_TYPE, STRING, "FACT")
				.validator(SinkConnectorConfig.ValidStringNoCase.in("fact", "dimension"))
				.importance(LOW)
				.documentation("Firebolt table type. Valid values are FACT or DIMENSION")
				.group(JDBC_SINK_GROUP)
				.displayName("Firebolt table type"))

			.withKey(nexlaKey(ORACLE_DATE_FORMAT, STRING, AUTO)
				.importance(LOW)
				.documentation("Oracle Autonomous date format")
				.group(JDBC_SINK_GROUP)
				.displayName("Oracle Autonomous date format"))

			.withKey(nexlaKey(ORACLE_TIMESTAMP_FORMAT, STRING, AUTO)
				.importance(LOW)
				.documentation("Oracle Autonomous Timestamp format")
				.group(JDBC_SINK_GROUP)
				.displayName("Oracle Autonomous Timestamp format"))

			.withKey(nexlaKey(FILE_FORMAT, STRING, null)
				.documentation("Unload format: " + StreamEx.of(WarehouseCopyFileFormat.values()).map(Enum::toString).joining(" | "))
				.group(JDBC_SINK_GROUP)
				.displayName("Unload format"))
			.withKey(nexlaKey(PARTITIONING_COLUMN, STRING, null))
			.withKey(nexlaKey(CLUSTERING_COLUMNS, LIST, emptyList()))

			.withKey(nexlaKey(REDSHIFT_DIST_KEY, STRING, null)
				.documentation("Redshift Distribution Key")
				.displayName("Redshift Distribution Key"))

			.withKey(nexlaKey(REDSHIFT_SORT_KEYS, LIST, emptyList())
				.documentation("Redshift Sort Keys")
				.displayName("Redshift Sort Keys"))

			.withKey(nexlaKey(DIRECT_UPLOAD, BOOLEAN, false)
				.documentation("Upload DWH directly")
				.group(CONNECTOR_GROUP)
				.displayName("Upload DWH directly"))

			.withKey(nexlaKey(REJECT_LIMIT, INT, 0)
//			.withKey(nexlaKey(REJECT_LIMIT, STRING, "UNLIMITED") // todo [ENHANCEMENT] [ORACLE AUTONOMOUS] Can be put to "UNLIMITED" after implementing monitoring, so whole batch won't fail if some parts of the response are failing.
				.documentation("Reject limit")
				.group(JDBC_SINK_GROUP)
				.displayName("Reject limit"))

			.withKey(nexlaKey(ORACLE_AUTONOMOUS_OBSERVABLE_EXCEPTIONS, LIST, Arrays.asList("ORA-", "KUP-"))
				.documentation("Oracle Autonomous exception prefixes to look in the logs table." +
					" Default to [ORA-, KUP-]")
				.group(JDBC_SINK_GROUP)
				.displayName("Oracle Autonomous exception lookup prefixes."))

			.withKey(nexlaKey(INTERMEDIATE_FILE_FORMAT, STRING, null)
				.documentation("File format used to store data for copy operation")
				.group(JDBC_SINK_GROUP)
				.displayName("Intermediate file format"))

			.withKey(nexlaKey(SKIP_HEADER, BOOLEAN, false)
				.documentation("Specify if file headers should be skipped. Applicable to Replication flow only")
				.group(CONNECTOR_GROUP)
				.displayName("Skip file headers"))

			.withKey(nexlaKey(TEMP_STORAGE_TYPE, STRING, WarehouseTempStorageType.AMAZON_S3.name())
				.importance(LOW)
				.documentation("Define which mechanism should be used as temp storage for warehouse load")
				.group(JDBC_SINK_GROUP)
				.displayName("Define which mechanism should be used as temp storage for warehouse load"))
			.withKey(nexlaKey(POST_FLUSH_SQL_SCRIPT, ConfigDef.Type.STRING, "")
				.documentation("Post flush sql script")
				.group(JDBC_SINK_GROUP)
				.displayName("Post flush sql script"))

			.withKey(nexlaKey(INTERMEDIATE_FILE_DELIMITER, STRING, null)
				.documentation("File delimiter for the file used to store data for copy operation")
				.group(JDBC_SINK_GROUP)
				.displayName("Intermediate file delimiter"))

			.withKey(nexlaKey(INTERMEDIATE_FILE_QUOTE_CHAR, STRING, null)
				.documentation("Quote char for the file used to store data for copy operation")
				.group(JDBC_SINK_GROUP)
				.displayName("Intermediate file quote char"))

			.withKey(nexlaKey(FLUSH_INTERVAL_MIN, INT, 5)
				.documentation("Delay between flush")
				.group(JDBC_SINK_GROUP)
				.displayName("Delay between flush"))

			.withKey(nexlaKey(SKIP_COPY_ERROR_HANDLING, BOOLEAN, true)
				.documentation("Skip copy command error handling for warehouses")
				.group(JDBC_SINK_GROUP)
				.displayName("Skip copy command error handling for warehouses"))

			.withKey(nexlaKey(NLS_DATE_FORMAT, STRING, null)
				.documentation("NLS date format to be used during insert queries")
				.group(JDBC_SINK_GROUP)
				.displayName("NLS date format to be used during insert queries"))

			.withKey(nexlaKey(NLS_TIMESTAMP_FORMAT, STRING, null)
				.documentation("NLS timestamp format to be used during insert queries")
				.group(JDBC_SINK_GROUP)
				.displayName("NLS timestamp format to be used during insert queries"))

			.withKey(nexlaKey(WAREHOUSE_DATE_FORMAT, STRING, null)
					.documentation("Date format to be used during copy command queries")
					.group(JDBC_SINK_GROUP)
					.displayName("Date format to be used during copy command queries"))

			.withKey(nexlaKey(WAREHOUSE_TIMESTAMP_FORMAT, STRING, null)
					.documentation("Timestamp format to be used during copy command queries")
					.group(JDBC_SINK_GROUP)
					.displayName("Timestamp format to be used during copy command queries"))

			.withKey(nexlaKey(FALLBACK_SINGLE_RECORD_MODE, BOOLEAN, true)
				.documentation("Enable fallback to single record mode when any error happens during insert or upsert process")
				.group(JDBC_SINK_GROUP)
				.displayName("Enable fallback to single record mode when any error happens during insert or upsert process"))
		;
	}

	public enum InsertMode {
		INSERT,
		UPSERT
	}

	@Override
	public JdbcAuthConfig authConfig() {
		return authConfig;
	}

	private boolean isWareHouseWithS3Support() {
		return REDSHIFT.equals(authConfig.dbType)  ||
			SNOWFLAKE.equals(authConfig.dbType)  ||
			ORACLE_AUTONOMOUS.equals(authConfig.dbType)  ||
			FIREBOLT.equals(authConfig.dbType)  ||
			DATABRICKS.equals(authConfig.dbType);
	}

	private Map<String, ?> azureConfigurations() {
		Map<String, String> config = new HashMap<>();
		config.put(STORAGE_ACCOUNT_NAME, getString(STORAGE_ACCOUNT_NAME));
		config.put(STORAGE_ACCOUNT_KEY, getString(STORAGE_ACCOUNT_KEY));
		config.put(KEY_CONNECTION_STRING, getString(KEY_CONNECTION_STRING));
		return  config;
	}
}
