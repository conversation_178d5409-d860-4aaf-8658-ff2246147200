package com.nexla.connector.properties;

import com.nexla.common.NexlaConstants;

public class SqlConfigAccessor {

	public static final String OFFSET_POSITION_INCREMENTING = "offset.position.incrementing";
	public static final String OFFSET_POSITION_TIMESTAMP = "offset.position.timestamp";

	public static final String PARTITION_KEY = "partition.key";

	public static final String PRIMARY_KEY = "primary.key";

	public static final String MONITOR_POLL_MS = NexlaConstants.MONITOR_POLL_MS;
	public static final String POLL_MS = "poll.ms";

	public static final String QUERY = "query";
	public static final String QUERY_PROBE_TIMEOUT = "query.probe.timeout";
	public static final String TABLE = "table";

	public static final String INCREMENTING_LOAD_FROM = "incrementing.load.from";
	public static final String TIMESTAMP_LOAD_FROM = "timestamp.load.from";

	public static final String INTERNAL_INCREMENTING_COLUMN_FROM = "internal.incrementing.column.from";
	public static final String INTERNAL_INCREMENTING_COLUMN_TO = "internal.incrementing.column.to";
	public static final String JDBC_TASK_ID = "taskId";
	public static final String INTERNAL_TIMESTAMP_COLUMN_FROM = "internal.timestamp.column.from";
	public static final String INTERNAL_TIMESTAMP_COLUMN_TO = "internal.timestamp.column.to";

	public static final String BATCH_SIZE_APPROX = "batch.size.approx";

	public static final String INCREMENTING_COLUMN_NAME = "incrementing.column.name";
	public static final String TIMESTAMP_COLUMN_NAME = "timestamp.column.name";

	public static final String MODE = "mode";
	public static final String MODE_VALUE_INCREMENTING = "incrementing";
	public static final String MODE_VALUE_TIMESTAMP = "timestamp";
	public static final String MODE_VALUE_TIMESTAMP_INCREMENTING = "incrementing,timestamp";
	public static final String MODE_NONE = "none";
	public static final String FILE_BUFFER = "file.buffer";

	public static final String INSERT_MODE = "insert.mode";
	public static final String FLUSH_INTERVAL_MIN = "flush.interval.minutes";
	public static final String CDC_ENABLED = "cdc.enabled";
	public static final String CDC_CAPTURE_DELETE = "cdc.capture.delete";
	public static final String CDC_SHAPSHOT_ENABLED = "cdc.snapshot.enabled";
	public static final String CDC_POSTGRES_LOGICAL_DECODER = "cdc.postgres.logical.decoder";
	public static final String CDC_MODE = "cdc.mode";
	public static final String CDC_TABLE_CONFIG = "cdc.table.config";

	public static final String JDBC_PARAMETERS = "jdbc.parameters";
	public static final String JDBC_IAM_AUTH_ENABLED = "jdbc.iam.auth.enabled";
	public static final String COMMIT_ON_READ = "commit.on.read";

	public static final String JDBC_SOURCE_TRANSPORT = "query.method";
	public static final String JDBC_SOURCE_TRANSPORT_UNLOAD = "unload";
	public static final String JDBC_SOURCE_TRANSPORT_SQL = "sql";

}
