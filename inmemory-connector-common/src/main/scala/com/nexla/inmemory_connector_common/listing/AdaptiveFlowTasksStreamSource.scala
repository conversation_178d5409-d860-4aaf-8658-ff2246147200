package com.nexla.inmemory_connector_common.listing

import akka.NotUsed
import akka.actor.ActorSystem
import akka.stream.scaladsl.Source
import com.nexla.inmemory_connector_common.listing.AdaptiveFlowTasksStreamSource.TaskIterator
import com.nexla.listing.client.{AdaptiveFlowTask, ListingClient}
import com.nexla.sc.util.StrictNexlaLogging

import java.time.Instant
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}


class AdaptiveFlowTasksStreamSource(adaptiveFlowTasksClient: ListingClient,
                                    cooldownPeriod: FiniteDuration,
                                    maxRestartPeriod: FiniteDuration = 1.day)(implicit val ec: ExecutionContext, system: ActorSystem) extends StrictNexlaLogging {

  def listTasks(sourceId: Int, updateDataIngestionTs: () => Unit): Source[AdaptiveFlowTask, NotUsed] = {

    logger.info(s"List tasks for sourceId $sourceId STARTED")
    val taskIterator = new TaskIterator(sourceId, adaptiveFlowTasksClient, cooldownPeriod, maxRestartPeriod, updateDataIngestionTs)

    // 1 - keep polling the tasks until exhausted
    // 2 - after hitting exhaustion, keep polling - but stop emitting heartbeats
    // 3 - if prolonged exhaustion for 10 minutes - shutdown (killed by ctrl jobscheduler because no heartbeats - idle)

    Source.unfoldAsync(None) { _ =>
      taskIterator.requestNewTask().map {
        case Some(file) => Some((None, file))
        case None => None
      }
    }
  }

}

object AdaptiveFlowTasksStreamSource {
  private class TaskIterator(sourceId: Int,
                             adaptiveFlowTasksClient: ListingClient,
                             listingCooldownPeriod: FiniteDuration,
                             maxRestartPeriod: FiniteDuration,
                             updateDataTs: () => Unit)
                            (implicit val ec: ExecutionContext, system: ActorSystem) extends StrictNexlaLogging {
    private val globalTimeoutDate = Instant.now().plusSeconds(maxRestartPeriod.toSeconds)

    private sealed trait Result

    private case class TaskFound(task: AdaptiveFlowTask) extends Result
    private case object ExecutionFinished extends Result

    private def innerFetchTask(): Future[Result] = {
      Thread.sleep(5.seconds.toMillis)
      logger.info("doing inner fetch task")
      updateDataTs()
      val result = Future(adaptiveFlowTasksClient.takeAdaptiveFlowTask(sourceId).adaptiveFlowTask.asScala)
      result.flatMap {
        case Some(task) => Future(TaskFound(task))
        case None =>
          akka.pattern.after(15.seconds)(innerFetchTask())
      }
    }

    def requestNewTask(): Future[Option[AdaptiveFlowTask]] = {
      if (Instant.now().isAfter(globalTimeoutDate)) {
        logger.info(s"Global waiting date exceeded: $maxRestartPeriod")
        Future.successful(None)
      } else {
        innerFetchTask().flatMap {
          case TaskFound(task) =>
            logger.info(s"Task found: id=${task.getId}, cfg=${task.getParameters}")
            Future.successful(Some(task))
          case ExecutionFinished =>
            logger.info(s"All finished. No more tasks as of now")
            Future.successful(None)
        }.recoverWith {
          case ex =>
            logger.error(s"Exception while fetching task. Retrying", ex)
            akka.pattern.after(listingCooldownPeriod)(requestNewTask())
        }
      }
    }
  }
}
