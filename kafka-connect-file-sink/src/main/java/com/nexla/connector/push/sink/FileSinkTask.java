package com.nexla.connector.push.sink;

import com.nexla.common.FileUtils;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.file.sink.FileSinkNotificationSender;
import com.nexla.connector.file.sink.FileSinkWriter;
import com.nexla.connector.file.sink.PollingWriter;
import com.nexla.connector.file.sink.names.FileNamingGenerator;
import com.nexla.file.service.FileConnectorService;
import com.nexla.probe.ftp.FileConnectorServiceBuilder;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.errors.RetriableException;

import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.nexla.common.ResourceType.SINK;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

public class FileSinkTask extends BaseSinkTask<FileSinkConnectorConfig> {

	private PollingWriter pollingWriter;

	protected FileSinkWriter fileSinkWriter;

	protected FileConnectorService probeService;


	@Override
	public void doStart() {
		logger.info("sinkType={}", config.sinkType.name());

		String localDir = config.localBufferDir + "/" + UUID.randomUUID().toString();
		new File(localDir).mkdirs();

		this.sinkTelemetryReporter =
				Optional.of(new ConnectorTelemetryReporter(config.sinkId, config.sinkType.name(), SINK, isDedicatedNode));

		FileConnectorServiceBuilder fileConnectorServiceBuilder = new FileConnectorServiceBuilder(adminApiClient, listingClient, config.decryptKey);
		this.probeService = fileConnectorServiceBuilder.createFileConnectorService(config.sinkType);

		probeService.initLogger(SINK, config.sinkId, taskId);
		logger.info("probeService={}", probeService);

		this.pollingWriter = new PollingWriter(probeService, config, offsetsSender, logger);
		FileSinkNotificationSender notificationSender = new FileSinkNotificationSender(config, nexlaMessageProducer);
		FileNamingGenerator fileNamingGenerator = createFileNamingGenerator(config, localDir);

		this.fileSinkWriter = new FileSinkWriter(
			empty(),
			pollingWriter,
			config,
			notificationSender,
			localDir,
			logger,
			true,
			ctrlClient, probeService,
			adminApiClient,
			fileNamingGenerator);
		// Recover initial assignments
		recoverAssignment(context.assignment());
	}

	protected FileNamingGenerator createFileNamingGenerator(FileSinkConnectorConfig config, String localDir) {
		return new FileNamingGenerator(config, localDir);
	}

	protected PostponedFlush postponedFlush() {
		KafkaProgressTracker kafkaProgressTracker = new KafkaProgressTracker(getKafkaLagCalculator(), adminApiClient, ctrlClient, logger);
		return new PostponedFlush(this, kafkaProgressTracker, logger) {
			@Override
			protected boolean dataMatureMs(long delayMs) {
				return fileSinkWriter.writersReady(TimeUnit.MILLISECONDS.toMinutes(delayMs));
			}

			@Override
			protected boolean noDataMatureMs(long delayMs) {
				return !hasAccumulatedRecords() && System.currentTimeMillis() - getLastRecordsPutTs() > delayMs;
			}

			@Override
			protected boolean hasAccumulatedRecords() {
				return fileSinkWriter.getTotalRecordsNum() > 0;
			}
		};
	}

	@Override
	protected FileSinkConnectorConfig parseConfig(Map<String, String> props) {
		return new FileSinkConnectorConfig(props);
	}

	protected ConfigDef configDef() {
		return FileSinkConnectorConfig.configDef();
	}

	@Override
	protected void doPut(StreamEx<NexlaMessageContext> messages, int streamSize) {
		messages.forEach(m -> fileSinkWriter.write(m, ofNullable(runId), config.mappingConfig));
	}

	@Override
	@SneakyThrows
	public boolean doFlush(ReadyToFlush readyToFlush) {
		if (readyToFlush.flush) {
			pipelineStatusReport.ifPresent(status -> status.reportPipelineFlushStarted(fileSinkWriter.getTotalRecordsNum()));
			try {
				boolean flushResult = fileSinkWriter.flush(
					flushedRecords -> pipelineStatusReport.ifPresent(
						status -> status.flushProgress.addAndGet(flushedRecords)));
				this.postponedFlush.resetFailures();
				return flushResult && readyToFlush.reportFlushed;
			} catch (Exception e) {
				this.postponedFlush.incrementFailures(e);
				throw new RetriableException(String.format("Failed file upload. %s", e.getMessage()), e);
			}
		} else {
			return false;
		}
	}

	protected void onPipelineFlushed() {
		Optional.ofNullable(
			FileConnectorService.FILESINK_FLUSH_FILE_RENAME_TRACKER.get(config.sinkId)
		).ifPresent(Map::clear);

		super.onPipelineFlushed();
	}

	@Override
	protected void refreshPipelineStatus() {
		super.refreshPipelineStatus();
		pipelineStatusReport.ifPresent(status -> status.bufferedRecords.set(fileSinkWriter.getTotalRecordsNum()));
	}

	@Override
	protected Set<Long> getActiveRunIds() {
		return fileSinkWriter.getWriters().values().stream().mapToLong(Collection::size).sum() == 0
			? Collections.emptySet()
			: fileSinkWriter.heartbeatRunIds;
	}

	@Override
	protected Optional<Set<Long>> getAndResetFlushedRunIds() {
		Set<Long> heartbeatRunIds = new HashSet<>(fileSinkWriter.heartbeatRunIds);
		fileSinkWriter.heartbeatRunIds.clear();
		return Optional.of(heartbeatRunIds);
	}

	@Override
	public void open(Collection<org.apache.kafka.common.TopicPartition> partitions) throws ConnectException {
		logger.info("Open: " + partitions);
		try {
			recoverAssignment(partitions);
			postponedFlush.resetFirstFlushTsThreshold();
		} catch (Exception e) {
			reportTelemetryFromError(e);
			throw e;
		}
	}

	@Override
	public void close(Collection<org.apache.kafka.common.TopicPartition> partitions) throws ConnectException {
		fileSinkWriter.close();
		FileUtils.closeSilently(pollingWriter);
		FileUtils.closeSilently(probeService);
	}

	private void recoverAssignment(Collection<org.apache.kafka.common.TopicPartition> tps) throws ConnectException {

		List<TopicPartition> topicPartitions = tps.stream()
			.map(tp -> {
				// See if this is a new assignment
				TopicPartition partition = toPartition(tp);
				if (!fileSinkWriter.containsTopicPartition(partition)) {
					logger.info("Assigned new partition {}", tp);
					try {
						recoverPartition(tp);
					} catch (Exception e) {
						throw new ConnectException("Failed to resume TopicPartition", e);
					}
				}
				return partition;
			})
			.collect(toList());

		fileSinkWriter.setTopicPartitions(topicPartitions);
	}

	private TopicPartition toPartition(org.apache.kafka.common.TopicPartition tp) {
		return new TopicPartition(tp.topic(), tp.partition());
	}

	private void recoverPartition(org.apache.kafka.common.TopicPartition tp) {
		context.pause(tp);
		TopicPartition partition = toPartition(tp);
		// Recover last committed offset from storage
		long nextOffset = pollingWriter.fetchLastOffset(partition).orElse(0L);
		logger.info("Recovering partition {} from offset {}", tp, nextOffset);
		context.offset(tp, nextOffset);
		context.resume(tp);
	}

}
