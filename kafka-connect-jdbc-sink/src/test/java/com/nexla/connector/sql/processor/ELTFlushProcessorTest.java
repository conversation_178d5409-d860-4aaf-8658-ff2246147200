package com.nexla.connector.sql.processor;

import com.nexla.common.ConnectionType;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.report.PipelineStatusReport;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.sql.sink.JdbcSinkTask;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.copy.FlushResult;
import connect.jdbc.sink.warehouse.DataWarehouseSink;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Connection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;
import java.util.HashMap;

import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.connector.config.BaseConnectorConfig.UNIT_TEST;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ELTFlushProcessorTest {

  @Mock
  private TableProcessor tableProcessorMock;

  private Integer sinkId = 1;

  @Mock
  private BaseSinkTask.ExceptionHandler exceptionHandlerMock;

  @Mock
  private JdbcSinkTask.MetricHandler metricHandlerMock;

  @Mock
  private AtomicLong lastProcessedMessageTsMock;

  @Mock
  private DataWarehouseSink dataWarehouseSinkMock;

  @Mock
  private PostponedFlush postponedFlushMock;

  @Mock
  private PipelineStatusReport pipelineStatusReportMock;

  @Mock
  private Supplier<Connection> supplierMock;

  @Mock
  private Connection connectionMock;

  @Mock
  private DbDialect dbDialectMock;

  @Captor
  private ArgumentCaptor<FlushResult> flushResultCaptor;

  private ELTFlushProcessor processor;

  @Before
  public void setUp(){
    this.processor = new ELTFlushProcessor(
        tableProcessorMock,
        sinkId,
        exceptionHandlerMock,
        metricHandlerMock,
        lastProcessedMessageTsMock
    );
  }

  @Test
  @SneakyThrows
  public void flushWhenInsertAndNotFinalFlush() {
    int offsets = 1;
    int sentRecords = 1;
    when(tableProcessorMock.getConnProvider()).thenReturn(supplierMock);
    when(supplierMock.get()).thenReturn(connectionMock);
    when(tableProcessorMock.getDbDialect()).thenReturn(dbDialectMock);
    when(dbDialectMock.isAutoCommit()).thenReturn(false);
    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.of(buildFlushResult(offsets, sentRecords)));
    when(dataWarehouseSinkMock.getCurrentBatchSize()).thenReturn(0);

    boolean flush = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.INSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FLUSH
    );

    assertFalse(flush);

    verify(postponedFlushMock).resetFailures();
    verify(metricHandlerMock).handleMetrics(flushResultCaptor.capture());
    verify(connectionMock).commit();
    verify(dataWarehouseSinkMock).getCurrentBatchSize();
    verify(dataWarehouseSinkMock).flushBatch(connectionMock);
    verifyFlushResult(offsets, sentRecords);
  }

  @Test
  @SneakyThrows
  public void flushWhenInsertAndFinalFlush() {
    int offsets = 2;
    int sentRecords = 2;
    when(tableProcessorMock.getConnProvider()).thenReturn(supplierMock);
    when(supplierMock.get()).thenReturn(connectionMock);
    when(tableProcessorMock.getDbDialect()).thenReturn(dbDialectMock);
    when(dbDialectMock.isAutoCommit()).thenReturn(false);
    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.of(buildFlushResult(offsets, sentRecords)));
    when(dataWarehouseSinkMock.getCurrentBatchSize()).thenReturn(0);

    boolean flush = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.INSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FINAL_FLUSH
    );

    assertTrue(flush);

    verify(postponedFlushMock).resetFailures();
    verify(metricHandlerMock).handleMetrics(flushResultCaptor.capture());
    verify(connectionMock).commit();
    verify(dataWarehouseSinkMock).getCurrentBatchSize();
    verify(dataWarehouseSinkMock).flushBatch(connectionMock);
    verifyFlushResult(offsets, sentRecords);
  }

  @Test
  @SneakyThrows
  public void flushWhenInsertAndFinalFlushAndNoFlushResult() {
    when(tableProcessorMock.getConnProvider()).thenReturn(supplierMock);
    when(supplierMock.get()).thenReturn(connectionMock);
    when(tableProcessorMock.getDbDialect()).thenReturn(dbDialectMock);
    when(dbDialectMock.isAutoCommit()).thenReturn(false);
    when(dataWarehouseSinkMock.getBufferSize()).thenReturn(0);
    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.empty());

    boolean flush = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.INSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FINAL_FLUSH
    );

    assertTrue(flush);

    verify(postponedFlushMock).resetFailures();
    verify(metricHandlerMock, never()).handleMetrics(flushResultCaptor.capture());
    verify(connectionMock).commit();
    verify(dataWarehouseSinkMock, times(2)).getBufferSize();
    verify(dataWarehouseSinkMock).flushBatch(connectionMock);
  }

  @Test
  @SneakyThrows
  public void flushWhenInsertAsUpsertMode() {
    int offsets = 2;
    int sentRecords = 2;
    when(tableProcessorMock.getConnProvider()).thenReturn(supplierMock);
    when(supplierMock.get()).thenReturn(connectionMock);
    when(tableProcessorMock.getDbDialect()).thenReturn(dbDialectMock);
    when(dbDialectMock.isAutoCommit()).thenReturn(false);
    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.of(buildFlushResult(offsets, sentRecords)));
    when(dataWarehouseSinkMock.getCurrentBatchSize()).thenReturn(0);

    boolean flush = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FLUSH
    );

    assertFalse(flush);

    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.of(buildFlushResult(offsets+1, sentRecords+1)));

    boolean flush2 = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FINAL_FLUSH
    );

    assertTrue(flush2);

    verify(postponedFlushMock, times(2)).resetFailures();
    verify(metricHandlerMock).handleMetrics(flushResultCaptor.capture());
    verify(connectionMock, times(2)).commit();
    verify(dataWarehouseSinkMock, times(4)).getCurrentBatchSize();
    verify(dataWarehouseSinkMock, times(2)).flushBatch(connectionMock);
    verifyFlushResult(3, 5);
  }

  @Test
  @SneakyThrows
  public void flushWhenInsertAndUpsertAsUpsertMode() {
    int offsets = 5;
    int sentRecords = 2;
    when(tableProcessorMock.getConnProvider()).thenReturn(supplierMock);
    when(supplierMock.get()).thenReturn(connectionMock);
    when(tableProcessorMock.getDbDialect()).thenReturn(dbDialectMock);
    when(dbDialectMock.isAutoCommit()).thenReturn(false);
    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.of(buildFlushResult(offsets, sentRecords)));
    when(dataWarehouseSinkMock.getCurrentBatchSize()).thenReturn(3);
    when(dataWarehouseSinkMock.getBufferSize()).thenReturn(2);

    boolean flush = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.NOT_READY
    );

    assertFalse(flush);

    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.empty());
    when(dataWarehouseSinkMock.flushPipeline(connectionMock)).thenReturn(Optional.of(buildFlushResult(3, 3)));

    boolean flush2 = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FLUSH
    );

    assertFalse(flush2);

    when(dataWarehouseSinkMock.getCurrentBatchSize()).thenReturn(0);

    boolean flush3 = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FINAL_FLUSH
    );

    assertTrue(flush3);

    verify(postponedFlushMock, times(3)).resetFailures();
    verify(metricHandlerMock).handleMetrics(flushResultCaptor.capture());
    verify(connectionMock, times(3)).commit();
    verify(dataWarehouseSinkMock, times(5)).getCurrentBatchSize();
    verify(dataWarehouseSinkMock, times(3)).flushBatch(connectionMock);
    verifyFlushResult(5, 5);
  }

  @Test
  @SneakyThrows
  public void flushWhenUpsertAsUpsertMode() {
    int offsets = 6;
    int sentRecords = 2;
    when(tableProcessorMock.getConnProvider()).thenReturn(supplierMock);
    when(supplierMock.get()).thenReturn(connectionMock);
    when(tableProcessorMock.getDbDialect()).thenReturn(dbDialectMock);
    when(dbDialectMock.isAutoCommit()).thenReturn(false);
    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.empty());
    when(dataWarehouseSinkMock.getCurrentBatchSize()).thenReturn(3);
    when(dataWarehouseSinkMock.getBufferSize()).thenReturn(2);

    boolean flush = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.NOT_READY
    );

    assertFalse(flush);

    when(dataWarehouseSinkMock.flushBatch(connectionMock)).thenReturn(Optional.empty());
    when(dataWarehouseSinkMock.flushPipeline(connectionMock)).thenReturn(Optional.of(buildFlushResult(offsets, sentRecords)));

    boolean flush2 = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FLUSH
    );

    assertFalse(flush2);

    when(dataWarehouseSinkMock.getCurrentBatchSize()).thenReturn(0);

    boolean flush3 = processor.flush(
        dataWarehouseSinkMock,
        buildConfig(JdbcSinkConnectorConfig.InsertMode.UPSERT),
        postponedFlushMock,
        Optional.empty(),
        ReadyToFlush.FINAL_FLUSH
    );

    assertTrue(flush3);

    verify(postponedFlushMock, times(3)).resetFailures();
    verify(metricHandlerMock).handleMetrics(flushResultCaptor.capture());
    verify(connectionMock, times(3)).commit();
    verify(dataWarehouseSinkMock, times(4)).getCurrentBatchSize();
    verify(dataWarehouseSinkMock, times(3)).flushBatch(connectionMock);
    verifyFlushResult(offsets, sentRecords);
  }

  private void verifyFlushResult(int offsets, int sentRecords) {
    FlushResult flushResult = flushResultCaptor.getValue();

    flushResult.getBufferOffsets().values().forEach(offset -> assertEquals(Long.valueOf(offsets), offset));
    var metricsByRunId = flushResult.getMetricsByRunId();
    var recordMetric = metricsByRunId.get(0L);
    assertEquals(sentRecords, recordMetric.getSentRecordsTotal().get());
  }
  private JdbcSinkConnectorConfig buildConfig(JdbcSinkConnectorConfig.InsertMode insertMode) {
    var originals = Map.of(
        SINK_ID, "1",
        INSERT_MODE, insertMode.name(),
        CREDENTIALS_TYPE, ConnectionType.SNOWFLAKE.name().toLowerCase(),
        UNIT_TEST, "true",
        PRIMARY_KEY, "id");

    return new JdbcSinkConnectorConfig(originals);
  }

  private FlushResult buildFlushResult(long offset, int sentRecords) {
    RecordMetric recordMetric = new RecordMetric();
    recordMetric.sentRecordsTotal.set(sentRecords);
    Map<Long, RecordMetric> metricsByRunId = new HashMap<>();
    metricsByRunId.put(0L, recordMetric);

    return FlushResult.builder()
        .bufferOffsets(Map.of(new TopicPartition("partition-1", 1), offset, new TopicPartition("partition-2", 2), offset))
        .metricsByRunId(metricsByRunId)
        .build();
  }
}