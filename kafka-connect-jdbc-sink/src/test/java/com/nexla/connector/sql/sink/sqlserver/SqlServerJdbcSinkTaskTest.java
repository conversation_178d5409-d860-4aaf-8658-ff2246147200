package com.nexla.connector.sql.sink.sqlserver;

import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.PDataset;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.schema.SchemaType;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.sql.sink.JdbcSinkTask;
import com.nexla.listing.client.ListingClient;
import connect.jdbc.sink.dialect.AutomaticBinding;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.connect.errors.RetriableException;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.junit.*;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.MSSQLServerContainer;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.google.common.collect.ImmutableMap.of;
import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.NexlaConstants.EXCEPTION_TRACE;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.NexlaNamingUtils.getQuarantineTopic;
import static com.nexla.common.Resource.sink;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.common.metrics.NexlaRawMetric.*;
import static com.nexla.connector.config.BaseConnectorConfig.METRICS_TOPIC;
import static com.nexla.connector.config.MappingConfig.MODE_MANUAL;
import static com.nexla.connector.config.SinkConnectorConfig.*;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.USERNAME;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.PARALLELISM;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.STOP_ON_ERROR;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static com.nexla.connector.sql.sink.MySqlJdbcSinkTaskTest.nexlaMessage;
import static com.nexla.connector.sql.sink.sqlserver.SqlServerJdbcSinkTaskTestHelper.*;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

//@Test FIXME Deactivating tests in release/v3.2.0containers
public class SqlServerJdbcSinkTaskTest extends BaseKafkaTest {

	private static final String CREDS_TYPE = "SQLSERVER";
	private static final String TABLE_NAME = "TEST" + UUID.randomUUID().toString().substring(0, 8);
	private JdbcSinkTask task;

	private static Map<String, String> properties;

	public static MSSQLServerContainer sqlServer = new MSSQLServerContainer()
		.acceptLicense();

	public static KafkaContainer kafka = new KafkaContainer("7.2.11");

	@BeforeClass
	public static void startUp() {
		sqlServer.withReuse(true);
		kafka.withReuse(true);
		sqlServer.start();
		kafka.start();
		init(kafka);
	}

	@AfterClass
	public static void tearDown() {
		sqlServer.stop();
		kafka.stop();
		// kafka producers will keep spamming - even though the kafka broker container is stopped
	}

	@Before
	public void onBefore() {
		AdminApiClient adminApiClient = mock(AdminApiClient.class);
		AdminApiClientBuilder.INSTANCE = adminApiClient;
		List<PDataset> dataSets = List.of(new PDataset(24, 32, null));
		PDataSource ds = new PDataSource();
		DataSink sink = new DataSink();
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(new Pipeline(dataSets, ds, sink)));

		when(adminApiClient.getPipeline(any(Integer.class))).thenReturn(Optional.of(
			new Pipeline(null, null, null)));
		DataSink dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.SQLSERVER);
		dataSink.setSinkConfig(Map.of(PARALLELISM, "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);
		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(dataSink));

		this.task = new JdbcSinkTask() {
			@Override
			public void doStart() {
				super.doStart();
				this.listingClient = mock(ListingClient.class);
				this.offsetsSender = Optional.of(mock(OffsetsTracker.class));
			}
		};
		task.initialize(mock(SinkTaskContext.class));
		this.properties = getProperties(sqlServer, CREDS_TYPE, TABLE_NAME);
	}

	@After
	@SneakyThrows
	public void after() {
		// flush all the producers to prevent them being stuck after test run / kafka container stop
		if (task != null
			&& task.getNexlaMessageProducer() != null
			&& task.getNexlaMessageProducer().getTransport() != null) {
			task.getNexlaMessageProducer().close();
			task.stop();
		}
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void nullPrimaryKeyTest() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));

		recreateTable(mappingConfig, sqlServer, properties);
		mappingConfig.setMode(MappingConfig.MODE_AUTO);

		properties.put(INSERT_MODE, "insert");
		properties.put(MAPPING, toJsonString(mappingConfig));
		properties.put(STOP_ON_ERROR, "true");
		task.start(properties);

		LinkedHashMap<String, Object> messageMap = Maps.newLinkedHashMap();
		messageMap.put("id", null);
		messageMap.put("description", "test");
		NexlaMessage message = createMessage(messageMap);

		try {
			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		} catch (Exception e) {
			assertEquals("java.sql.BatchUpdateException: Cannot insert the value NULL into column 'id', table 'master.dbo." + TABLE_NAME + "'; column does not allow nulls. INSERT fails.", e.getCause().getMessage());
			task.stop();
			return;
		}

		fail("Did not throw an exception on null primary key");
		task.stop();
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putInsert() {

		withConsumer((metricsTopic, metricsConsumer) -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("descriptions", map("description", DEFAULT_MAPPING, "desc", DEFAULT_MAPPING));
			mappingConfig.setTrackerMode(Tracker.TrackerMode.RECORD);
			mappingConfig.setTrackerFieldName("mytracker");

			recreateTable(mappingConfig, sqlServer, properties);

			mappingConfig.setMode(MappingConfig.MODE_AUTO);
			mappingConfig.getExcludes().add("excluded");

			properties.put(INSERT_MODE, "insert");
			properties.put(MAPPING, toJsonString(mappingConfig));
			properties.put(METRICS_TOPIC, metricsTopic);

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(
				of("ids", "1", "descriptions", "test", "excluded", "test excluded")));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));

			List<Map<String, String>> result = selectRows(sqlServer, false, newHashSet("id", "description", "desc", "mytracker"), TABLE_NAME);
			String tracker = result.get(0).get("mytracker");

			assertTrue(tracker.length() > 15);

			assertEquals(singletonList(of("id", "1", "description", "test", "desc", "test", "mytracker", tracker)), result);
			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

			assertNexlaMetric(metrics.get(0), TABLE_NAME, 694, 1, 1);

			task.stop();

		});
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putInsertCaps() {
		withConsumer((metricsTopic, metricsConsumer) -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("descriptions", map("DESCRIPTION", DEFAULT_MAPPING, "DESC", DEFAULT_MAPPING));

			recreateTable(mappingConfig, sqlServer, properties);

			properties.put(INSERT_MODE, INSERT.toString());
			properties.put(MAPPING, toJsonString(mappingConfig));
			properties.put(TRACKER_ENCRYPTION_ENABLED, "true");
			properties.put(TRACKER_ENCRYPTION_KEY, "9Rz+VE1VzxS7wvUpg8hhsbrt8vrPLUKBtDNd8N/wKbg=");
			properties.put(METRICS_TOPIC, metricsTopic);

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(
				of("ids", "1",
					"descriptions", "park")));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
			assertEquals(singletonList(of("id", "1", "DESCRIPTION", "park", "DESC", "park")),
				selectRows(sqlServer, false, newHashSet("id", "DESCRIPTION", "DESC"), TABLE_NAME));
			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

			assertNexlaMetric(metrics.get(0), TABLE_NAME, 582, 1, 1);

			task.stop();

		});
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsert() {
		properties.put(INSERT_MODE, "upsert");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));
		recreateTable(mappingConfig, sqlServer, properties);
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "test")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "description", "test")),
			selectRows(sqlServer, false, newHashSet("id", "description"), TABLE_NAME));

		message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "changed")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 2)));
		assertEquals(singletonList(of("id", "1", "description", "changed")),
			selectRows(sqlServer, false, newHashSet("id", "description"), TABLE_NAME));
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertPkCaps() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(PRIMARY_KEY, "ID");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("ID", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("DESCRIPTION", DEFAULT_MAPPING));
		recreateTable(mappingConfig, sqlServer, properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "test")));
		task.start(properties);

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "description", "test")),
			selectRows(sqlServer, false, newHashSet("id", "description"), TABLE_NAME));

		message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "changed")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 2)));
		assertEquals(singletonList(of("ID", "1", "DESCRIPTION", "changed")),
			selectRows(sqlServer, false, newHashSet("ID", "DESCRIPTION"), TABLE_NAME));
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertWithNullsDisabled() {
		properties.put(INSERT_MODE, "insert");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("flag", map("flag", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));
		recreateTable(mappingConfig, sqlServer, properties);
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", 1,
				"flag", false,
				"description", "test")));

		String strMessage = toJsonString(message);
		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, strMessage, 1)));
		assertEquals(singletonList(of("id", "1", "description", "test", "flag", "false")),
			selectRows(sqlServer, false, newHashSet("id", "flag", "description"), TABLE_NAME));

		message = createMessage(new LinkedHashMap<>(
			map("id", 1,
				"flag", false,
				"description", null)));

		strMessage = toJsonString(message);

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, strMessage, 2)));
		assertEquals(singletonList(of("id", "1", "description", "test", "flag", "false")),
			selectRows(sqlServer, false, newHashSet("id", "flag", "description"), TABLE_NAME));
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void dedupUpsert() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(PRIMARY_KEY, "id");
		task.start(properties);
		NexlaMetaData metaData = new NexlaMetaData();
		LinkedHashMap<String, Object> map1 = new LinkedHashMap<>(of("id", "1", "description", "test1"));
		LinkedHashMap<String, Object> map2 = new LinkedHashMap<>(of("id", "2", "description", "test2"));
		LinkedHashMap<String, Object> map3 = new LinkedHashMap<>(of("id", "4", "description", "test3"));
		LinkedHashMap<String, Object> map4 = new LinkedHashMap<>(of("id", "4", "description", "test4"));

		List<NexlaMessageContext> messages = asList(
			nexlaMessage(metaData, map1),
			nexlaMessage(metaData, map2),
			nexlaMessage(metaData, map3),
			nexlaMessage(metaData, map4)
		);

		List<LinkedHashMap<String, Object>> messagesExpected = asList(
			map1,
			map2,
			map4
		);

		List<LinkedHashMap<String, Object>> collect = task
			.deduplicate(StreamEx.of(messages), task.config)
			.map(x -> x.getMapped().getRawMessage())
			.collect(toList());

		assertEquals(messagesExpected, collect);
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void dedupUpsertWithCaps() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(PRIMARY_KEY, "ID,id");
		task.start(properties);

		NexlaMetaData metaData = new NexlaMetaData();
		LinkedHashMap<String, Object> map1 = new LinkedHashMap<>(of("ID", "1", "description", "test1"));
		LinkedHashMap<String, Object> map2 = new LinkedHashMap<>(of("ID", "2", "description", "test2"));
		LinkedHashMap<String, Object> map3 = new LinkedHashMap<>(of("ID", "4", "description", "test3"));
		LinkedHashMap<String, Object> map4 = new LinkedHashMap<>(of("ID", "4", "description", "test4"));
		LinkedHashMap<String, Object> map5 = new LinkedHashMap<>(of("id", "1", "description", "test5"));
		LinkedHashMap<String, Object> map6 = new LinkedHashMap<>(of("id", "2", "description", "test6"));
		LinkedHashMap<String, Object> map7 = new LinkedHashMap<>(of("id", "4", "description", "test7"));
		LinkedHashMap<String, Object> map8 = new LinkedHashMap<>(of("id", "4", "description", "test8"));

		List<LinkedHashMap<String, Object>> messagesExpected = asList(
			map1,
			map2,
			map4,
			map5,
			map6,
			map8
		);

		List<NexlaMessageContext> messages = asList(
			nexlaMessage(metaData, map1),
			nexlaMessage(metaData, map2),
			nexlaMessage(metaData, map3),
			nexlaMessage(metaData, map4),
			nexlaMessage(metaData, map5),
			nexlaMessage(metaData, map6),
			nexlaMessage(metaData, map7),
			nexlaMessage(metaData, map8)
		);


		List<LinkedHashMap<String, Object>> collect = task
			.deduplicate(StreamEx.of(messages), task.config)
			.map(x -> x.getMapped().getRawMessage())
			.collect(toList());

		assertEquals(messagesExpected, collect);
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void closedConnectionBeforeStart() {
		properties.put(INSERT_MODE, INSERT.toString());
		properties.put(USERNAME, ""); // simulate connection drop by giving incorrect username
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "park")));

		try {
			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
		} catch (RetriableException e) {
			task.stop();
		} catch (Exception e) {
			task.stop();
			fail("Wrong exception");
		}
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void quarantineRecordsTest() {
		int sinkId = new Random().nextInt(10000);
		String quarantineTopic = getQuarantineTopic(sink(sinkId), Optional.empty());

		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer(quarantineTopic, quarantineConsumer -> {

				MappingConfig mappingConfig = new MappingConfig();
				mappingConfig.setMode(MODE_MANUAL);
				mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
				mappingConfig.getMapping().put("descriptions", map("description", DEFAULT_MAPPING));

				recreateTable(mappingConfig, sqlServer, properties);

				mappingConfig.setMode(MappingConfig.MODE_AUTO);
				mappingConfig.getExcludes().add("excluded");

				properties.put(INSERT_MODE, "insert");
				properties.put(MAPPING, toJsonString(mappingConfig));
				properties.put(METRICS_TOPIC, metricsTopic);
				properties.put(SINK_ID, sinkId + "");

				task.start(properties);

				NexlaMessage message = createMessage(new LinkedHashMap<>(
					of("descriptions", "test", "excluded", "test excluded")));

				task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));

				List<NexlaQuarantineMessage> quarantine = readQuarantine(quarantineConsumer);
				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

				assertEquals(1, quarantine.size());
				assertEquals(0, metrics.get(0).getFields().get(RECORDS));
				task.stop();
			});

		});
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void noQuarantineRecordsTest() {
		int sinkId = new Random().nextInt(10000);
		String quarantineTopic = getQuarantineTopic(sink(sinkId), Optional.empty());

		withConsumer(quarantineTopic, quarantineConsumer -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("descriptions", map("description", DEFAULT_MAPPING));

			recreateTable(mappingConfig, sqlServer, properties);

			mappingConfig.setMode(MappingConfig.MODE_AUTO);
			mappingConfig.getExcludes().add("excluded");

			properties.put(INSERT_MODE, "insert");
			properties.put(MAPPING, toJsonString(mappingConfig));
			properties.put(USERNAME, "");
			properties.put(SINK_ID, sinkId + "");

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(
				of("id", "1",
					"description", "park")));

			try {
				task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
			} catch (RetriableException e) {
				List<NexlaQuarantineMessage> quarantine = readQuarantine(quarantineConsumer);
				assertEquals(0, quarantine.size());
				task.stop();
			}
		});
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void checkException() throws SQLException {

		String qualifiedName = dbDialect.getQualifiedTableName(TABLE_NAME, null, null);

		Map<String, SchemaType> typeBinding = Maps.newHashMap();
		typeBinding.put("id", SchemaType.INTEGER);
		typeBinding.put("description", SchemaType.STRING);

		String createSql = dbDialect.getCreateSql(qualifiedName, singletonList("id"), singletonList("description"),
			() -> new AutomaticBinding(typeBinding, emptyMap()), new MappingConfig(), new JdbcSinkConnectorConfig(properties));

		try (Connection connection = sqlServer.createConnection("")) {
			connection.prepareStatement(createSql).execute();
			connection.commit();
		}

		properties.put(INSERT_MODE, "insert");
		properties.put(STOP_ON_ERROR, "true");
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "jim",
				"description", "park")));

		try {
			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
			fail("Did not throw exception");
		} catch (Exception e) {
			assertTrue(e.getCause().getMessage().contains("expected column \"id\" to be of format \"int32\""));
		}
	}

	private void assertNexlaMetric(NexlaRawMetric metric, String tableName, Integer size, Integer records, Integer sinkId) {
		Map<String, Object> expectedMetrics = new HashMap<>(
			of("Path", tableName,
				"Size", size,
				"Records", records,
				"Resource ID", sinkId,
				"Resource Type", SINK)
		);
		expectedMetrics.put("Error Message", null);
		expectedMetrics.put("Hash", null);

		Map<String, Object> actualMetrics = new HashMap<>(
			of("Path", metric.getTags().get(NAME),
				"Size", metric.getFields().get(SIZE),
				"Records", metric.getFields().get(RECORDS),
				"Resource ID", metric.getResourceId(),
				"Resource Type", metric.getResourceType())
		);
		actualMetrics.put("Error Message", metric.getFields().get(EXCEPTION_TRACE));
		actualMetrics.put("Hash", metric.getFields().get(HASH));

		assertEquals(expectedMetrics, actualMetrics);
	}
}
