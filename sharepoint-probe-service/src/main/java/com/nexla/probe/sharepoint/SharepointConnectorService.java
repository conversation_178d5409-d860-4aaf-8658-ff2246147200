package com.nexla.probe.sharepoint;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.api.client.util.Lists;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.microsoft.graph.core.tasks.PageIterator;
import com.microsoft.graph.models.*;
import com.microsoft.graph.models.odataerrors.ODataError;
import com.microsoft.graph.serviceclient.GraphServiceClient;
import com.microsoft.kiota.RequestInformation;
import com.microsoft.kiota.serialization.*;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.*;
import com.nexla.common.io.CloseableInputStream;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.sharepoint.SharepointAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.listing.client.ListingClient;
import lombok.Builder;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpStatus;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jayway.jsonpath.JsonPath;

import java.io.*;
import java.io.File;
import java.net.URI;
import java.nio.file.Files;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nexla.common.ListingResourceType.FILE;
import static com.nexla.common.probe.ProbeControllerConstants.*;
import static com.nexla.connector.config.file.DirScanningMode.FILES;
import static java.util.Objects.nonNull;

public class SharepointConnectorService extends FileConnectorService<BaseAuthConfig> {

    static final Cache<String, String> ID_TO_NAMES = CacheBuilder.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(5))
            .maximumSize(100)
            .build();

    static final Cache<String, String> ID_TO_PERMISSIONS = CacheBuilder.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(5))
            .maximumSize(100)
            .build();

    private static final Logger log = LoggerFactory.getLogger(SharepointConnectorService.class);

    private static final String ROOT = "root";

    private static final String SITE = "Sites";

    private static final String SLASH = "/";

    private static final String INVALID_PATH_MESSAGE = "The specified `path` is invalid! Path must follow the pattern Sites/{site_id}/{driveId}/{itemId}...";
    public static final String METADATA_WEBURL = "web_url";
    public static final String METADATA_ACCESS = "access";
    public static final String ADD_PERMISSIONS_TO_METADATA = "add.permissions.to.metadata";
    private static final String EMPTY_PERMISSIONS = JsonUtils.toJsonString(Map.of("value", List.of()));

    public SharepointConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
        super(adminApiClient, listingClient, credentialsDecryptKey);
    }

    @Override
    public InputStream readInputStreamInternal(FileConnectorAuth config, String file) {
        return withRetriableTokenRefresh(config.getAuthConfig().getCredsId(), client -> readInputStream(file, config, client));
    }

    @SneakyThrows
    InputStream readInputStream(String file, FileConnectorAuth config, GraphServiceClient client) {
        File tempFile = Files.createTempFile("", "").toFile();
        String configPath = config.getPath();
        PathSearch pathSearch = PathSearch.init(getFullPath(configPath, file));
        OutputStream outputStream = new FileOutputStream(tempFile);

        long bytesTransferred = client.drives()
                .byDriveId(pathSearch.driveId)
                .items()
                .byDriveItemId(pathSearch.itemId)
                .content()
                .get()
                .transferTo(outputStream);

        log.info("M=readInputStreamInternal, path={}, file={}, bytesTransferred={}", configPath, file, bytesTransferred);

        return new CloseableInputStream(new FileInputStream(tempFile)).onClose(tempFile::delete);
    }

    @Override
    public FileDetails writeInternal(FileConnectorAuth config, String key, File file) {
        throw new NotImplementedException();
    }

    @Override
    public FileDetails writeInternal(FileConnectorAuth config, String key, InputStream inputStream) {
        throw new NotImplementedException();
    }

    @Override
    public boolean doesFileExistsInternal(FileConnectorAuth config, String key) {
        String path = config.getPath();
        Integer credentialId = config.getAuthConfig().getCredsId();
        PathSearch pathSearch = PathSearch.init(getFullPath(path, key));
        try {
            return withRetriableTokenRefresh(credentialId, client -> {
                DriveItem driveItem = client.drives()
                        .byDriveId(pathSearch.driveId)
                        .items()
                        .byDriveItemId(pathSearch.itemId)
                        .get();

                return nonNull(driveItem);
            });
        } catch (ODataError e) {
            log.warn("M=doesFileExistsInternal, error fetching file with path={} and key={} for credentialId={}, status={}",
                    path,
                    key,
                    credentialId,
                    e.getResponseStatusCode());
            if (HttpStatus.SC_NOT_FOUND == e.getResponseStatusCode()) {
                return false;
            }
            throw e;
        }
    }

    @Override
    public AuthResponse authenticate(BaseAuthConfig authConfig) {
        try {
            return withRetriableTokenRefresh(authConfig.getCredsId(), client -> {
                List<Site> sites = getAllSites(client, true, toSharepointAuthCfg(authConfig));

                if (sites == null || sites.isEmpty()) {
                    log.error("M=authenticate, no sites found, credentialId=" + authConfig.getCredsId());
                    return AuthResponse.authError(new RuntimeException("No sites found"));
                }

                return AuthResponse.SUCCESS;
            });
        } catch (Exception e) {
            log.error("M=authenticate, authError, credentialId=" + authConfig.getCredsId(), e);
            return AuthResponse.authError(e);
        }
    }

    @Override
    public StreamEx<NexlaBucket> listBuckets(AbstractConfig abstractConfig) {
        return StreamEx.empty();
    }

    /**
     * This method is called by probe listing app list files, that's why we need to remove treeify path, so we don't
     * insert extra metadata info in listing dd
     */
    @Override
    public StreamEx<NexlaFile> listBucketContents(AbstractConfig abstractConfig) {
        FileSourceConnectorConfig config = (FileSourceConnectorConfig) abstractConfig;
        StreamEx<NexlaFile> nexlaFiles = listTopLevelBuckets(config)
                .peek(x -> x.getMetadata().ifPresent(meta -> meta.remove(TREEIFY_PATH)));

        if (config.dirScanningMode == FILES) {
            return nexlaFiles.filter(file -> file.getType() == FILE);
        }
        return nexlaFiles;
    }

    @Override
    public boolean checkWriteAccess(AbstractConfig abstractConfig) {
        throw new NotImplementedException();
    }

    /**
     * This method is called by probe listTree
     */
    @Override
    public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig abstractConfig) {
        FileSourceConnectorConfig config = (FileSourceConnectorConfig) abstractConfig;
        BaseAuthConfig authConfig = config.getAuthConfig();
        Integer credentialId = authConfig.getCredsId();
        boolean addPermissionsToMetadata =
            Optional.ofNullable(abstractConfig.originals().get(ADD_PERMISSIONS_TO_METADATA))
                    .map(x -> Boolean.valueOf(x.toString()))
                    .orElse(false);

        return withRetriableTokenRefresh(credentialId, client -> {
            String path = Optional.ofNullable(config.getPath())
                    .filter(StringUtils::isNotBlank)
                    .filter(it -> !it.startsWith("/"))
                    .orElse(SITE);
            PathSearch pathSearch = PathSearch.init(path);
            Integer depth = Optional.ofNullable(config.depth).orElse(1);
            switch (pathSearch.type) {
                case ITEM_CHILDREN:
                    return listDriveItemChildren(client, pathSearch, depth, addPermissionsToMetadata);
                case ITEM:
                    return listDriveItems(client, pathSearch, depth, addPermissionsToMetadata);
                case DRIVE:
                    return listDrives(client, pathSearch, depth, addPermissionsToMetadata);
                default:
                    if (authConfig instanceof SharepointAuthConfig) {
                        return listSites(client, depth, (SharepointAuthConfig) authConfig, addPermissionsToMetadata);
                    } else {
                        SharepointAuthConfig attemptToCast = toSharepointAuthCfg(authConfig);
                        return listSites(client, depth, attemptToCast, addPermissionsToMetadata);
                    }
            }
        });
    }

    private SharepointAuthConfig toSharepointAuthCfg(BaseAuthConfig authConfig) {
        return new SharepointAuthConfig(authConfig.originals(), authConfig.getCredsId());
    }

    private StreamEx<NexlaFile> listDriveItemChildren(GraphServiceClient graphServiceClient, PathSearch pathSearch, Integer depth, Boolean addPermissionsToMetadata) {
        log.info("M=listDriveItemChildren, fullPath={}, depth={}", pathSearch.fullPath, depth);
        DriveItemCollectionResponse driveItemCollectionResponse = graphServiceClient.drives()
                .byDriveId(pathSearch.driveId)
                .items()
                .byDriveItemId(pathSearch.itemId)
                .children()
                .get();

        List<DriveItem> paginatedResults = getPaginatedResults(graphServiceClient,
                driveItemCollectionResponse,
                DriveItemCollectionResponse::createFromDiscriminatorValue);

        List<NexlaFile> nexlaFiles = paginatedResults
                .stream()
                .map(item -> {
                    String itemId = item.getId();
                    boolean isFile = isFile(item);
                    String webUrl = item.getWebUrl();
                    ListingResourceType type = isFile ? FILE : ListingResourceType.FOLDER;

                    Pair<String, String> treeAndDisplayPath = buildTreeAndDisplayPath(itemId, item.getName(), pathSearch, graphServiceClient);

                    cacheName(itemId, item.getName());

                    String permissions = null;
                    if (addPermissionsToMetadata) {
                        permissions = getItemPermissions(itemId, pathSearch.driveId, graphServiceClient);
                        cachePermission(itemId, permissions);
                    }

                    return createNexlaFile(
                            pathSearch.getFullPathExcludingOriginalPath(itemId),
                            itemId,
                            type,
                            item.getSize(),
                            item.getCreatedDateTime(),
                            item.getLastModifiedDateTime(),
                            treeAndDisplayPath.getLeft(),
                            treeAndDisplayPath.getRight(),
                            getDriveItemHash(item),
                            isFile ? webUrl : null,
                            permissions
                    );
                })
                .collect(Collectors.toList());

        if (depth > 1) {
            List<NexlaFile> items = nexlaFiles
                    .stream()
                    .filter(nexlaFile -> ListingResourceType.FOLDER == nexlaFile.getType())
                    .flatMap(nexlaFile -> {
                        PathSearch newPathSearch = PathSearch.init(pathSearch.fullPath + SLASH + getCurrentIdFromMetadata(nexlaFile), pathSearch.originalPathConfig);
                        return listDriveItemChildren(graphServiceClient, newPathSearch, depth - 1, addPermissionsToMetadata);
                    })
                    .collect(Collectors.toList());

            nexlaFiles.addAll(items);
        }

        return StreamEx.of(nexlaFiles);
    }

    private StreamEx<NexlaFile> listDrives(GraphServiceClient graphServiceClient, PathSearch pathSearch, Integer depth, Boolean addPermissionsToMetadata) {
        log.info("M=listDrives, fullPath={}, depth={}", pathSearch.fullPath, depth);
        DriveCollectionResponse driveCollectionResponse = graphServiceClient
                .sites()
                .bySiteId(pathSearch.siteId)
                .drives()
                .get();

        List<Drive> paginatedResults = getPaginatedResults(graphServiceClient,
                driveCollectionResponse,
                DriveCollectionResponse::createFromDiscriminatorValue);

        List<NexlaFile> nexlaFiles = paginatedResults
                .stream()
                .map(drive -> {
                    String driveId = drive.getId();
                    Pair<String, String> treeAndDisplayPath = buildTreeAndDisplayPath(driveId, drive.getName(), pathSearch, graphServiceClient);
                    cacheName(driveId, drive.getName());

                    return createNexlaFile(
                            pathSearch.getFullPathExcludingOriginalPath(driveId),
                            driveId,
                            ListingResourceType.FOLDER,
                            null,
                            drive.getCreatedDateTime(),
                            drive.getLastModifiedDateTime(),
                            treeAndDisplayPath.getLeft(),
                            treeAndDisplayPath.getRight(),
                            null,
                            null,
                            null
                    );
                })
                .collect(Collectors.toList());

        if (depth > 1) {
            List<NexlaFile> drives = nexlaFiles
                    .stream()
                    .filter(nexlaFile -> ListingResourceType.FOLDER == nexlaFile.getType())
                    .flatMap(nexlaFile -> {
                        PathSearch newPathSearch = PathSearch.init(pathSearch.fullPath + SLASH + getCurrentIdFromMetadata(nexlaFile), pathSearch.originalPathConfig);
                        return listDriveItems(graphServiceClient, newPathSearch, depth - 1, addPermissionsToMetadata);
                    })
                    .collect(Collectors.toList());

            nexlaFiles.addAll(drives);
        }

        return StreamEx.of(nexlaFiles);
    }

    private StreamEx<NexlaFile> listDriveItems(GraphServiceClient graphServiceClient, PathSearch pathSearch, Integer depth, Boolean addPermissionsToMetadata) {
        log.info("M=listDriveItems, fullPath={}, depth={}", pathSearch.fullPath, depth);

        List<NexlaFile> nexlaFiles = getDriveChildren(graphServiceClient, pathSearch.driveId)
                .stream()
                .map(item -> {
                    String itemId = item.getId();
                    boolean isFile = isFile(item);
                    String webUrl = item.getWebUrl();
                    ListingResourceType type = isFile ? FILE : ListingResourceType.FOLDER;
                    Pair<String, String> treeAndDisplayPath = buildTreeAndDisplayPath(itemId, item.getName(), pathSearch, graphServiceClient);
                    cacheName(itemId, item.getName());

                    String permissions = null;
                    if (addPermissionsToMetadata) {
                        permissions = getItemPermissions(itemId, pathSearch.driveId, graphServiceClient);
                        cachePermission(itemId, permissions);
                    }

                    return createNexlaFile(
                            pathSearch.getFullPathExcludingOriginalPath(itemId),
                            itemId,
                            type,
                            item.getSize(),
                            item.getCreatedDateTime(),
                            item.getLastModifiedDateTime(),
                            treeAndDisplayPath.getLeft(),
                            treeAndDisplayPath.getRight(),
                            getDriveItemHash(item),
                            isFile ? webUrl : null,
                            permissions
                    );
                })
                .collect(Collectors.toList());


        if (depth > 1) {
            List<NexlaFile> items = nexlaFiles
                    .stream()
                    .filter(nexlaFile -> ListingResourceType.FOLDER == nexlaFile.getType())
                    .flatMap(nexlaFile -> {
                        PathSearch newPathSearch = PathSearch.init(pathSearch.fullPath + SLASH + getCurrentIdFromMetadata(nexlaFile), pathSearch.originalPathConfig);
                        return listDriveItemChildren(graphServiceClient, newPathSearch, depth - 1, addPermissionsToMetadata);
                    })
                    .collect(Collectors.toList());

            nexlaFiles.addAll(items);
        }

        return StreamEx.of(nexlaFiles);
    }

    private NexlaFile createNexlaFile(
            String fullPath,
            String id,
            ListingResourceType type,
            Long size,
            OffsetDateTime createDateTime,
            OffsetDateTime lastModifiedDateTime,
            String treeifyPath,
            String displayPath,
            String hash,
            String webUrl,
            Object permissions) {

        NexlaFile nexlaFile = new NexlaFile();
        nexlaFile.setFullPath(fullPath);
        nexlaFile.setType(type);
        nexlaFile.setCreatedAt(extractTimeMillis(createDateTime));
        nexlaFile.setLastModified(extractTimeMillis(lastModifiedDateTime));
        nexlaFile.setSize(size);
        nexlaFile.setMd5(hash);

        Map<String, Object> metadata = Maps.newHashMap();
        metadata.put(ID, id);
        metadata.put(TREEIFY_PATH, treeifyPath);
        metadata.put(DISPLAY_PATH, displayPath);
        metadata.put(MIME_TYPE, nexlaFile.getType());
        if (webUrl != null) {
            metadata.put(METADATA_WEBURL, webUrl);
        }
        if (permissions != null) {
            metadata.put(METADATA_ACCESS, permissions);
        }

        nexlaFile.setMetadata(Optional.of(metadata));

        return nexlaFile;
    }

    private String getFullPath(String path, String key) {
        var keyPath = key.startsWith(SLASH) ? key : SLASH + key;
        if (path.endsWith(SLASH)) {
            return path.substring(0, path.length() - 1) + keyPath;
        }
        return path + keyPath;
    }

    @SneakyThrows
    private List<Site> getAllSites(GraphServiceClient client, boolean auth, SharepointAuthConfig authConfig) {

        if (authConfig.getSiteNames() != null) {
            return getConfiguredSites(client, authConfig);
        }

        RequestInformation requestInformation = client.sites().toGetRequestInformation();
        String urlTemplate = String.valueOf(requestInformation.getUri());
        if (authConfig.getQueryParameter() != null & !authConfig.getQueryParameter().isEmpty()) {
            urlTemplate = urlTemplate + "?" + authConfig.getQueryParameter();
        }
        requestInformation.setUri(URI.create(urlTemplate));
        HashMap<String, ParsableFactory<? extends Parsable>> errorMapping = new HashMap<>();
        errorMapping.put("XXX", ODataError::createFromDiscriminatorValue);
        SiteCollectionResponse siteCollectionResponse = client.getRequestAdapter().send(requestInformation,
                errorMapping,
                SiteCollectionResponse::createFromDiscriminatorValue);

        return auth ? siteCollectionResponse.getValue()
                : getPaginatedResults(client, siteCollectionResponse, SiteCollectionResponse::createFromDiscriminatorValue);
    }


    private List<Site> getConfiguredSites(GraphServiceClient client, SharepointAuthConfig config) {
        List<Site> sites = new ArrayList<>();
        String hostname = config.getHostname();
        for (String name : config.getSiteNames()) {
            if (name == null || name.isBlank()) {
                continue;
            }

            name = name.trim();

            if (name.startsWith(SLASH)) {
                name = name.substring(1);
            }

            if (name.toLowerCase().startsWith((SITE + SLASH).toLowerCase())) {
                name = name.substring((SITE + SLASH).length());
            }

            name = SLASH + SITE + SLASH + name;
            if (hostname == null) {
                Site rootSite = queryRootSite(client);
                String id = rootSite == null ? null : rootSite.getId();

                if (id != null) {
                    String[] ids = id.split(",");
                    if (ids.length > 0) {
                        hostname = ids[0];
                    }
                } else {
                    log.error("M=getConfiguredSites, root site is null");
                    return Collections.emptyList();
                }
            }

            String siteIdentifier = hostname + ":" + name;
            try {
                Site site = client.sites().bySiteId(siteIdentifier).get();
                if (site != null) {
                    sites.add(site);
                } else {
                    log.error("M=getConfiguredSites, site is null");
                }
            } catch (Exception e) {
                log.error("M=getConfiguredSites, error fetching site with id={}", siteIdentifier, e);
            }
        }

        return sites;
    }

    private Site queryRootSite(GraphServiceClient client) {
        return client.sites().bySiteId(ROOT).get();
    }

    @SneakyThrows
    private List<DriveItem> getDriveChildren(GraphServiceClient client, String driveId) {
        RequestInformation requestInformation = client.drives().byDriveId(driveId).root().toGetRequestInformation();
        String urlTemplate = requestInformation.getUri() + "/children";
        requestInformation.setUri(URI.create(urlTemplate));
        HashMap<String, ParsableFactory<? extends Parsable>> errorMapping = new HashMap<>();
        errorMapping.put("XXX", ODataError::createFromDiscriminatorValue);
        DriveItemCollectionResponse driveItemCollectionResponse = client.getRequestAdapter().send(requestInformation,
                errorMapping,
                DriveItemCollectionResponse::createFromDiscriminatorValue);

        return getPaginatedResults(client, driveItemCollectionResponse, DriveItemCollectionResponse::createFromDiscriminatorValue);
    }

    private StreamEx<NexlaFile> listSites(GraphServiceClient graphServiceClient, Integer depth, SharepointAuthConfig authConfig, Boolean addPermissionsToMetadata) {
        log.info("M=listSites, depth={}", depth);
        List<NexlaFile> nexlaFiles = getAllSites(graphServiceClient, false, authConfig)
                .stream()
                .filter(site -> nonNull(site.getDisplayName()) || nonNull(site.getName()))
                .map(site -> {
                    String siteId = site.getId();
                    String siteName = getSiteDisplayName(site);
                    cacheName(siteId, siteName);

                    return createNexlaFile(
                            SITE + SLASH + site.getId(),
                            siteId,
                            ListingResourceType.FOLDER,
                            null,
                            site.getCreatedDateTime(),
                            site.getLastModifiedDateTime(),
                            SITE + SLASH + site.getId(),
                            SITE + SLASH + siteName,
                            null,
                            null,
                            null
                    );
                })
                .collect(Collectors.toList());

        if (depth > 1) {
            List<NexlaFile> drives = nexlaFiles
                    .stream()
                    .flatMap(nexlaFile -> listDrives(graphServiceClient, PathSearch.init(SITE + SLASH + getCurrentIdFromMetadata(nexlaFile), SITE), depth - 1, addPermissionsToMetadata))
                    .collect(Collectors.toList());

            nexlaFiles.addAll(drives);
        }

        return StreamEx.of(createSitesFolder()).append(nexlaFiles);
    }

    private String getCurrentIdFromMetadata(NexlaFile nexlaFile) {
        return nexlaFile.getMetadata().get().get(ID).toString();
    }

    private Long extractTimeMillis(OffsetDateTime dateTime) {
        return Optional.ofNullable(dateTime)
                .map(it -> it.toInstant().toEpochMilli())
                .orElse(null);
    }

    private boolean isFile(DriveItem item) {
        return nonNull(item.getFile());
    }

    private NexlaFile createSitesFolder() {
        return createNexlaFile(SITE,
                SITE,
                ListingResourceType.FOLDER,
                null,
                null,
                null,
                SITE,
                SITE,
                null,
                null,
                null);
    }

    private String getDriveItemHash(DriveItem driveItem) {
        return Optional.ofNullable(driveItem.getFile())
                .map(com.microsoft.graph.models.File::getHashes)
                .map(Hashes::getQuickXorHash)
                .orElse(null);
    }

    private <T, U> U withRetriableTokenRefresh(Integer credentialId, Function<GraphServiceClient, U> consumer) {
        try {
            GraphServiceClient client = GraphClientFactory.createClient(adminApiClient, credentialsDecryptKey, credentialId);
            return consumer.apply(client);
        } catch (ODataError e) {
            if (HttpStatus.SC_UNAUTHORIZED == e.getResponseStatusCode()) {
                log.warn("M=withRetriableTokenRefresh, token is expired, refreshing token for credentialId={}", credentialId);
                listingClient.refreshRestToken(credentialId);
                adminApiClient.invalidate(credentialId, Optional.empty(), ResourceType.CREDENTIALS);
                GraphServiceClient client = GraphClientFactory.createClient(adminApiClient, credentialsDecryptKey, credentialId);
                return consumer.apply(client);
            }
            throw e;
        }
    }

    private void cacheName(String id, String name) {
        ID_TO_NAMES.put(id, name);
    }

    private void cachePermission(String id, String permission) {
        ID_TO_PERMISSIONS.put(id, permission);
    }

    private String getSiteDisplayName(Site site) {
        return ObjectUtils.firstNonNull(site.getDisplayName(), site.getName());
    }

    private String getSiteName(String siteId, GraphServiceClient client) {
        Function<String, String> function = id -> Optional.ofNullable(client.sites().bySiteId(id).get())
                .map(this::getSiteDisplayName)
                .orElse("No name found");

        return ID_TO_NAMES.asMap().computeIfAbsent(siteId, function);
    }

    private String getDriveName(String driveId, GraphServiceClient client) {
        Function<String, String> function = id -> Optional.ofNullable(client.drives().byDriveId(id).get())
                .map(Drive::getName)
                .orElse("No name found");

        return ID_TO_NAMES.asMap().computeIfAbsent(driveId, function);
    }

    private String getItemName(String itemId, String driveId, GraphServiceClient client) {
        Function<String, String> function = id -> Optional.ofNullable(client.drives().byDriveId(driveId).items().byDriveItemId(itemId).get())
                .map(DriveItem::getName)
                .orElse("No name found");

        return ID_TO_NAMES.asMap().computeIfAbsent(itemId, function);
    }

    private String getItemPermissions(String itemId, String driveId, GraphServiceClient client) {
        try {
            Function<String, String> function = id -> Optional.ofNullable(client.drives().byDriveId(driveId).items().byDriveItemId(itemId).permissions().get())
                    .map(PermissionCollectionResponse::getValue)
                    .map(this::permissionsToJson)
                    .orElse(EMPTY_PERMISSIONS);

            return ID_TO_PERMISSIONS.asMap().computeIfAbsent(itemId, function);
        } catch (ODataError e) {
            log.warn("M=getItemPermissions, error fetching permissions for id {}. Error {} with code {}", itemId, e.getMessage(), e.getResponseStatusCode());
            return EMPTY_PERMISSIONS;
        }
    }

    private String permissionsToJson(List<Permission> permissions) {
        try {
            var initialJsonString = KiotaJsonSerialization.serializeAsString(permissions);
            // Remove auxiliar API fields from the json
            var filetedJsonString = JsonPath.parse(initialJsonString).delete("$..['@odata.type']").jsonString();
            var valueMap = JsonUtils.jsonToList(filetedJsonString);
            return JsonUtils.toJsonString(Map.of("value", valueMap));
        } catch (IOException ex) {
            log.info("Can't extract permission for item", ex);
            return EMPTY_PERMISSIONS;
        }
    }

    private Pair<String, String> buildTreeAndDisplayPath(String appendToPathId, String appendToPathName, PathSearch pathSearch, GraphServiceClient client) {
        List<String> tree = Lists.newArrayList();
        List<String> display = Lists.newArrayList();

        if (nonNull(pathSearch.siteId)) {
            tree.add(SITE);
            display.add(SITE);
            tree.add(pathSearch.siteId);
            display.add(getSiteName(pathSearch.siteId, client));
        }

        if (nonNull(pathSearch.driveId)) {
            tree.add(pathSearch.driveId);
            display.add(getDriveName(pathSearch.driveId, client));
        }

        if (nonNull(pathSearch.itemPath)) {
            pathSearch.itemPath.forEach(itemId -> {
                tree.add(itemId);
                display.add(getItemName(itemId, pathSearch.driveId, client));
            });
        }

        tree.add(appendToPathId);
        display.add(appendToPathName);

        String treePath = String.join(SLASH, tree);
        String displayPath = String.join(SLASH, display);

        return Pair.of(treePath, displayPath);
    }

    @SneakyThrows
    private <T extends Parsable, R extends BaseCollectionPaginationCountResponse> List<T> getPaginatedResults(
            GraphServiceClient client,
            R responseObject,
            ParsableFactory<R> collectionPageFactory){

        List<T> items = new ArrayList<>();

        PageIterator<T, R> iterator = new PageIterator.Builder<T, R>()
                .client(client)
                .collectionPage(Objects.requireNonNull(responseObject))
                .collectionPageFactory(collectionPageFactory)
                .processPageItemCallback(response -> {
                    items.add(response);
                    return true;
                }).build();

        iterator.iterate();

        return items;
    }

    @Builder
    private static class PathSearch {

        enum PathSearchType {
            SITE, DRIVE, ITEM, ITEM_CHILDREN
        }

        private PathSearchType type;

        private String siteId;

        private String driveId;

        private String itemId;

        private List<String> itemPath;

        private String fullPath;

        private String originalPathConfig;

        static PathSearch init(String path) {
            if (!path.toLowerCase().startsWith(SITE.toLowerCase())) {
                throw new IllegalArgumentException(INVALID_PATH_MESSAGE);
            }
            return PathSearch.init(path, path);
        }

        static PathSearch init(String currentPath, String originalPath) {
            String[] splitPath = currentPath.split(SLASH);
            String fullPath = String.join(SLASH, splitPath);

            if (splitPath.length >= 4) {
                List<String> itemPath = List.of(splitPath).subList(3, splitPath.length);

                return PathSearch.builder()
                        .type(PathSearchType.ITEM_CHILDREN)
                        .siteId(getSiteId(splitPath))
                        .driveId(getDriveId(splitPath))
                        .itemId(getItemId(splitPath))
                        .itemPath(itemPath)
                        .fullPath(fullPath)
                        .originalPathConfig(originalPath)
                        .build();
            } else if (splitPath.length == 3) {
                return PathSearch.builder()
                        .type(PathSearchType.ITEM)
                        .siteId(getSiteId(splitPath))
                        .driveId(getDriveId(splitPath))
                        .fullPath(fullPath)
                        .originalPathConfig(originalPath)
                        .build();
            } else if (splitPath.length == 2) {
                return PathSearch.builder()
                        .type(PathSearchType.DRIVE)
                        .siteId(getSiteId(splitPath))
                        .fullPath(fullPath)
                        .originalPathConfig(originalPath)
                        .build();
            } else {
                return PathSearch.builder()
                        .type(PathSearchType.SITE)
                        .originalPathConfig(originalPath)
                        .build();
            }

        }

        public String getFullPathExcludingOriginalPath(String pathId) {
            return fullPath.replace(originalPathConfig, "") + SLASH + pathId;
        }

        private static String getSiteId(String[] splitPath) {
            return splitPath[1];
        }

        private static String getDriveId(String[] splitPath) {
            return splitPath[2];
        }

        private static String getItemId(String[] splitPath) {
            return splitPath[splitPath.length - 1];
        }
    }
}
