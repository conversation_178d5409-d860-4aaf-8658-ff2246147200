package com.nexla.connector.rest.sink;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.oauth2.RefreshingTokenProvider;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.exception.NexlaError;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.parse.ParserConfigs;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.pool.SimplePool;
import com.nexla.common.transform.Flattener;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.DetailedFlowInsightsSender;
import com.nexla.connect.common.HeartbeatPacerKey;
import com.nexla.connect.common.NexlaConnectorUtils;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.FilterMessageTraverse;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.connector.config.rest.RestSinkConnectorConfig;
import com.nexla.probe.http.*;
import com.nexla.rest.encode.CredentialSubstitutionMapper;
import com.nexla.sinkcaller.BatchRequestReadyIngestion;
import com.nexla.sinkcaller.HttpResponseErrorState;
import com.nexla.sinkcaller.HttpResponseState;
import com.nexla.sinkcaller.SingleRequestReadyIngestion;
import com.nexla.sinkcaller.RestIngestionCallback;
import com.nexla.sinkcaller.RestSinkCaller;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;
import org.springframework.web.client.RestClientResponseException;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.nexla.admin.client.oauth2.NexlaTokenProvider.sameToken;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaMetaData.NX_REQUEST_URL;
import static com.nexla.common.NexlaMetaData.NX_RESPONSE_HEADERS;
import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.NotificationUtils.notifyErrorMessage;
import static com.nexla.common.Resource.sink;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.common.time.VarUtils.getSubstitutionDates;
import static com.nexla.common.time.VarUtils.replaceVars;
import static com.nexla.probe.http.BaseRequestSender.createSender;
import static com.nexla.probe.http.BaseRequestSender.createSenderPool;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonList;
import static java.util.Optional.*;
import static java.util.stream.Collectors.toList;

public class RestSinkTask extends BaseSinkTask<RestSinkConnectorConfig> {

	private RestConnectorService probeService;
	private NexlaPool<RequestSender> senderPool;
	private Optional<NexlaPool<RequestSender>> ingestSenderPool;
	private ForkJoinPool executorService;
	private RestSinkCaller caller;
	private Optional<FilterMessageTraverse> filterMessage;

	private ScriptEvalClient scriptEvalClient;
	private Optional<DetailedFlowInsightsSender> flowInsightsSender;

	@Override
	public void doStart() {
		this.sinkTelemetryReporter =
				Optional.of(new ConnectorTelemetryReporter(config.sinkId, ConnectionType.REST.name(), SINK, isDedicatedNode));

		this.scriptEvalClient = new ScriptEvalClient(config.probeAppUrl, config.nexlaUsername, config.nexlaPassword, restTemplate);
		this.probeService = new RestConnectorService(scriptEvalClient);
		this.probeService.initLogger(SINK, config.sinkId, taskId);

		RefreshingTokenProvider tokenProvider = new RefreshingTokenProvider(adminApiClient, config.decryptKey);
		this.flowInsightsSender = Optional.ofNullable(DetailedFlowInsightsSender.from(
				config.logVerbose, getSuccessFlowInsightsSender(), getErrorFlowInsightsSender(), config.detailedFlowInsightsAbbreviatedLength));

		this.senderPool = createSenderPool(config.requestParallelism,
			// XXX: always does URL encoding
			() -> createSender(config.authConfig, tokenProvider, scriptEvalClient, config.logVerbose, false, flowInsightsSender)
				.withLoggerPrefix(logger.getPrefix(), "[request]"));

		this.ingestSenderPool =
			config.callerConfig.ingestUrl
				.map(url ->
					createSenderPool(
						config.requestParallelism,
						// XXX: always does URL encoding
						() -> createSender(config.ingestAuthConfig, sameToken(), scriptEvalClient, config.logVerbose, false, flowInsightsSender)
							.withLoggerPrefix(logger.getPrefix(), "[request]")));

		this.executorService = (ForkJoinPool) Executors.newWorkStealingPool(config.requestParallelism);
		
		if (this.nexlaMessageProducer == null) {
			this.nexlaMessageProducer = messageProducer(NexlaSslContext.newBuilder().build());
		}

		open(context.assignment());
		this.filterMessage = config.ignoreNullsAndEmptyStrings ? Optional.of(new FilterMessageTraverse((k, v) -> v != null && !"".equals(v))) : empty();
		this.caller = createSinkCaller();
	}

    @Override
    protected ConfigDef configDef() {
        return RestSinkConnectorConfig.configDef();
    }

    @Override
	protected RestSinkConnectorConfig parseConfig(Map<String, String> props) {
		return new RestSinkConnectorConfig(props);
	}

	@SneakyThrows
	@Override
	protected void doPut(StreamEx<NexlaMessageContext> allMessages, int unused) {
		ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(
			() -> {
				try {
					heartbeatSender.get(new HeartbeatPacerKey(runId, config.sinkId, true));
				} catch (ExecutionException e) {
					logger.warn("Error during heartbeat process.", e);
				}
			}, 0, 1, TimeUnit.MINUTES);
		try {
			Lists.partition(allMessages.collect(toList()), config.batchSize)
				.forEach(messages -> doPutBatch(messages.size(), caller, StreamEx.of(messages), false));
		} finally {
			heartbeat.cancel(true);
		}
	}

	public List<HttpResponseState> doPutBatch(int streamSize, RestSinkCaller caller, StreamEx<NexlaMessageContext> messages, boolean isProbe) {
		ConcurrentHashMap<com.nexla.common.sink.TopicPartition, Long> offsets = new ConcurrentHashMap<>();
		StreamEx<NexlaMessageContext> messageWithTrackedOffsets =
			messages.peek(m -> {
				if (!config.fastMode) {
					offsets.merge(m.topicPartition, m.kafkaOffset, Math::max);
				}
			});

		List<HttpResponseState> results = doCalls(streamSize, caller, messageWithTrackedOffsets, isProbe);

		offsetsSender.ifPresent(os -> {
			os.updateSinkOffsets(config.sinkId, offsets);
			logger.info("Updated offsets: {}", offsets);
		});

		AtomicLong sentRecordsTotal = new AtomicLong();
		AtomicLong sentBytesTotal = new AtomicLong();

		sentRecordsTotal.addAndGet(results.stream().mapToLong(HttpResponseState::getSentRecords).sum());
		sentBytesTotal.addAndGet(results.stream().mapToLong(HttpResponseState::getSentBytes).sum());

		List<HttpResponseState> callsWithErrors = results.stream()
			.filter(restResponseState -> !restResponseState.getErrorList().isEmpty())
			.collect(toList());

		callsWithErrors.forEach(state -> {
			List<Throwable> deduplicatedThrowableList =
				new ArrayList<>(new LinkedHashSet<>(state.getErrorList().stream()
					.map(HttpResponseErrorState::getThrowable).collect(toList())));
			for (Throwable throwable : deduplicatedThrowableList) {
				logException(throwable, state.getUrl());
				NexlaConnectorUtils.sendNexlaNotificationEvent(
					nexlaMessageProducer, ERROR, runId, SINK, config.sinkId, state.getUrl(), 0l,
					notifyErrorMessage(getErrorDetails(throwable, of(state.getErrorRecords()))));
				publishMonitoringLog(throwable.getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
			}

			state.getErrorList().forEach(error -> nexlaMessageProducer.sendQuarantineMessage(
				sink(config.sinkId),
				error.getOriginalMessage().getRawMessage(),
				error.getOriginalMessage().getNexlaMetaData(),
				error.getThrowable(),
				state.getUrl()
			));
		});

		String calledUrl = of(results)
			.filter(x -> !x.isEmpty())
			.map(x -> x.get(0))
			.map(HttpResponseState::getUrl)
			.orElse(config.callerConfig.url.template);

		long errorCnt = StreamEx.of(callsWithErrors)
			.mapToLong(HttpResponseState::getErrorRecords)
			.sum();

		if (dataSink != null) { // not initialized during a probe
			sendMetric(calledUrl, Optional.empty(), sentRecordsTotal.get(), sentBytesTotal.get(), errorCnt);
		}
		if(errorCnt > 0) {
			publishMonitoringLog(
					String.format("Failed to send %d messages to url %s", errorCnt, StringUtils.abbreviate(calledUrl, 64)),
					NexlaMonitoringLogType.LOG,
					NexlaMonitoringLogSeverity.ERROR);

			return callsWithErrors;
		}
		
		return results;
	}

	private List<HttpResponseState> doCalls(int streamSize, RestSinkCaller caller, StreamEx<NexlaMessageContext> messages, boolean isProbe) {
		if (config.batchMode) {
			return config.batchGroupKey
				.map(groupKey -> groupedBatch(streamSize, caller, messages, groupKey, isProbe))
				.orElseGet(() -> singletonList(executeBatch(emptyMap(), caller, messages, streamSize, isProbe)));
		} else {
			return messages
				.parallel(executorService)
				.map(m -> doSingleCall(caller, m))
				.collect(toList());
		}
	}

	private List<HttpResponseState> groupedBatch(int streamSize, RestSinkCaller caller, StreamEx<NexlaMessageContext> messages, String groupKey, boolean isProbe) {
		Map<String, List<NexlaMessageContext>> grouped = messages
			.groupingBy(x -> x.original.getRawMessage().get(groupKey).toString());
		return EntryStream.of(grouped)
			.mapValues(x -> removeGroupKey(groupKey, x))
			.parallel(executorService)
			.map(m -> executeBatch(lhm(groupKey, m.getKey()), caller, StreamEx.of(m.getValue()), streamSize, isProbe))
			.collect(toList());
	}

	private StreamEx<NexlaMessageContext> removeGroupKey(String groupKey, List<NexlaMessageContext> x) {
		return StreamEx.of(x).map(y -> {
			LinkedHashMap<String, Object> newLhm = new LinkedHashMap<>(y.mapped.getRawMessage());
			newLhm.remove(groupKey);
			NexlaMessage mapped = new NexlaMessage(newLhm, y.mapped.getNexlaMetaData());
			return new NexlaMessageContext(y.original, mapped, y.topicPartition, y.kafkaOffset);
		});
	}

	public RestSinkCaller createSinkCaller() {

		Optional<RestIngestionCallback> ingester = config.callerConfig.ingestUrl
			.map(url -> new RestIngestionCallback(url, ingestSenderPool.get()));

		return new RestSinkCaller(
			senderPool,
			ingester,
			config.callerConfig,
			ParserConfigs.DEFAULT_CHARSET_DETECT_THRESHOLD,
			logger,
			config.logVerbose && config.logSinkBody,
			config.logVerbose);
	}

	@SneakyThrows
	private HttpResponseState doSingleCall(RestSinkCaller caller, NexlaMessageContext m) {
		HttpResponseState state = new HttpResponseState();

		Map<String, String> flattened = Maps.newHashMap();

		EntryStream.of(Flattener.INSTANCE.flatten(replacementMap(m)))
			.mapValues(v -> v == null ? null : v.toString())
			.forEach(k -> flattened.put(k.getKey(), k.getValue()));

		flattened.putAll(getSubstitutionDates(config.dateTimeUnit, config.dateFormat, config.callerConfig.url.variables, nowUTC()));
		flattened.putAll(new CredentialSubstitutionMapper().map(this.config.authConfig));
		String url = replaceVars(caller.config.url, flattened);

		Map<String, String> requestHeaders = Maps.newHashMap();

		EntryStream
			.of(caller.config.requestHeaders)
			.mapValues(x -> replaceVars(x, flattened))
			.forEach(k -> requestHeaders.put(k.getKey(), k.getValue()));

		RestHeaders headers = new RestHeaders(
			requestHeaders,
			caller.config.contentType,
			caller.config.acceptHeader);

		SingleRequestReadyIngestion request = caller.createSingleDataReadyIngestion(url, maybeIgnoreNullsEmpty(m.mapped.getRawMessage()), m.original, caller.getConfig().sinkMode, headers);
		try {
			caller.makeSingleCallWithIngestion(request, state);
		} catch (Exception e) {
			Throwable cause = e.getCause();
			if (cause instanceof SenderHttpException) {
				state.setResponse(Optional.of(((SenderHttpException) cause).getResponseBodyAsString()));
			}
			state.setErrorRecords(1);
			state.getErrorList().add(new HttpResponseErrorState(e, m.original));
		}
		return state;
	}

	private Map<String, String> replacementMap(NexlaMessageContext m) {
		Map<String, String> replacementMap = Maps.newHashMap();
		Flattener.INSTANCE.flatten(maybeIgnoreNullsEmpty(m.mapped.getRawMessage()))
			.forEach((k, v) -> replacementMap.put(k, v == null ? null : v.toString()));
		Flattener.INSTANCE.flatten(maybeIgnoreNullsEmpty(m.original.getRawMessage()))
			.forEach((k, v) -> replacementMap.put(k, v == null ? null : v.toString()));
		return replacementMap;
	}

	private void addHeadersToMeta(NexlaMessageContext m, SingleRequestReadyIngestion request, Exception e) {
		Map<String, Object> tagsMap = ofNullable(m.mapped.getNexlaMetaData().getTags())
			.orElse(Maps.newHashMap());
		tagsMap.put(NX_REQUEST_URL, request.url);
		if (config.callerConfig.addHeadersToMeta && e instanceof RestClientResponseException) {
			tagsMap.put(NX_RESPONSE_HEADERS, ((RestClientResponseException) e).getResponseHeaders());
		}
		m.mapped.getNexlaMetaData().setTags(tagsMap);
	}

	private Map<String, Object> replacementMap(LinkedHashMap<String, Object> mapped, LinkedHashMap<String, Object> original) {
		LinkedHashMap<String, Object> resultMap = Maps.newLinkedHashMap(maybeIgnoreNullsEmpty(original));
		resultMap.putAll(maybeIgnoreNullsEmpty(mapped));
		return resultMap;
	}

	private LinkedHashMap<String, Object> maybeIgnoreNullsEmpty(LinkedHashMap<String, Object> message) {
		return filterMessage.map(f -> f.filter(message)).orElse(message);
	}

	private HttpResponseState executeBatch(Map<String, String> groupKey, RestSinkCaller caller, StreamEx<NexlaMessageContext> messages, long nRecords, boolean isProbe) {
		HttpResponseState state = new HttpResponseState();
		List<NexlaMessageContext> messageList = messages.toList();
		try {
			putBatchMode(groupKey, caller, messageList, state, isProbe);
		} catch (Throwable e) {
			state.setErrorRecords(nRecords);
			state.getErrorList().addAll(messageList.stream()
				.map(m -> new HttpResponseErrorState(e, m.original)).collect(toList()));
		}
		return state;
	}

	private void logException(Throwable e, String url) {
		NexlaError error = getErrorDetails(e, empty());
		logger.error(String.format("Failed to send message for url=%s: %s",
				StringUtils.abbreviate(url, 64), error.getMessage()), error.getException());

		if (config.logVerbose) {
			logger.error(String.format("Error details: url=%s \nresponseBody=%s",
					url, error.getResponseBody()), e);
		}
	}

	private long putBatchMode(
		Map<String, String> groupKey,
		RestSinkCaller caller,
		List<NexlaMessageContext> messageList,
		HttpResponseState varState,
		boolean isProbe
	) {
		Map<String, String> flattened = Maps.newHashMap();
		flattened.putAll(getSubstitutionDates(config.dateTimeUnit, config.dateFormat, config.callerConfig.url.variables, nowUTC()));
		flattened.putAll(new CredentialSubstitutionMapper().map(this.config.authConfig));
		flattened.putAll(groupKey);
		String url = replaceVars(config.callerConfig.url, flattened);

		varState.setUrl(url);

		// to compile it once and reuse
		Optional<BodyEval> bodyMaker = config.bodyTransformFunction
			.map(bodyFn -> new BodyEval(bodyFn, restTemplate, scriptEvalClient));

		Optional<String> body = of(
				bodyMaker.orElseThrow(() -> new ConfigException("Configuration is not supported"))
					.makeBody(logger, StreamEx.of(messageList)
						.map(m -> m.mapped)
						.peek(message -> byteCounter.addAndGet(calcBytes(message.toJsonString())))
						.map(NexlaMessage::getRawMessage)
						.toList(),
						isProbe
					)
		);

		Map<String, String> requestHeaders = Maps.newHashMap();

		EntryStream
				.of(caller.config.requestHeaders)
				.mapValues(x -> replaceVars(x, flattened))
				.forEach(k -> requestHeaders.put(k.getKey(), k.getValue()));

		RestHeaders restHeaders = new RestHeaders(requestHeaders, caller.config.contentType, caller.config.acceptHeader);

		BatchRequestReadyIngestion batchRequestReadyIngestion = caller.createBatchDataReadyIngestion(messageList,
			url, body, restHeaders);
		caller.makeBatchCallWithIngestion(batchRequestReadyIngestion, varState);

		return varState.getSentBytes();
	}

	@SneakyThrows
	@Override
	public void stop() {
		if (executorService != null) {
			executorService.shutdown();
		}
		if (probeService != null) {
			probeService.close();
		}
		super.stop();
	}

}
