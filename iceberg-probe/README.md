# Iceberg Connector

Iceberg is an open table format that brings correctness, atomic transactions, and improved performance to large analytic tables. The open spec makes it possible for various compute engines like Spark, Trino, Flink, and Hive to interact with the same tables, decoupling the compute engine from the data storage. Iceberg tables can be stored in any file system and official support will vary by compute engine and vendor. Popular integrations are with Amazon S3, HDFS, Snowflake, and Azure Data Lake. 

This module houses the Nexla Iceberg probe to support the Iceberg table format as a source and sink. This is a library responsible for providing authentication, SparkSessions, and sample data.

## Features
- [x] Support for AWS access key authentication
- [x] Support for AWS IAM authentication
- [x] Static time travel features (read from snapshot/timestamp/branch/tag)
- [ ] Dynamic time travel features (incremental read mode)
- [ ] Support for S3TablesCatalog
- [ ] Support GlueCatalog
- [ ] Support DynamoDB Lock Manager
- [ ] Support JDBC catalog
- [ ] Support for custom KMS keys

## Constraints and Scope
- Uses a hadoop catalog to write to ad-hoc table at locations in S3. The hadoop catalog is not recommended for production use cases, and it will be important to support catalogs in Hive Metastore, RDS (JDBC), DynamoDB, and AWS Glue.
- The use of the hadoop catalog means table discovery is purely based on file structure and depends on setting the correct bucket and bucket prefix in the configuration. Once more robust catalogs are supported the connector should be able to discover all tables in the catalog.

## Running Locally

A unit test suite is included that executes against the localstack S3 image. It's possible to run awscli commands against the localstack image at test execution time with a little setup.

The first requirement is to setup your localstack credentials file in `~/.aws/credentials`, the credentials for localstack are:

```ini
[default]
aws_access_key_id = accesskey
aws_secret_access_key = secretkey
```
and the default localstack region is `us-east-1`.

Now, you can simply set a breakpoint and start the test execution. With the localstack container running you can execute awscli commands against it by providing the URL endpoint. The host port number is randomly assigned and can be found in the test log, via docker desktop, or `$ docker ps`

For example, after the test writes the Iceberg tables to the container, you can see the metadata contents and data parquet files with these commands.
```
$ aws s3 ls s3://nexla-data-lake/db/table/metadata/ --endpoint-url http://localhost:<localstack-s3-port>
$ aws s3 ls s3://nexla-data-lake/db/table/data/ --endpoint-url http://localhost:<localstack-s3-port>
```

Further, you can use a spark-sql shell to interact with the tables in the container, it requires quite a bit of configuration which is included below:

```zsh
$ spark-sql --packages org.apache.iceberg:iceberg-spark-runtime-3.4_2.12:1.5.2,org.apache.iceberg:iceberg-aws-bundle:1.4.1,org.apache.iceberg:iceberg-spark-extensions-3.4_2.12:1.5.2,org.apache.hadoop:hadoop-aws:2.10.2 \
--conf spark.sql.extensions=org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions \
--conf spark.sql.catalog.spark_catalog=org.apache.iceberg.spark.SparkCatalog \
--conf spark.sql.catalog.spark_catalog.warehouse=s3a://nexla-data-lake \
--conf spark.sql.catalog.spark_catalog.io-impl=org.apache.iceberg.aws.s3.S3FileIO \
--conf spark.sql.catalog.spark_catalog.s3.access-key-id=accesskey \
--conf spark.sql.catalog.spark_catalog.s3.secret-access-key=secretkey \
--conf spark.sql.catalog.spark_catalog.s3.endpoint=http://127.0.0.1:<localstack-s3-port> \
--conf spark.sql.catalog.spark_catalog.type=hadoop \
--conf spark.hadoop.fs.s3a.endpoint=http://127.0.0.1:<localstack-s3-port> \
--conf spark.hadoop.fs.s3a.access.key=accesskey \
--conf spark.hadoop.fs.s3a.secret.key=secretkey \
--conf spark.hadoop.fs.s3a.endpoint.region=us-east-1 \
--conf spark.hadoop.fs.s3a.buffer.dir=/tmp \
--conf spark.hadoop.fs.s3a.aws.credentials.provider=org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider
```

```
spark-sql (default)> select * from db.table;
23/11/27 01:33:58 WARN ObjectStore: Failed to get database global_temp, returning NoSuchObjectException
Death Knight    Knights of the Frozen Throne    2023-01-01      80.0    Arthas
Mage    Kirin Tor       2023-01-02      75.0    Jaina
Shaman  NULL    NULL    78.0    Thrall
NULL    Banshee's Wail  2023-01-03      60.0    Sylvanas
Demon Hunter    The Betrayer    2023-01-04      NULL    Illidan
Warlock Shadow Council  2023-01-01      75.0    Guldan
Druid   NULL    2023-01-02      NULL    Malfurion
Warrior Horde Chieftains        NULL    80.0    Saurfang
NULL    Night Warriors  2023-01-03      60.0    Tyrande
Priest  The Draenei     2023-01-04      70.0    Velen
Priest  Stormwind Elite 2023-01-01      78.0    Anduin
NULL    Bloodhoof Tribe NULL    NULL    Baine
Hunter  NULL    2023-01-04      80.0    Alleria
Warrior Gilneas Liberation Front        2023-01-05      72.0    Genn
Death Knight    Knights of the Frozen Throne    2023-11-20      81.0    Arthas
Time taken: 4.724 seconds, Fetched 15 row(s)
spark-sql (default)> %
```
