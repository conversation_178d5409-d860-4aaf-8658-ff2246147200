package com.nexla.connect.common.connector.schema;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.NexlaMessage;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.connect.common.SchemaDetectionResult;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.kafka.service.TopicMetaService;
import com.nexla.transform.JsonCompareResult;
import com.nexla.transform.TransformService;
import com.nexla.transform.TransformServiceImpl;
import lombok.Getter;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.TopicConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.nexla.common.NexlaNamingUtils.nameDataSetTopic;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

@Getter
public class SchemaDetectionUtils {

	private static final Logger logger = LoggerFactory.getLogger(SchemaDetectionUtils.class);

	public static final int MAX_SAMPLES = 20;

	private final DataSetService dataSetService;

	private final int sourceId;
	private final int partitions;
	private final int replication;
	private final long retentionTime;
	private final boolean createTopics;
    private final Long maxMessageBytes;

	private final TransformService transformService;
	private final Optional<TopicMetaService> topicMetaService;

	private final Map<String, Integer> streamNameToDatasetId = new HashMap<>();

	public SchemaDetectionUtils(
		int sourceId,
		AdminApiClient adminApiClient,
		NexlaMessageProducer messageProducer,
		Optional<TopicMetaService> topicMetaService,
		int partitions,
		int replication,
		long retentionTime,
		boolean createTopics
	) {
        this(sourceId, adminApiClient, messageProducer, topicMetaService, partitions, replication, null, retentionTime, createTopics);
    }

	public SchemaDetectionUtils(
		int sourceId,
		AdminApiClient adminApiClient,
		NexlaMessageProducer messageProducer,
		Optional<TopicMetaService> topicMetaService,
		int partitions,
		int replication,
		Long maxMessageBytes,
		long retentionTime,
		boolean createTopics
	) {
		this.sourceId = sourceId;
		this.partitions = partitions;
		this.replication = replication;
		this.retentionTime = retentionTime;
		this.createTopics = createTopics;

		this.transformService = new TransformServiceImpl();
		this.topicMetaService = topicMetaService;

        this.maxMessageBytes = maxMessageBytes;

		List<DataSet> currentDatasets = adminApiClient.getAllDataSetsForSourceWithSamples(sourceId);

		currentDatasets.forEach(ds -> streamNameToDatasetId.put(ds.getName(), ds.getId()));

		if (createTopics) {
			currentDatasets.forEach(dataset -> createTopic(dataset.getId()));
		}

		ConcurrentHashMap<Integer, Integer> schemaToDataSetId = new ConcurrentHashMap<>();
		StreamEx.of(currentDatasets).forEach(ds -> schemaToDataSetId.put(ds.getSourceSchema().getSchemaId(), ds.getId()));

		ConcurrentHashMap<Integer, List<NexlaMessage>> samples = new ConcurrentHashMap<>(
			currentDatasets.stream().collect(toMap(DataSet::getId, DataSet::getDataSamples)));

		Set<NexlaSchema> schemas = currentDatasets.stream()
			.map(DataSet::getSourceSchema)
			.collect(toSet());

		this.dataSetService = new DataSetService(
			adminApiClient,
			messageProducer,
			transformService,
			schemaToDataSetId,
			samples,
			schemas,
			MAX_SAMPLES);
	}

	public Optional<List<NexlaMessage>> getSamples(int dataSetId) {
		return dataSetService.getLocalSamples(dataSetId);
	}

	public synchronized SchemaDetectionResult updateOrCreateDataSet(
		List<LinkedHashMap<String, Object>> messages,
		Optional<NexlaSchema> resolvedSchema
	) {
		return updateOrCreateDataSet(messages, null, resolvedSchema);
	}

	public synchronized SchemaDetectionResult updateOrCreateDataSet(
		List<LinkedHashMap<String, Object>> messages,
		String name,
		Optional<NexlaSchema> resolvedSchema
	) {
		return updateOrCreateDataSet(messages, name, resolvedSchema, false);
	}

	public synchronized SchemaDetectionResult updateOrCreateDataSet(
		List<LinkedHashMap<String, Object>> messages,
		String name,
		Optional<NexlaSchema> resolvedSchema,
		boolean isMultiSchema
	) {
		if (messages.isEmpty()) {
			logger.warn("[source-{}] Message list for schema detection is empty.", sourceId);
		}

		long emptyMessageCount = messages.stream().filter(Map::isEmpty).count();
		if (emptyMessageCount != 0) {
			logger.warn("[source-{}] Empty messages detected in message list for schema detection. Size: {}, Nr of empty messages: {}", sourceId, messages.size(), emptyMessageCount);
		}

		NexlaSchema schema = resolvedSchema.orElseGet(() -> dataSetService.getSchema(messages));
		Integer schemaId = schema.getSchemaId();
		List<NexlaMessage> nexlaMessages = StreamEx.of(messages)
			.map(NexlaMessage::new)
			.toList();

		Optional<Integer> datasetIdOpt = dataSetService.getDatasetId(schemaId);

		if (isMultiSchema && (name != null) && streamNameToDatasetId.containsKey(name)) { // Dataset already exists, just add samples to it
			int datasetId = streamNameToDatasetId.get(name);
			return new SchemaDetectionResult(datasetId, nameDataSetTopic(datasetId), c -> addSamplesToExisting(c, datasetId));
		} else if (!isMultiSchema && datasetIdOpt.isPresent()) { // Dataset already exists, just add samples to it
			int datasetId = datasetIdOpt.get();
			return new SchemaDetectionResult(datasetId, nameDataSetTopic(datasetId), c -> addSamplesToExisting(c, datasetId));
		} else { // Dataset does not exist
			return dataSetService.tryUpdateDataSet(nexlaMessages, schema)
				.orElseGet(() -> {
					// Create dataset
					SchemaDetectionResult result = dataSetService.createDataset(nexlaMessages, sourceId, schema, name);
					if (createTopics && result.dataSetId != -1) {
						createTopic(result.dataSetId);
					}
					streamNameToDatasetId.put(name, result.dataSetId);
					return result;
				});
		}
	}

	public int addSamplesToExisting(List<NexlaMessage> messages, int datasetId) {
		if (dataSetService.addToInMemorySamples(datasetId, messages)) {
			dataSetService.addToRemoteSamples(datasetId, messages);
			logger.info("Dataset {} already exists, added samples to in-memory and remote", datasetId);
		}
		return datasetId;
	}

	private void createTopic(int datasetId) {
		topicMetaService.ifPresent(topicService -> topicService.createTopic(nameDataSetTopic(datasetId), partitions, replication, getTopicProperties()));
	}

	public static SchemaDetectionUtils createSchemaDetection(
		SourceConnectorConfig config,
		AdminApiClient adminApiClient,
		NexlaMessageProducer messageProducer,
		Optional<TopicMetaService> topicMetaService
	) {
		return new SchemaDetectionUtils(
			config.sourceId, adminApiClient, messageProducer, topicMetaService,
			config.datasetPartitions, config.datasetReplication, config.maxRecordSize, config.topicRetentionTime,
			config.createTopics);
	}

	private Map<String, String> getTopicProperties() {
        Map<String, String> map = new HashMap<>(Map.of(
                TopicConfig.RETENTION_MS_CONFIG, retentionTime + ""
        ));

        if (this.maxMessageBytes != null && this.maxMessageBytes > 0) {
            map.put(TopicConfig.MAX_MESSAGE_BYTES_CONFIG, this.maxMessageBytes.toString());
        }

        return map;
	}

	public void setTryCombineSingleSchema() {
		dataSetService.setTryCombineSingleSchema();
	}

	public int getNumSchemas() {
		return dataSetService.getNumSchemas();
	}

	public boolean hasDataSet(int datasetId) {
		return dataSetService.getSchemaId(datasetId).isPresent();
	}

	public Optional<Integer> getSchemaId(int dataSetId) {
		return dataSetService.getSchemaId(dataSetId);
	}

	public void addLocalSamples(List<NexlaMessage> messages, int datasetId) {
		dataSetService.addToInMemorySamples(datasetId, messages);
		logger.info("Added {} in-memory samples to dataset {}", messages.size(), datasetId);
	}

	public JsonCompareResult compareSchemas(NexlaSchema newSchema, NexlaSchema oldSchema) {
		return dataSetService.compareSchemas(newSchema, oldSchema);
	}

	public NexlaSchema computeMergedSchema(List<LinkedHashMap<String, Object>> newMessages, Optional<NexlaSchema> maybeOldSchema) {
		NexlaSchema newSchema = dataSetService.getSchema(newMessages);
		return computeMergedSchema(newSchema, maybeOldSchema);
	}

	public NexlaSchema computeMergedSchema(NexlaSchema newSchema, Optional<NexlaSchema> maybeOldSchema) {
		if (maybeOldSchema.isEmpty()) {
			return newSchema;
		} else {
			NexlaSchema oldSchema = maybeOldSchema.get();
			return dataSetService.mergeSchemas(newSchema, oldSchema);
		}
	}

	public SchemaDetectionResult createDatasetWithEmptySchema() {
		NexlaSchema emptySchema = dataSetService.getSchema(List.of(new HashMap<>()));
		return dataSetService.createDataset(Collections.emptyList(), sourceId, emptySchema, null);
	}

	public Optional<NexlaSchema> getSchemaForDataset(int dataSetId) {
		return dataSetService.getSchemaForDataset(dataSetId);
	}

}