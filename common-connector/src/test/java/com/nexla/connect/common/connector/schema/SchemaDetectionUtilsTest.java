package com.nexla.connect.common.connector.schema;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.client.FindOrCreateDataSetResult;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.notify.context.NexlaNotificationEventContext;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.kafka.service.TopicMetaService;
import com.nexla.test.UnitTests;
import com.nexla.transform.StaticServices;
import com.nexla.transform.TransformServiceImpl;
import com.nexla.transform.cache.DataMapService;
import com.nexla.transform.schema.FormatDetector;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;

import static com.nexla.admin.client.FindOrCreateDataSetResult.Result.CREATED;
import static com.nexla.connect.common.connector.schema.SchemaDetectionUtils.MAX_SAMPLES;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static java.util.Optional.empty;
import static java.util.stream.Collectors.joining;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

@Category(UnitTests.class)
public class SchemaDetectionUtilsTest {

	static {
		FormatDetector.initDefault();
		StaticServices.setDataMapService(mock(DataMapService.class));
	}

	private static final TypeReference<Map<String, Object>> MAP_TYPE_REFERENCE = new TypeReference<Map<String, Object>>() {
	};
	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
	private AdminApiClient adminAPIUtils;
	private NexlaMessageProducer notificationCreateService;
	private TopicMetaService topicMetaService;
	private SchemaDetectionUtils schemaDetectionUtils;
	private DataSetService dataSetService;

	private final int DETECTED_SCHEMA_ID = 1421072087;
	private final int ANYOF_SCHEMA_ID = 738805422;
	private final String SUPERSET_SCHEMA = getFileAsString("superset_schema");
	private final String SUBSET_SCHEMA = getFileAsString("subset_schema");
	private final String DETECTED_SCHEMA = getFileAsString("detected_schema");
	private final String ANYOF_SCHEMA = getFileAsString("anyOf_schema");
	private final String SCHEMA = "http://json-schema.org/draft-04/schema#";
	private final String EXPECTED_CONTEXT = getFileAsString("expected_context");
	private final String OBJECT = "object";

	@SneakyThrows
	@Before
	public void onBefore() {
		this.adminAPIUtils = mock(AdminApiClient.class);
		this.notificationCreateService = mock(NexlaMessageProducer.class);
		this.topicMetaService = mock(TopicMetaService.class);
		this.schemaDetectionUtils = new SchemaDetectionUtils(
			10, adminAPIUtils, notificationCreateService,
			Optional.ofNullable(topicMetaService), 1, 1, 10, false);

		this.dataSetService = new DataSetService(adminAPIUtils, notificationCreateService, new TransformServiceImpl(),
			new ConcurrentHashMap<>(), new ConcurrentHashMap<>(), new HashSet<>(), MAX_SAMPLES);
	}

	@SneakyThrows
	@Test
	public void getSchemaTest() {
		Map<String, Object> message1 = Maps.newLinkedHashMap();
		message1.put("a", 10);
		Map<String, Object> message2 = Maps.newLinkedHashMap();
		message1.put("b", false);
		List<Map> messages = asList(message1, message2);

		NexlaSchema expectedSchema = new NexlaSchema();
		expectedSchema.setProperties(OBJECT_MAPPER.readValue(DETECTED_SCHEMA, MAP_TYPE_REFERENCE));
		expectedSchema.setSchema(SCHEMA);
		expectedSchema.setType(OBJECT);
		expectedSchema.setSchemaId(DETECTED_SCHEMA_ID);

		NexlaSchema actualSchema = dataSetService.getSchema(messages);

		assertEquals(expectedSchema, actualSchema);
	}

	@SneakyThrows
	@Test
	public void getAccumulatedSchemaTest() {
		Map<String, Object> message1 = Maps.newLinkedHashMap();
		message1.put("a", 10);
		Map<String, Object> message2 = Maps.newLinkedHashMap();
		message2.put("b", false);
		List<Map> messages = Arrays.asList(message1, message2);
		NexlaSchema oldSchema = dataSetService.getSchema(messages);

		message1.put("a", "15");
		message2.put("b", "test");
		messages = asList(message1, message2);

		NexlaSchema newSchema = dataSetService.getSchema(messages, oldSchema);

		NexlaSchema expectedSchema = new NexlaSchema();
		expectedSchema.setProperties(OBJECT_MAPPER.readValue(ANYOF_SCHEMA, MAP_TYPE_REFERENCE));
		expectedSchema.setSchema(SCHEMA);
		expectedSchema.setType(OBJECT);
		expectedSchema.setSchemaId(ANYOF_SCHEMA_ID);

		assertEquals(expectedSchema, newSchema);
	}

	@Test
	public void addAndGetSamples_appendSamplesTest() {
		LinkedHashMap<String, Object> message1 = Maps.newLinkedHashMap();
		message1.put("a", "test2");
		List<LinkedHashMap<String, Object>> messages1 = singletonList(message1);

		LinkedHashMap<String, Object> message2 = Maps.newLinkedHashMap();
		message2.put("a", "test2");
		List<LinkedHashMap<String, Object>> messages2 = singletonList(message2);
		DataSource dataSource = new DataSource();
		DataSet dataSet = new DataSet();
		dataSet.setId(1);
		dataSource.setDatasets(singletonList(dataSet));
		doReturn(Optional.of(dataSource)).when(adminAPIUtils).getDataSource(any());
		doReturn(new FindOrCreateDataSetResult(CREATED, 1)).when(adminAPIUtils).findOrCreateDataSet(any());
		schemaDetectionUtils.updateOrCreateDataSet(messages1, empty());
		try {
			schemaDetectionUtils.updateOrCreateDataSet(messages2, empty());
		} catch (Exception e) {
			fail("Exception was thrown");
		}
	}

	@SneakyThrows
	@Test
	public void getNotificationContextTest() {
		Map<String, Object> oldSchemaMap = OBJECT_MAPPER.readValue(SUBSET_SCHEMA, MAP_TYPE_REFERENCE);
		Map<String, Object> newSchemaMap = OBJECT_MAPPER.readValue(SUPERSET_SCHEMA, MAP_TYPE_REFERENCE);

		NexlaSchema newSchema = new NexlaSchema();
		newSchema.setProperties(newSchemaMap);
		NexlaSchema oldSchema = new NexlaSchema();
		oldSchema.setProperties(oldSchemaMap);

		NexlaNotificationEventContext context = dataSetService.createNotificationContext(newSchema, oldSchema);
		assertEquals(EXPECTED_CONTEXT,
				Stream.of(context.getEntriesOnlyOnRight(), context.getEntriesDiffering())
				.map(Object::toString)
				.collect(joining("\n")));
	}

	@SneakyThrows
	private String getFileAsString(String fileName) {
		return new BufferedReader(
			new InputStreamReader(
				this.getClass().getClassLoader().getResourceAsStream(fileName))).lines().collect(joining("\n"));
	}
}